{"name": "college-ai-assistant", "version": "1.0.0", "description": "面向高校师生的综合性AI学习助手平台", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "build": "npm run build:frontend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "start": "concurrently \"npm run start:frontend\" \"npm run start:backend\"", "start:frontend": "cd frontend && npm run preview", "start:backend": "cd backend && npm start", "lint": "npm run lint:frontend && npm run lint:backend", "lint:frontend": "cd frontend && npm run lint", "lint:backend": "cd backend && npm run lint", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "cd frontend && npm run test", "test:backend": "cd backend && npm run test"}, "keywords": ["AI", "education", "college", "assistant", "learning", "academic", "react", "typescript", "nodejs"], "author": "College AI Team", "license": "MIT", "devDependencies": {"@playwright/test": "^1.40.0", "concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/your-org/college-ai-assistant.git"}, "bugs": {"url": "https://github.com/your-org/college-ai-assistant/issues"}, "homepage": "https://github.com/your-org/college-ai-assistant#readme", "dependencies": {"immer": "^10.1.1", "zustand": "^5.0.5"}}