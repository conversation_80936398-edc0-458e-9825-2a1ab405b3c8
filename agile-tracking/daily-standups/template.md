# 📅 每日站会记录模板

## 基本信息
- **日期**: YYYY-MM-DD
- **时间**: 09:00-09:15
- **Sprint**: Sprint N (第N周)
- **参与者**: 后滩萧亚轩 (Product Owner & Developer)

---

## 🎯 三个核心问题

### 1. 昨天完成了什么？
**基于Riper5循环执行结果**:
- [ ] **任务**: [任务编号] - [任务名称]
  - 🔄 Riper5循环状态: [研究✅] → [创新✅] → [计划✅] → [执行✅] → [检查✅]
  - 📊 完成质量评分: ⭐⭐⭐⭐⭐ (5/5)
  - 💡 关键成果: [具体完成内容]
  - ⏱️ 实际用时: X小时 (预估Y小时)

- [ ] **代码提交**: 
  - 提交次数: X次
  - 主要变更: [变更描述]
  - 代码行数: +X行, -Y行

### 2. 今天计划做什么？
**基于任务清单和优先级**:
- [ ] **主要任务**: [任务编号] - [任务名称]
  - 🎯 预期成果: [具体目标]
  - ⏱️ 预估时间: X小时
  - 🔄 Riper5循环规划:
    - 09:00-10:00 [模式：研究] - [研究内容]
    - 10:00-11:00 [模式：创新] - [创新方向]
    - 11:00-13:00 [模式：计划] - [计划制定]
    - 14:00-17:00 [模式：执行] - [执行内容]
    - 17:00-18:00 [模式：检查] - [检查标准]

- [ ] **次要任务**: [如有其他任务]

### 3. 遇到了什么障碍？
**需要解决的问题和风险**:
- 🚨 **技术障碍**: 
  - 问题描述: [具体问题]
  - 影响程度: [高/中/低]
  - 解决方案: [计划的解决方式]
  - 需要支持: [是否需要外部帮助]

- ⚠️ **时间风险**:
  - 风险描述: [时间相关风险]
  - 缓解措施: [应对计划]

- 💡 **学习需求**:
  - 技术学习: [需要学习的技术点]
  - 资源需求: [需要的学习资源]

---

## 📊 关键指标更新

### 昨日指标
- ✅ 任务完成率: X% (目标90%)
- 🕐 时间预估准确度: ±X% (目标±20%)
- 🧪 代码质量: [通过/需改进]
- 📈 进度状态: [正常/延迟/提前]

### Sprint进度
- 📊 Sprint进度: X% (X/Y任务完成)
- 🎯 Sprint目标达成预期: [乐观/正常/风险]
- ⏱️ 剩余时间: X天
- 🔥 燃尽图状态: [正常/需加速/超前]

---

## 🎯 今日重点关注

### 优先级排序
1. **P0 - 必须完成**: [最重要的任务]
2. **P1 - 计划完成**: [重要任务]
3. **P2 - 时间允许**: [次要任务]

### 风险监控
- 🟢 **正常**: [正常进行的方面]
- 🟡 **注意**: [需要关注的方面]
- 🔴 **风险**: [高风险项目]

---

## 📝 行动项 (Action Items)

- [ ] **行动项1**: [具体行动] - 负责人: 后滩萧亚轩 - 截止: [日期]
- [ ] **行动项2**: [具体行动] - 负责人: 后滩萧亚轩 - 截止: [日期]

---

## 💬 备注和想法
[记录其他想法、灵感或需要后续跟进的事项]

---

**📅 下次站会**: 明日09:00
**🔄 自动提醒**: 系统将在明日08:55自动提醒 