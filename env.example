# 应用配置
NODE_ENV=development
PORT=3001

# 数据库配置
MONGODB_URI=********************************************************************
REDIS_URL=redis://localhost:6379

# JWT配置
JWT_SECRET=your_very_secure_jwt_secret_key_here
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# AI服务配置
OPENAI_API_KEY=your_openai_api_key
OPENAI_API_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-4-turbo-preview
OPENAI_MAX_TOKENS=4096

# 文件上传配置
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=100MB
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document

# 邮件服务配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password
FROM_EMAIL=<EMAIL>

# 第三方服务配置
GOOGLE_SCHOLAR_API_KEY=your_google_scholar_api_key
BAIDU_ACADEMIC_API_KEY=your_baidu_academic_api_key

# 安全配置
BCRYPT_SALT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
CORS_ORIGIN=http://localhost:3000

# 日志配置
LOG_LEVEL=debug
LOG_DIR=./logs

# 监控配置
SENTRY_DSN=your_sentry_dsn_here

# Docker配置
MONGODB_ROOT_USERNAME=admin
MONGODB_ROOT_PASSWORD=password
REDIS_PASSWORD=

# 开发环境特殊配置
ENABLE_DEBUG=true
MOCK_AI_RESPONSES=false 