# AI搜题助手 - 代码规范文档 v2.0

## 1. 概述

本文档规定了AI搜题助手项目的代码规范，旨在保证代码质量，提升开发效率，确保项目可维护性。

### 1.1 适用范围
- 前端React + TypeScript代码
- 后端Node.js + Express + TypeScript代码
- 配置文件和脚本
- 文档和注释

### 1.2 规范原则
- **一致性**：团队成员使用统一的编码风格
- **可读性**：代码清晰易懂，自解释性强
- **可维护性**：模块化设计，便于扩展和修改
- **性能优先**：注重代码执行效率和用户体验

## 2. 文件组织规范

### 2.1 目录结构规范

#### 前端目录结构
```
frontend/src/
├── components/          # React组件
│   ├── SearchPage.tsx  # 搜题主页组件
│   └── SearchResult.tsx # 搜题结果组件
├── services/            # API服务层
│   └── api.ts          # HTTP请求封装
├── store/               # 状态管理
│   └── index.ts        # Zustand store
├── types/               # TypeScript类型定义
│   └── index.ts        # 公共类型定义
├── utils/               # 工具函数
│   └── helpers.ts      # 通用辅助函数
├── hooks/               # 自定义Hooks
│   └── useSearch.ts    # 搜题相关Hooks
├── App.tsx             # 根组件
├── main.tsx            # 应用入口
└── index.css           # 全局样式
```

#### 后端目录结构
```
backend/src/
├── controllers/         # 控制器层
│   └── searchController.ts
├── services/            # 业务逻辑层
│   ├── searchService.ts
│   └── aiService.ts
├── models/              # 数据模型
│   └── searchModels.ts
├── middleware/          # 中间件
│   ├── auth.ts
│   └── validation.ts
├── utils/               # 工具函数
│   └── helpers.ts
├── routes/              # 路由定义
│   └── api.ts
└── index.ts            # 服务入口
```

### 2.2 文件命名规范

#### 文件名规范
- **React组件**：PascalCase，如 `SearchPage.tsx`
- **非组件文件**：camelCase，如 `apiService.ts`
- **类型定义文件**：camelCase，如 `searchTypes.ts`
- **工具函数文件**：camelCase，如 `formatHelpers.ts`
- **测试文件**：`.test.ts` 或 `.spec.ts` 后缀

#### 目录名规范
- 全部使用小写字母
- 多词使用连字符，如 `search-results`
- 避免缩写，使用完整单词

### 2.3 文件大小限制
- **组件文件**：不超过300行
- **服务文件**：不超过500行
- **工具文件**：不超过200行
- **类型文件**：不超过100行

超过限制时必须进行代码重构和模块拆分。

## 3. TypeScript编码规范

### 3.1 类型定义规范

#### 接口定义
```typescript
// ✅ 好的示例
interface SearchQuestion {
  id: string;
  content: string;
  type: 'text' | 'image' | 'screenshot';
  subject: string;
  difficulty?: 'easy' | 'medium' | 'hard';
  createdAt: Date;
}

// ❌ 避免的写法
interface searchQuestion {  // 命名不规范
  id: any;                 // 避免使用any
  content: string;
  type: string;           // 应该使用联合类型
}
```

#### 类型别名
```typescript
// ✅ 好的示例
type SearchType = 'text' | 'image' | 'screenshot';
type ApiResponse<T = any> = {
  success: boolean;
  message: string;
  data?: T;
  error?: ApiError;
};

// ❌ 避免的写法
type searchType = string;  // 应该使用联合类型
```

#### 泛型使用
```typescript
// ✅ 好的示例
interface ApiService<T> {
  get<U = T>(url: string): Promise<ApiResponse<U>>;
  post<U = T>(url: string, data: any): Promise<ApiResponse<U>>;
}

// ✅ 有意义的泛型名称
interface SearchResult<TData = any, TMeta = any> {
  data: TData;
  metadata: TMeta;
  confidence: number;
}
```

### 3.2 变量命名规范

#### 基本命名规则
```typescript
// ✅ 变量和函数：camelCase
const searchQuery = 'mathematics equation';
const isLoading = false;
const userPreferences = {};

function searchByText(query: string): Promise<SearchResult> {
  // 函数实现
}

// ✅ 常量：SCREAMING_SNAKE_CASE
const API_BASE_URL = 'https://api.example.com';
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
const SEARCH_TIMEOUT = 5000;

// ✅ 类和接口：PascalCase
class SearchService {
  // 类实现
}

interface UserPreferences {
  theme: 'light' | 'dark';
  language: string;
}
```

#### 布尔值命名
```typescript
// ✅ 使用is/has/can/should等前缀
const isSearching = false;
const hasPermission = true;
const canEdit = false;
const shouldValidate = true;

// ❌ 避免的写法
const search = false;      // 不明确是否为布尔值
const permission = true;   // 应该使用hasPermission
```

#### 函数命名
```typescript
// ✅ 动词开头，描述具体动作
function searchByImage(file: File): Promise<SearchResult> {}
function validateSearchQuery(query: string): boolean {}
function formatSearchResults(results: SearchResult[]): FormattedResult[] {}

// ❌ 避免的写法
function image(file: File) {}        // 不明确动作
function query(q: string) {}         // 命名过于简单
function results(data: any) {}       // 不明确处理类型
```

### 3.3 函数和方法规范

#### 函数签名
```typescript
// ✅ 完整的类型注解
async function searchByText(
  query: string,
  options?: SearchOptions
): Promise<SearchResult> {
  // 函数实现
}

// ✅ 参数解构
function processSearchResult({
  question,
  answer,
  steps,
  confidence
}: {
  question: string;
  answer: string;
  steps: string[];
  confidence: number;
}): ProcessedResult {
  // 函数实现
}
```

#### 异步函数规范
```typescript
// ✅ 明确的错误处理
async function fetchSearchResult(query: string): Promise<SearchResult> {
  try {
    const response = await apiService.post('/search/text', { query });
    return response.data;
  } catch (error) {
    logger.error('Search failed:', error);
    throw new SearchError('搜题失败，请重试', error);
  }
}

// ✅ 超时控制
async function searchWithTimeout(
  query: string,
  timeout: number = SEARCH_TIMEOUT
): Promise<SearchResult> {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);
  
  try {
    const result = await fetch('/api/search', {
      signal: controller.signal,
      body: JSON.stringify({ query })
    });
    return await result.json();
  } finally {
    clearTimeout(timeoutId);
  }
}
```

## 4. React组件规范

### 4.1 组件设计原则

#### 单一职责原则
```typescript
// ✅ 单一职责：搜题输入组件
interface SearchInputProps {
  onSearch: (query: string) => void;
  placeholder?: string;
  disabled?: boolean;
}

function SearchInput({ onSearch, placeholder, disabled }: SearchInputProps) {
  const [query, setQuery] = useState('');
  
  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    if (query.trim()) {
      onSearch(query.trim());
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <input
        type="text"
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        placeholder={placeholder}
        disabled={disabled}
      />
      <button type="submit" disabled={disabled || !query.trim()}>
        搜索
      </button>
    </form>
  );
}
```

#### Props接口定义
```typescript
// ✅ 完整的Props类型定义
interface SearchResultProps {
  result: SearchResult;
  onRelatedQuestionClick?: (questionId: string) => void;
  onSave?: (result: SearchResult) => void;
  showActions?: boolean;
  className?: string;
  'data-testid'?: string;
}

// ✅ 默认值处理
function SearchResult({
  result,
  onRelatedQuestionClick = () => {},
  onSave,
  showActions = true,
  className = '',
  'data-testid': testId
}: SearchResultProps) {
  // 组件实现
}
```

### 4.2 Hooks使用规范

#### 自定义Hooks
```typescript
// ✅ 自定义Hook设计
interface UseSearchResult {
  searchResults: SearchResult[];
  isLoading: boolean;
  error: string | null;
  searchByText: (query: string) => Promise<void>;
  searchByImage: (file: File) => Promise<void>;
  clearResults: () => void;
}

function useSearch(): UseSearchResult {
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const searchByText = useCallback(async (query: string) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await apiService.searchByText(query);
      setSearchResults([result]);
    } catch (err) {
      setError(err instanceof Error ? err.message : '搜索失败');
    } finally {
      setIsLoading(false);
    }
  }, []);

  const searchByImage = useCallback(async (file: File) => {
    // 图片搜索实现
  }, []);

  const clearResults = useCallback(() => {
    setSearchResults([]);
    setError(null);
  }, []);
  
  return {
    searchResults,
    isLoading,
    error,
    searchByText,
    searchByImage,
    clearResults
  };
}
```

#### 性能优化
```typescript
// ✅ 使用React.memo优化渲染
const SearchResult = React.memo<SearchResultProps>(({
  result,
  onRelatedQuestionClick
}) => {
  return (
    <div className="search-result">
      {/* 组件内容 */}
    </div>
  );
});

SearchResult.displayName = 'SearchResult';

// ✅ 使用useCallback优化回调
function SearchPage() {
  const [query, setQuery] = useState('');
  
  const handleSearch = useCallback(async (searchQuery: string) => {
    // 搜索逻辑
  }, []);

  const handleInputChange = useCallback((value: string) => {
    setQuery(value);
  }, []);

  return (
    <SearchInput 
      onSearch={handleSearch}
      onChange={handleInputChange}
    />
  );
}
```

### 4.3 状态管理规范

#### Zustand Store设计
```typescript
// ✅ 清晰的状态结构
interface AppState {
  // 数据状态
  user: User | null;
  searchResults: SearchResult[];
  searchHistory: SearchHistory[];
  
 // UI状态
  isLoading: boolean;
  error: string | null;
  
  // 动作方法
  actions: {
    // 用户相关
    setUser: (user: User | null) => void;
    
    // 搜题相关
    searchByText: (query: string) => Promise<void>;
    searchByImage: (file: File) => Promise<void>;
    addToHistory: (search: SearchHistory) => void;
    
    // UI控制
    setLoading: (loading: boolean) => void;
    setError: (error: string | null) => void;
    clearError: () => void;
  };
}

// ✅ Store实现
const useAppStore = create<AppState>((set, get) => ({
  // 初始状态
  user: null,
  searchResults: [],
  searchHistory: [],
  isLoading: false,
  error: null,
  
  actions: {
    setUser: (user) => set({ user }),
    
    searchByText: async (query) => {
      set({ isLoading: true, error: null });
      try {
        const result = await apiService.searchByText(query);
        set({ 
          searchResults: [result],
          isLoading: false 
        });
        get().actions.addToHistory({
          id: Date.now().toString(),
          query,
          type: 'text',
          result,
          timestamp: new Date()
        });
      } catch (error) {
        set({ 
          error: error instanceof Error ? error.message : '搜索失败',
          isLoading: false 
        });
      }
    },
    
    addToHistory: (search) => set((state) => ({
      searchHistory: [search, ...state.searchHistory].slice(0, 50) // 保留最近50条
    })),
    
    setLoading: (isLoading) => set({ isLoading }),
    setError: (error) => set({ error }),
    clearError: () => set({ error: null })
  }
}));
```

## 5. 样式和UI规范

### 5.1 TailwindCSS使用规范

#### 类名组织
```typescript
// ✅ 合理的类名组织
function SearchButton({ onClick, disabled, children }: ButtonProps) {
  const baseClasses = [
    'px-6 py-3',
    'font-medium text-white',
    'bg-gradient-to-r from-blue-500 to-blue-600',
    'rounded-lg shadow-md',
    'transition-all duration-200',
    'hover:from-blue-600 hover:to-blue-700',
    'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
    'disabled:opacity-50 disabled:cursor-not-allowed'
  ];

  return (
    <button
      className={baseClasses.join(' ')}
      onClick={onClick}
      disabled={disabled}
    >
      {children}
    </button>
  );
}

// ✅ 使用工具函数组织样式
import clsx from 'clsx';

function SearchCard({ isHighlighted, className }: CardProps) {
  return (
    <div className={clsx(
      'p-6 bg-white rounded-lg shadow-sm border',
      'transition-all duration-200',
      isHighlighted && 'ring-2 ring-blue-500 shadow-md',
      className
    )}>
      {/* 卡片内容 */}
    </div>
  );
}
```

#### 响应式设计
```typescript
// ✅ 移动优先的响应式设计
function SearchLayout({ children }: LayoutProps) {
  return (
    <div className={clsx(
      'min-h-screen bg-gray-50',
      'px-4 py-6',           // 移动端间距
      'sm:px-6 sm:py-8',     // 小屏设备
      'lg:px-8 lg:py-12',    // 大屏设备
      'xl:px-12 xl:py-16'    // 超大屏设备
    )}>
      <div className={clsx(
        'max-w-md mx-auto',      // 移动端最大宽度
        'sm:max-w-lg',           // 小屏设备
        'lg:max-w-4xl',          // 大屏设备
        'xl:max-w-6xl'           // 超大屏设备
      )}>
        {children}
      </div>
    </div>
  );
}
```

### 5.2 动画和交互规范

#### Framer Motion使用
```typescript
// ✅ 统一的动画配置
const fadeInUp = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -20 },
  transition: { duration: 0.3, ease: 'easeOut' }
};

const scaleOnHover = {
  whileHover: { scale: 1.02 },
  whileTap: { scale: 0.98 }
};

// ✅ 组件中使用动画
function SearchResult({ result }: SearchResultProps) {
  return (
    <motion.div
      {...fadeInUp}
      className="bg-white rounded-lg shadow-sm p-6"
    >
      <motion.button
        {...scaleOnHover}
        className="w-full text-left"
      >
        {result.question}
      </motion.button>
    </motion.div>
  );
}
```

## 6. API和服务规范

### 6.1 HTTP请求规范

#### Axios配置
```typescript
// ✅ 统一的API服务配置
class ApiService {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: import.meta.env.VITE_API_BASE_URL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // 请求拦截器
    this.client.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('auth_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // 响应拦截器
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          // 处理认证失败
          localStorage.removeItem('auth_token');
          window.location.href = '/login';
        }
        return Promise.reject(this.handleError(error));
      }
    );
  }

  private handleError(error: AxiosError): ApiError {
    return {
      code: error.code || 'UNKNOWN_ERROR',
      message: error.message || '请求失败',
      details: error.response?.data
    };
  }

  // ✅ 具体的API方法
  async searchByText(query: string): Promise<SearchResult> {
    const response = await this.client.post<ApiResponse<SearchResult>>(
      '/api/v1/search/text',
      { query }
    );
    return response.data.data!;
  }

  async searchByImage(file: File): Promise<SearchResult> {
    const formData = new FormData();
    formData.append('image', file);
    
    const response = await this.client.post<ApiResponse<SearchResult>>(
      '/api/v1/search/image',
      formData,
      {
        headers: { 'Content-Type': 'multipart/form-data' }
      }
    );
    return response.data.data!;
  }
}
```

### 6.2 错误处理规范

#### 错误类型定义
```typescript
// ✅ 错误类型体系
interface ApiError {
  code: string;
  message: string;
  details?: any;
}

class SearchError extends Error {
  constructor(
    message: string,
    public code: string = 'SEARCH_ERROR',
    public details?: any
  ) {
    super(message);
    this.name = 'SearchError';
  }
}

class ValidationError extends Error {
  constructor(
    message: string,
    public field: string,
    public value: any
  ) {
    super(message);
    this.name = 'ValidationError';
  }
}
```

#### 错误处理实践
```typescript
// ✅ 组件中的错误处理
function SearchPage() {
  const [error, setError] = useState<string | null>(null);
  
  const handleSearch = async (query: string) => {
    try {
      setError(null);
      const result = await apiService.searchByText(query);
      // 处理成功结果
    } catch (error) {
      if (error instanceof SearchError) {
        setError(`搜索失败: ${error.message}`);
      } else if (error instanceof ValidationError) {
        setError(`输入错误: ${error.message}`);
      } else {
        setError('发生未知错误，请重试');
      }
      
      // 记录错误日志
      console.error('Search error:', error);
    }
  };

  return (
    <div>
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}
      {/* 其他组件内容 */}
    </div>
  );
}
```

## 7. 测试规范

### 7.1 单元测试规范

#### 测试文件组织
```typescript
// ✅ 测试文件示例: SearchInput.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { SearchInput } from '../SearchInput';

describe('SearchInput', () => {
  const mockOnSearch = jest.fn();

  beforeEach(() => {
    mockOnSearch.mockClear();
  });

  it('should render with placeholder text', () => {
    render(
      <SearchInput 
        onSearch={mockOnSearch} 
        placeholder="输入题目进行搜索"
      />
    );
    
    expect(screen.getByPlaceholderText('输入题目进行搜索')).toBeInTheDocument();
  });

  it('should call onSearch when form is submitted', async () => {
    const user = userEvent.setup();
    
    render(<SearchInput onSearch={mockOnSearch} />);
    
    const input = screen.getByRole('textbox');
    const button = screen.getByRole('button', { name: /搜索/i });
    
    await user.type(input, '求解方程 x² + 2x - 3 = 0');
    await user.click(button);
    
    expect(mockOnSearch).toHaveBeenCalledWith('求解方程 x² + 2x - 3 = 0');
  });

  it('should not submit empty query', async () => {
    const user = userEvent.setup();
    
    render(<SearchInput onSearch={mockOnSearch} />);
    
    const button = screen.getByRole('button', { name: /搜索/i });
    await user.click(button);
    
    expect(mockOnSearch).not.toHaveBeenCalled();
  });
});
```

#### 自定义Hook测试
```typescript
// ✅ Hook测试示例: useSearch.test.ts
import { renderHook, act } from '@testing-library/react';
import { useSearch } from '../useSearch';
import * as apiService from '../../services/api';

jest.mock('../../services/api');
const mockApiService = apiService as jest.Mocked<typeof apiService>;

describe('useSearch', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should initialize with empty state', () => {
    const { result } = renderHook(() => useSearch());
    
    expect(result.current.searchResults).toEqual([]);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBe(null);
  });

  it('should handle successful text search', async () => {
    const mockResult = {
      id: '1',
      question: 'test question',
      answer: 'test answer',
      steps: ['step 1', 'step 2']
    };
    
    mockApiService.searchByText.mockResolvedValue(mockResult);
    
    const { result } = renderHook(() => useSearch());
    
    await act(async () => {
      await result.current.searchByText('test query');
    });
    
    expect(result.current.searchResults).toEqual([mockResult]);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBe(null);
  });

  it('should handle search error', async () => {
    mockApiService.searchByText.mockRejectedValue(new Error('搜索失败'));
    
    const { result } = renderHook(() => useSearch());
    
    await act(async () => {
      await result.current.searchByText('test query');
    });
    
    expect(result.current.searchResults).toEqual([]);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBe('搜索失败');
  });
});
```

### 7.2 集成测试规范

#### E2E测试示例
```typescript
// ✅ Playwright E2E测试
import { test, expect } from '@playwright/test';

test.describe('AI搜题功能', () => {
  test('用户可以通过文字搜题', async ({ page }) => {
    await page.goto('/');
    
    // 验证页面加载
    await expect(page.locator('h1')).toContainText('AI搜题');
    
    // 输入搜题内容
    const searchInput = page.locator('input[placeholder*="输入题目"]');
    await searchInput.fill('求解方程 x² + 2x - 3 = 0');
    
    // 点击搜索按钮
    await page.locator('button:has-text("搜索")').click();
    
    // 等待结果加载
    await page.waitForSelector('[data-testid="search-result"]');
    
    // 验证搜索结果
    await expect(page.locator('[data-testid="search-result"]')).toBeVisible();
    await expect(page.locator('[data-testid="answer"]')).toContainText('x = 1, x = -3');
  });

  test('用户可以通过图片搜题', async ({ page }) => {
    await page.goto('/');
    
    // 点击图片搜题按钮
    await page.locator('button:has-text("图片搜题")').click();
    
    // 上传图片文件
    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles('tests/fixtures/math-equation.jpg');
    
    // 等待OCR识别完成
    await page.waitForSelector('[data-testid="ocr-result"]');
    
    // 验证识别结果
    await expect(page.locator('[data-testid="recognized-text"]')).toBeVisible();
    
    // 点击确认搜索
    await page.locator('button:has-text("确认搜索")').click();
    
    // 验证搜索结果
    await page.waitForSelector('[data-testid="search-result"]');
    await expect(page.locator('[data-testid="search-result"]')).toBeVisible();
  });
});
```

## 8. 性能优化规范

### 8.1 代码优化

#### 组件性能优化
```typescript
// ✅ 使用React.memo和useMemo优化
const SearchResultItem = React.memo<SearchResultItemProps>(({ 
  result, 
  onSave 
}) => {
  const formattedSteps = useMemo(() => {
    return result.steps.map((step, index) => ({
      number: index + 1,
      content: step,
      id: `step-${index}`
    }));
  }, [result.steps]);

  const handleSave = useCallback(() => {
    onSave(result);
  }, [result, onSave]);

  return (
    <div className="search-result-item">
      {formattedSteps.map(step => (
        <div key={step.id}>
          {step.number}. {step.content}
        </div>
      ))}
      <button onClick={handleSave}>保存</button>
    </div>
  );
});
```

#### 列表渲染优化
```typescript
// ✅ 虚拟滚动处理大列表
import { FixedSizeList as List } from 'react-window';

function SearchHistoryList({ items }: { items: SearchHistory[] }) {
  const Row = ({ index, style }: { index: number; style: CSSProperties }) => (
    <div style={style}>
      <SearchHistoryItem item={items[index]} />
    </div>
  );

  return (
    <List
      height={400}
      itemCount={items.length}
      itemSize={80}
      itemData={items}
    >
      {Row}
    </List>
  );
}
```

### 8.2 加载优化

#### 代码分割
```typescript
// ✅ 路由级代码分割
import { lazy, Suspense } from 'react';

const SearchPage = lazy(() => import('./components/SearchPage'));
const SearchResult = lazy(() => import('./components/SearchResult'));

function App() {
  return (
    <Suspense fallback={<div>加载中...</div>}>
      <Router>
        <Routes>
          <Route path="/" element={<SearchPage />} />
          <Route path="/result" element={<SearchResult />} />
        </Routes>
      </Router>
    </Suspense>
  );
}
```

#### 图片优化
```typescript
// ✅ 图片懒加载和优化
function LazyImage({ 
  src, 
  alt, 
  className 
}: {
  src: string;
  alt: string;
  className?: string;
}) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, []);

  return (
    <div ref={imgRef} className={className}>
      {isInView && (
        <img
          src={src}
          alt={alt}
          onLoad={() => setIsLoaded(true)}
          className={`transition-opacity duration-300 ${
            isLoaded ? 'opacity-100' : 'opacity-0'
          }`}
        />
      )}
    </div>
  );
}
```

## 9. 安全规范

### 9.1 输入验证
```typescript
// ✅ 严格的输入验证
function validateSearchQuery(query: string): { isValid: boolean; error?: string } {
  // 长度验证
  if (!query || query.trim().length === 0) {
    return { isValid: false, error: '搜索内容不能为空' };
  }
  
  if (query.length > 1000) {
    return { isValid: false, error: '搜索内容过长，请控制在1000字符以内' };
  }
  
  // XSS防护
  const hasScriptTags = /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi.test(query);
  if (hasScriptTags) {
    return { isValid: false, error: '输入内容包含不安全字符' };
  }
  
  return { isValid: true };
}

// ✅ 文件上传验证
function validateUploadFile(file: File): { isValid: boolean; error?: string } {
  const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
  const maxSize = 10 * 1024 * 1024; // 10MB
  
  if (!allowedTypes.includes(file.type)) {
    return { isValid: false, error: '仅支持JPG、PNG、GIF、WEBP格式的图片' };
  }
  
  if (file.size > maxSize) {
    return { isValid: false, error: '文件大小不能超过10MB' };
  }
  
  return { isValid: true };
}
```

### 9.2 数据安全
```typescript
// ✅ 敏感数据处理
function sanitizeSearchHistory(history: SearchHistory[]): SearchHistory[] {
  return history.map(item => ({
    ...item,
    // 移除敏感信息
    userId: undefined,
    userAgent: undefined,
    ipAddress: undefined
  }));
}

// ✅ Token管理
class AuthService {
  private static readonly TOKEN_KEY = 'auth_token';
  
  static setToken(token: string): void {
    localStorage.setItem(this.TOKEN_KEY, token);
  }
  
  static getToken(): string | null {
    return localStorage.getItem(this.TOKEN_KEY);
  }
  
  static removeToken(): void {
    localStorage.removeItem(this.TOKEN_KEY);
  }
  
  static isTokenExpired(token: string): boolean {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.exp * 1000 < Date.now();
    } catch {
      return true;
    }
  }
}
```

## 10. 日志和监控规范

### 10.1 前端日志
```typescript
// ✅ 统一的日志服务
class Logger {
  private static isDevelopment = import.meta.env.DEV;
  
  static info(message: string, data?: any): void {
    if (this.isDevelopment) {
      console.log(`[INFO] ${message}`, data);
    }
    this.sendToMonitoring('info', message, data);
  }
  
  static warn(message: string, data?: any): void {
    console.warn(`[WARN] ${message}`, data);
    this.sendToMonitoring('warn', message, data);
  }
  
  static error(message: string, error?: Error, data?: any): void {
    console.error(`[ERROR] ${message}`, error, data);
    this.sendToMonitoring('error', message, { error: error?.stack, data });
  }
  
  private static sendToMonitoring(level: string, message: string, data?: any): void {
    // 发送到监控服务
    if (!this.isDevelopment) {
      // 实际项目中集成Sentry等监控服务
    }
  }
}

// ✅ 性能监控
function usePerformanceMonitoring() {
  useEffect(() => {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      if (duration > 1000) { // 组件渲染超过1秒
        Logger.warn('Component render time exceeded 1s', { duration });
      }
    };
  }, []);
}
```

## 11. 代码审查规范

### 11.1 审查清单
- [ ] **类型安全**：是否有any类型，类型定义是否完整
- [ ] **命名规范**：变量、函数、文件命名是否符合规范
- [ ] **代码组织**：函数长度、文件行数是否合理
- [ ] **性能考虑**：是否存在不必要的重渲染或计算
- [ ] **错误处理**：是否有完善的错误处理机制
- [ ] **安全性**：输入验证、XSS防护是否到位
- [ ] **测试覆盖**：是否有对应的单元测试
- [ ] **文档注释**：复杂逻辑是否有注释说明

### 11.2 审查流程
1. **自审**：提交前开发者自己检查代码
2. **工具检查**：ESLint、TypeScript编译检查通过
3. **同行审查**：至少一位同事代码审查
4. **测试验证**：所有测试用例通过
5. **合并主干**：审查通过后合并到主分支

---

## AI对话模块开发经验 ⭐ 新增章节

### 智能响应设计

1. **学科分类响应**
   - 按数学、物理、化学等学科设计专门的响应逻辑
   - 每个学科包含基础理论、解题示例、相关知识点
   - 提供结构化的学习内容，便于学生理解

2. **多模态交互处理**
   - 支持文字+图片的混合输入
   - 根据上传文件类型提供不同的识别建议
   - 图像识别能力的详细说明和使用指导

3. **图表生成集成**
   - 数学函数图像：支持常见函数的可视化
   - 物理图示：运动图、受力图等
   - 统计图表：数据分析和可视化

### 图表组件开发要点 ⭐ 重要经验

1. **数据验证和错误处理**
   ```typescript
   // ✅ 数据验证
   if (!data || !Array.isArray(data.data) || data.data.length === 0) {
     return <EmptyChartComponent />;
   }

   // ✅ 错误边界
   try {
     const option = generateChartOption(data);
     return <ReactECharts option={option} />;
   } catch (error) {
     return <ErrorFallbackComponent />;
   }
   ```

2. **数据格式统一**
   - 线图数据：`[{x: number, y: number}]`
   - 柱状图数据：`[{name: string, value: number}]`
   - 饼图数据：`[{name: string, value: number}]`
   - 散点图数据：`[{x: number, y: number}]`

3. **TypeScript属性兼容性**
   - Card组件不支持style属性，使用内部div包装
   - ReactECharts不支持onError回调，依赖try-catch
   - 避免在接口中使用不存在的属性

## 最近开发总结 (2024-12-27)

### 完成的功能
1. **AI对话模块完整实现**：支持多学科智能问答
2. **图表生成功能**：集成ECharts实现数据可视化
3. **多模态交互**：文字+图片的混合输入支持
4. **错误处理优化**：添加图表组件错误边界

### 常见Bug修复经验
1. **ECharts渲染错误**
   - 原因：数据格式不匹配导致图表渲染失败
   - 解决：添加数据验证和格式转换逻辑
   - 预防：为每种图表类型定义标准数据格式

2. **TypeScript类型错误**
   - 原因：组件属性不兼容（如Card不支持style）
   - 解决：使用内部元素承载样式属性
   - 预防：查阅组件文档确认支持的属性

3. **Mock Service Worker 404错误**
   - 原因：API路径不匹配或MSW未正确启动
   - 解决：检查API路径和MSW配置
   - 预防：统一API路径定义，添加启动日志

*本规范将根据项目发展和团队反馈持续更新优化* 