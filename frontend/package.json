{"name": "college-ai-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@rollup/rollup-darwin-x64": "^4.44.1", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/react-katex": "^3.0.4", "@types/react-syntax-highlighter": "^15.5.13", "axios": "^1.7.9", "clsx": "^2.1.1", "dayjs": "^1.11.13", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "framer-motion": "^11.11.17", "immer": "^10.1.1", "katex": "^0.16.22", "lucide-react": "^0.460.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.58.1", "react-katex": "^3.1.0", "react-markdown": "^10.1.0", "react-router-dom": "^6.30.1", "react-syntax-highlighter": "^15.6.1", "rehype-katex": "^7.0.1", "remark-math": "^6.0.0", "tailwind-merge": "^3.3.1", "zod": "^3.25.67", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.13.0", "@playwright/test": "^1.53.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.6.1", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.3", "@vitest/coverage-v8": "^1.6.1", "@vitest/ui": "^1.6.1", "autoprefixer": "^10.4.20", "eslint": "^9.13.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.11.0", "jsdom": "^23.2.0", "msw": "^2.10.2", "postcss": "^8.4.49", "tailwindcss": "^3.4.14", "typescript": "~5.6.2", "typescript-eslint": "^8.10.0", "vite": "^5.4.10", "vitest": "^1.6.1"}, "msw": {"workerDirectory": ["public"]}}