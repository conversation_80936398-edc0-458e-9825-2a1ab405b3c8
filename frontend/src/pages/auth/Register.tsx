import React from 'react';

const Register: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center p-6">
      <div className="minimal-card p-8 w-full max-w-md">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold gradient-text mb-2">创建账户</h1>
          <p className="text-gray-600">加入AI助手大家庭</p>
        </div>
        
        <form className="space-y-6">
          <input type="text" placeholder="用户名" className="minimal-input" />
          <input type="email" placeholder="邮箱地址" className="minimal-input" />
          <input type="password" placeholder="密码" className="minimal-input" />
          <input type="password" placeholder="确认密码" className="minimal-input" />
          <button type="submit" className="minimal-button w-full">注册</button>
        </form>
        
        <p className="text-center text-gray-600 mt-6">
          已有账户？ <a href="/login" className="text-blue-600 hover:underline">立即登录</a>
        </p>
      </div>
    </div>
  );
};

export default Register; 