import React from 'react';

const Login: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center p-6">
      <div className="minimal-card p-8 w-full max-w-md">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold gradient-text mb-2">欢迎回来</h1>
          <p className="text-gray-600">登录您的AI助手账户</p>
        </div>
        
        <form className="space-y-6">
          <input type="email" placeholder="邮箱地址" className="minimal-input" />
          <input type="password" placeholder="密码" className="minimal-input" />
          <button type="submit" className="minimal-button w-full">登录</button>
        </form>
        
        <p className="text-center text-gray-600 mt-6">
          还没有账户？ <a href="/register" className="text-blue-600 hover:underline">立即注册</a>
        </p>
      </div>
    </div>
  );
};

export default Login; 