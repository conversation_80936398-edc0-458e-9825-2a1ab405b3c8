import React, { useState, useRef } from 'react';
import { motion } from 'framer-motion';
import { 
  Plus, 
  FileText, 
  Edit3,
  Search, 
  BookOpen,
  Download,
  RefreshCw,
  Save,
  Settings,
  BarChart3,
  Table,
  PieChart,
  TrendingUp,
  FileDown,
  Code,
  BookMarked,
  Zap,
  Brain,
  Library,
  Globe,
  CheckCircle,
  AlertCircle,
  X,
  Database,
  Sigma,
  Calculator,
  Grid3X3
} from 'lucide-react';
import Card, { CardHeader, CardTitle, CardDescription, CardContent } from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import Input from '../../components/ui/Input';

interface Paper {
  id: string;
  title: string;
  content: string;
  abstract: string;
  keywords: string[];
  references: Reference[];
  figures: Figure[];
  tables: TableData[];
  status: 'draft' | 'writing' | 'reviewing' | 'completed';
  format: 'word' | 'latex';
  template: string;
  wordCount: number;
  lastModified: Date;
  createdAt: Date;
}

interface Reference {
  id: string;
  type: 'article' | 'book' | 'website' | 'thesis';
  title: string;
  authors: string[];
  year: number;
  publisher?: string;
  journal?: string;
  volume?: string;
  issue?: string;
  pages?: string;
  doi?: string;
  url?: string;
  verified: boolean;
}

interface Figure {
  id: string;
  title: string;
  type: 'chart' | 'table' | 'image';
  data: any;
  caption: string;
  position: number;
}

interface TableData {
  id: string;
  title: string;
  headers: string[];
  rows: string[][];
  caption: string;
  position: number;
}

const Paper: React.FC = () => {
  // 基本状态
  const [paperTitle, setPaperTitle] = useState('');
  const [paperTopic, setPaperTopic] = useState('');
  const [paperKeywords, setPaperKeywords] = useState('');
  const [selectedTemplate, setSelectedTemplate] = useState('academic');
  const [currentPaper, setCurrentPaper] = useState<Paper | null>(null);
  
  // 编辑状态
  const [isEditing, setIsEditing] = useState(false);
  const [editMode, setEditMode] = useState<'markdown' | 'latex'>('markdown');
  const [autoSave, setAutoSave] = useState(true);
  
  // 文献状态
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<Reference[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showReferenceModal, setShowReferenceModal] = useState(false);
  const [showChartModal, setShowChartModal] = useState(false);
  
  // AI助手状态
  const [aiPrompt, setAiPrompt] = useState('');
  const [aiSuggestions, setAiSuggestions] = useState<string[]>([]);
  
  // 工作流状态
  const [currentStep, setCurrentStep] = useState<'info' | 'outline' | 'content' | 'review'>('info');
  const [outline, setOutline] = useState<string>('');
  const [isGeneratingOutline, setIsGeneratingOutline] = useState(false);
  
  // 模板选项
  const templates = [
    { id: 'academic', name: '学术研究论文', description: '标准学术论文格式' },
    { id: 'thesis', name: '学位论文', description: '本科/研究生论文格式' },
    { id: 'conference', name: '会议论文', description: '学术会议论文格式' },
    { id: 'report', name: '研究报告', description: '技术报告格式' }
  ];

  // 生成论文内容
  const generatePaperContent = (title: string, topic: string, keywords: string[], template: string): string => {
    const keywordsList = keywords.join(', ');
    
    const templates_content = {
      academic: `# ${title}

## 摘要

本研究围绕${topic}展开深入探讨。通过文献综述、实证分析和理论建构，本文系统地分析了该领域的核心问题。研究结果表明...

**关键词：** ${keywordsList}

## 1. 引言

随着现代科学技术的快速发展，${topic}已成为学术界关注的重要议题。本研究旨在通过系统性的理论分析和实证研究，深入探讨...

### 1.1 研究背景

在当前的学术背景下，${topic}的研究具有重要的理论价值和实践意义...

### 1.2 研究目的

本研究的主要目的是：
1. 梳理${topic}的理论基础
2. 分析当前研究的不足
3. 提出新的研究框架
4. 验证理论假设

## 2. 文献综述

### 2.1 理论基础

针对${topic}的研究，学者们从不同角度进行了深入探讨...

### 2.2 研究现状

通过对现有文献的系统梳理，发现当前研究主要集中在以下几个方面...

## 3. 研究方法

### 3.1 研究设计

本研究采用定量与定性相结合的混合研究方法...

### 3.2 数据收集

数据收集主要通过问卷调查、深度访谈和文献分析等方式进行...

## 4. 结果与分析

### 4.1 描述性分析

通过对收集数据的描述性分析，发现...

### 4.2 统计分析

运用SPSS统计软件进行相关性分析和回归分析...

## 5. 讨论

### 5.1 研究发现

本研究的主要发现包括...

### 5.2 理论贡献

本研究在理论上的贡献主要体现在...

### 5.3 实践意义

研究结果对实践的指导意义在于...

## 6. 结论

本研究通过系统的理论分析和实证研究，得出以下结论...

### 6.1 研究局限

本研究存在以下局限性...

### 6.2 未来研究方向

基于本研究的发现，未来研究可以从以下方向展开...

## 参考文献

[1] 作者1. ${topic}研究的理论基础[J]. 学术期刊, 2023, 45(3): 12-25.
[2] 作者2. ${topic}的实证分析[M]. 学术出版社, 2022: 156-178.
[3] 作者3. ${topic}发展趋势研究[J]. 研究论坛, 2023, 67(8): 89-102.`,

      thesis: `# ${title}

## 摘要

本学位论文以${topic}为研究对象，通过深入的理论分析和实证研究，探讨了该领域的核心问题。研究采用定量与定性相结合的方法...

**关键词：** ${keywordsList}

## Abstract

This thesis presents a comprehensive study on ${topic}...

**Keywords:** ${keywordsList}

## 目录

第一章 绪论........................1
第二章 文献综述....................15
第三章 研究设计....................28
第四章 研究方法....................42
第五章 结果分析....................56
第六章 讨论........................78
第七章 结论........................92
参考文献...........................98
附录...............................105

## 第一章 绪论

### 1.1 研究背景

在全球化和信息化的时代背景下，${topic}作为一个重要的研究领域...

### 1.2 研究问题

本研究主要关注以下几个核心问题：
1. ${topic}的理论基础是什么？
2. ${topic}在实践中面临哪些挑战？
3. 如何构建有效的${topic}评价体系？

### 1.3 研究目标

本研究的具体目标包括：
- 系统梳理${topic}的理论脉络
- 分析${topic}的实践现状
- 提出${topic}的优化策略

### 1.4 研究意义

#### 1.4.1 理论意义

本研究在理论层面的贡献主要体现在...

#### 1.4.2 实践意义

在实践层面，本研究有助于...

### 1.5 研究内容与方法

#### 1.5.1 研究内容

本研究的主要内容包括...

#### 1.5.2 研究方法

本研究采用的主要方法有...

### 1.6 论文结构

本论文共分为七章...

## 第二章 文献综述

### 2.1 相关概念界定

#### 2.1.1 ${topic}的定义

关于${topic}的定义，学术界存在不同观点...

#### 2.1.2 相关概念辨析

与${topic}相关的概念主要包括...

### 2.2 理论基础

#### 2.2.1 经典理论

${topic}的理论基础可以追溯到...

#### 2.2.2 现代理论发展

近年来，${topic}理论发展呈现出...

### 2.3 国外研究现状

#### 2.3.1 发达国家研究

欧美发达国家在${topic}研究方面起步较早...

#### 2.3.2 新兴市场研究

新兴市场国家在${topic}研究方面...

### 2.4 国内研究现状

#### 2.4.1 研究阶段划分

我国${topic}研究大致可分为三个阶段...

#### 2.4.2 主要观点梳理

国内学者对${topic}的主要观点包括...

### 2.5 研究评述

#### 2.5.1 已有研究的贡献

已有研究在以下方面取得了重要进展...

#### 2.5.2 研究不足与展望

当前研究存在的不足主要体现在...`,

      conference: `# ${title}

## Abstract

This paper investigates ${topic} through comprehensive analysis...

**Keywords:** ${keywordsList}

## 1. Introduction

${topic} has become increasingly important in recent years...

### 1.1 Background

The research background of ${topic} can be traced to...

### 1.2 Motivation

The motivation for this study stems from...

### 1.3 Contributions

The main contributions of this paper are:
- Analysis of ${topic} from multiple perspectives
- Proposal of innovative solutions
- Empirical validation of theoretical frameworks

## 2. Related Work

### 2.1 Theoretical Foundations

Previous studies on ${topic} have established...

### 2.2 Recent Advances

Recent advances in ${topic} research include...

## 3. Methodology

### 3.1 Research Design

Our research methodology consists of...

### 3.2 Data Collection

Data was collected through...

### 3.3 Analysis Framework

The analysis framework includes...

## 4. Results

### 4.1 Quantitative Results

The quantitative analysis reveals...

### 4.2 Qualitative Findings

Qualitative analysis shows...

## 5. Discussion

### 5.1 Implications

The results have important implications for...

### 5.2 Limitations

This study has several limitations...

## 6. Conclusion

This paper presents a comprehensive study of ${topic}...

### 6.1 Summary

In summary, our findings indicate...

### 6.2 Future Work

Future research directions include...

## References

[1] Author1, A. (2023). ${topic} analysis. Journal Name, 45(3), 12-25.
[2] Author2, B. (2022). ${topic} framework. Conference Proceedings, 156-178.`,

      report: `# ${title}

## 执行摘要

本报告针对${topic}进行了全面的技术分析和评估。通过深入的调研和数据分析，本报告提出了具体的解决方案和实施建议...

**关键词：** ${keywordsList}

## 1. 引言

### 1.1 报告背景

随着技术的快速发展，${topic}已成为行业关注的焦点...

### 1.2 报告目的

本报告旨在：
- 分析${topic}的现状和趋势
- 识别关键技术挑战
- 提出解决方案和建议

### 1.3 报告范围

本报告涵盖的范围包括...

## 2. 现状分析

### 2.1 市场概况

当前${topic}市场呈现以下特点...

### 2.2 技术现状

在技术层面，${topic}主要面临...

### 2.3 挑战识别

主要挑战包括：
1. 技术挑战
2. 市场挑战
3. 政策挑战

## 3. 技术方案

### 3.1 解决方案概述

针对识别的挑战，我们提出以下解决方案...

### 3.2 技术架构

技术架构包括以下几个层次...

### 3.3 实施路径

实施路径分为三个阶段：
- 第一阶段：基础建设
- 第二阶段：功能完善
- 第三阶段：优化提升

## 4. 风险评估

### 4.1 技术风险

技术风险主要包括...

### 4.2 市场风险

市场风险分析...

### 4.3 风险缓解措施

建议的风险缓解措施...

## 5. 效益分析

### 5.1 经济效益

预期的经济效益包括...

### 5.2 社会效益

社会效益体现在...

## 6. 建议与结论

### 6.1 主要建议

基于分析结果，我们建议...

### 6.2 结论

综上所述，${topic}具有重要的发展前景...`
    };
    
    return templates_content[template as keyof typeof templates_content] || templates_content.academic;
  };

  // 生成学术论文
  const generateAcademicPaper = () => {
    if (!paperTitle || !paperTopic) return;
    
    const keywords = paperKeywords.split(',').map(k => k.trim()).filter(k => k);
    let content = generatePaperContent(paperTitle, paperTopic, keywords, selectedTemplate);
    
    // 如果有大纲，将大纲作为基础结构
    if (outline) {
      content = outline + '\n\n' + content;
    }
    
    const newPaper: Paper = {
      id: Date.now().toString(),
      title: paperTitle,
      content: content,
      abstract: `本研究围绕${paperTopic}展开深入探讨。通过文献综述、实证分析和理论建构，本文系统地分析了该领域的核心问题。研究结果表明...`,
      keywords: keywords,
      references: [],
      figures: [],
      tables: [],
      status: 'draft',
      format: 'word',
      template: selectedTemplate,
      wordCount: content.length,
      lastModified: new Date(),
      createdAt: new Date()
    };
    
    setCurrentPaper(newPaper);
    setCurrentStep('content');
  };

  // 搜索文献
  const searchReferences = async () => {
    if (!searchQuery.trim()) return;
    
    setIsSearching(true);
    
    // 模拟API调用延迟
    setTimeout(() => {
      const mockResults: Reference[] = [
    {
      id: '1',
          type: 'article',
          title: `${searchQuery}的理论研究与实践应用`,
          authors: ['张三', '李四', '王五'],
          year: 2023,
          journal: '学术研究',
          volume: '45',
          issue: '3',
          pages: '12-25',
          doi: '10.1000/182',
          verified: true
    },
    {
      id: '2',
          type: 'book',
          title: `${searchQuery}概论`,
          authors: ['赵六'],
          year: 2022,
          publisher: '学术出版社',
          pages: '156-178',
          verified: true
    },
    {
      id: '3',
          type: 'article',
          title: `${searchQuery}发展趋势分析`,
          authors: ['孙七', '周八'],
          year: 2023,
          journal: '研究前沿',
          volume: '67',
          issue: '8',
          pages: '89-102',
          verified: false
        }
      ];
      
      setSearchResults(mockResults);
      setIsSearching(false);
    }, 1000);
  };

  // 智能数据表生成功能
  const generateDataTable = (tableConfig: {
    title: string;
    headers: string[];
    rows: string[][];
    description?: string;
    source?: string;
  }) => {
    if (!currentPaper) return;

    const newTable: TableData = {
      id: Date.now().toString(),
      title: tableConfig.title,
      headers: tableConfig.headers,
      rows: tableConfig.rows,
      caption: `表${currentPaper.tables.length + 1}：${tableConfig.title}`,
      position: currentPaper.content.length
    };

    const tableMarkdown = `

## ${newTable.caption}

${tableConfig.description ? `${tableConfig.description}\n` : ''}

| ${newTable.headers.join(' | ')} |
|${newTable.headers.map(() => '---').join('|')}|
${newTable.rows.map(row => `| ${row.join(' | ')} |`).join('\n')}

${tableConfig.source ? `*数据来源：${tableConfig.source}*` : '*注：表格数据根据研究分析生成*'}

`;

    setCurrentPaper({
      ...currentPaper,
      content: currentPaper.content + tableMarkdown,
      tables: [...currentPaper.tables, newTable],
      lastModified: new Date()
    });
  };

  // 智能数据图生成功能
  const generateSmartChart = (chartConfig: {
    type: 'bar' | 'line' | 'pie' | 'scatter';
    title: string;
    data?: any;
    description?: string;
    xAxisLabel?: string;
    yAxisLabel?: string;
  }) => {
    if (!currentPaper) return;

    const chartTemplates = {
      bar: {
        title: '数据对比分析',
        sample: {
          labels: ['实验组A', '实验组B', '对照组A', '对照组B'],
          datasets: [{
            label: chartConfig.yAxisLabel || '数值',
            data: [85.6, 78.3, 65.2, 71.8],
            backgroundColor: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444']
          }]
        }
      },
      line: {
        title: '变化趋势分析',
        sample: {
          labels: ['第1阶段', '第2阶段', '第3阶段', '第4阶段', '第5阶段'],
          datasets: [{
            label: chartConfig.yAxisLabel || '指标值',
            data: [20, 35, 45, 50, 60],
            borderColor: '#3B82F6',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            tension: 0.4
          }]
        }
      },
      pie: {
        title: '占比分布分析',
        sample: {
          labels: ['优秀', '良好', '中等', '改进'],
          datasets: [{
            data: [35, 40, 20, 5],
            backgroundColor: ['#10B981', '#3B82F6', '#F59E0B', '#EF4444']
          }]
        }
      },
      scatter: {
        title: '相关性分析',
        sample: {
          datasets: [{
            label: '数据点',
            data: [
              { x: 20, y: 30 }, { x: 25, y: 32 }, { x: 30, y: 35 },
              { x: 35, y: 38 }, { x: 40, y: 42 }, { x: 45, y: 45 }
            ],
            backgroundColor: '#3B82F6'
          }]
        }
      }
    };

    const template = chartTemplates[chartConfig.type];
    const chartData = chartConfig.data || template.sample;

    const newFigure: Figure = {
      id: Date.now().toString(),
      title: chartConfig.title || template.title,
      type: 'chart',
      data: {
        type: chartConfig.type,
        ...chartData
      },
      caption: `图${currentPaper.figures.length + 1}：${chartConfig.title || template.title}`,
      position: currentPaper.content.length
    };

    const chartMarkdown = `

## ${newFigure.caption}

${chartConfig.description ? `${chartConfig.description}\n` : ''}

\`\`\`chart
{
  "type": "${chartConfig.type}",
  "title": "${chartConfig.title || template.title}",
  "data": ${JSON.stringify(chartData, null, 2)}
}
\`\`\`

*图表说明：基于数据分析生成的学术可视化图表*

`;

    setCurrentPaper({
      ...currentPaper,
      content: currentPaper.content + chartMarkdown,
      figures: [...currentPaper.figures, newFigure],
      lastModified: new Date()
    });
  };

  // 预设数据表模板
  const generatePresetTable = (type: 'survey' | 'comparison' | 'statistics' | 'experiment') => {
    const tableTemplates = {
      survey: {
        title: '调查结果统计表',
        headers: ['调查项目', '样本数量', '百分比', '备注'],
        rows: [
          ['非常满意', '128', '32.0%', '最高满意度'],
          ['满意', '156', '39.0%', '较高满意度'],
          ['一般', '84', '21.0%', '中等满意度'],
          ['不满意', '32', '8.0%', '需要改进']
        ],
        description: '本表展示了调查问卷的统计结果，样本总数为400份。'
      },
      comparison: {
        title: '方法对比分析表',
        headers: ['方法名称', '准确率', '处理时间', '适用场景'],
        rows: [
          ['方法A', '85.6%', '0.5s', '小规模数据'],
          ['方法B', '92.3%', '1.2s', '中规模数据'],
          ['方法C', '88.9%', '0.8s', '复杂场景'],
          ['本文方法', '94.7%', '0.7s', '通用场景']
        ],
        description: '各种方法的性能对比分析，展示了本文方法的优势。'
      },
      statistics: {
        title: '描述性统计分析表',
        headers: ['变量', '均值', '标准差', '最小值', '最大值'],
        rows: [
          ['变量X1', '45.6', '8.2', '28.1', '67.8'],
          ['变量X2', '78.3', '12.4', '52.6', '98.7'],
          ['变量X3', '62.1', '9.8', '41.2', '84.5'],
          ['变量Y', '81.7', '11.6', '59.3', '102.4']
        ],
        description: '研究变量的基本统计特征描述。'
      },
      experiment: {
        title: '实验结果数据表',
        headers: ['实验条件', '试验次数', '成功率', '平均效果'],
        rows: [
          ['条件1', '50', '88%', '良好'],
          ['条件2', '50', '92%', '优秀'],
          ['条件3', '50', '76%', '一般'],
          ['条件4', '50', '94%', '优秀']
        ],
        description: '不同实验条件下的测试结果统计。'
      }
    };

    const template = tableTemplates[type];
    generateDataTable({
      title: template.title,
      headers: template.headers,
      rows: template.rows,
      description: template.description,
      source: '实验数据/问卷调查'
    });
  };

  // 添加图表
  const addChart = (type: 'bar' | 'line' | 'pie') => {
    if (!currentPaper) return;

    const chartData = {
      bar: {
        title: '数据对比图',
        data: {
          labels: ['项目A', '项目B', '项目C', '项目D'],
          datasets: [{
            label: '数值',
            data: [65, 59, 80, 81],
            backgroundColor: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444']
          }]
        }
      },
      line: {
        title: '趋势分析图',
        data: {
          labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
          datasets: [{
            label: '增长趋势',
            data: [12, 19, 3, 5, 2, 3],
            borderColor: '#3B82F6',
            tension: 0.1
          }]
        }
      },
      pie: {
        title: '比例分布图',
        data: {
          labels: ['类别1', '类别2', '类别3', '类别4'],
          datasets: [{
            data: [30, 25, 25, 20],
            backgroundColor: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444']
          }]
        }
      }
    };

    const newChart: Figure = {
      id: Date.now().toString(),
      title: chartData[type].title,
      type: 'chart',
      data: chartData[type].data,
      caption: `图${currentPaper.figures.length + 1}：${chartData[type].title}`,
      position: currentPaper.content.length
    };

    const chartMarkdown = `

## ${newChart.caption}

[图表数据：${JSON.stringify(newChart.data)}]

`;

    setCurrentPaper({
      ...currentPaper,
      content: currentPaper.content + chartMarkdown,
      figures: [...currentPaper.figures, newChart],
      lastModified: new Date()
    });
  };

  // 添加表格
  const addTable = () => {
    if (!currentPaper) return;

    const newTable: TableData = {
      id: Date.now().toString(),
      title: '数据统计表',
      headers: ['项目', '数值1', '数值2', '百分比'],
      rows: [
        ['项目A', '123', '456', '25%'],
        ['项目B', '234', '567', '30%'],
        ['项目C', '345', '678', '45%']
      ],
      caption: `表${currentPaper.tables.length + 1}：数据统计表`,
      position: currentPaper.content.length
    };

    const tableMarkdown = `

## ${newTable.caption}

| ${newTable.headers.join(' | ')} |
|${newTable.headers.map(() => '---').join('|')}|
${newTable.rows.map(row => `| ${row.join(' | ')} |`).join('\n')}

`;

    setCurrentPaper({
      ...currentPaper,
      content: currentPaper.content + tableMarkdown,
      tables: [...currentPaper.tables, newTable],
      lastModified: new Date()
    });
  };

  // 导出Word文档
  const exportToWord = () => {
    if (!currentPaper) return;
    
    // 模拟Word导出
    const blob = new Blob([currentPaper.content], { type: 'application/msword' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${currentPaper.title}.doc`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    alert('Word文档导出成功！');
  };

  // 导出LaTeX代码
  const exportToLatex = () => {
    if (!currentPaper) return;
    
    const latexContent = `\\documentclass{article}
\\usepackage[utf8]{inputenc}
\\usepackage{CJKutf8}
\\usepackage{amsmath}
\\usepackage{graphicx}

\\begin{document}
\\begin{CJK}{UTF8}{gbsn}

\\title{${currentPaper.title}}
\\author{作者姓名}
\\date{\\today}
\\maketitle

\\begin{abstract}
${currentPaper.abstract}
\\end{abstract}

${currentPaper.content.replace(/# /g, '\\section{').replace(/## /g, '\\subsection{').replace(/### /g, '\\subsubsection{')}

\\end{CJK}
\\end{document}`;
    
    const blob = new Blob([latexContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${currentPaper.title}.tex`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    alert('LaTeX代码导出成功！');
  };

  // 获取AI建议
  const getAISuggestions = () => {
    if (!aiPrompt.trim()) return;
    
    // 模拟AI建议
    const suggestions = [
      `关于"${aiPrompt}"的写作建议：建议从理论基础、现状分析、问题识别三个维度展开`,
      `可以考虑增加实证数据支撑，使用统计分析方法验证你的观点`,
      `建议参考最新的相关文献，特别是近两年的高质量期刊论文`,
      `关于"${aiPrompt}"的写作，建议加强数据支撑和图表分析，提高论证的说服力`
    ];
    
    setAiSuggestions(suggestions);
  };

  // 生成大纲
  const generateOutline = () => {
    if (!paperTitle || !paperTopic) return;
    
    setIsGeneratingOutline(true);
    
    // 模拟生成大纲
    setTimeout(() => {
      const outlineContent = `# ${paperTitle} - 论文大纲

## 一、引言
### 1.1 研究背景
- ${paperTopic}的发展现状
- 当前面临的主要问题
- 研究的必要性和紧迫性

### 1.2 研究目的与意义
- 理论意义
- 实践意义
- 创新点

### 1.3 研究内容与方法
- 研究内容概述
- 研究方法选择
- 技术路线

## 二、文献综述
### 2.1 相关理论基础
- 基础理论梳理
- 核心概念界定

### 2.2 国内外研究现状
- 国外研究进展
- 国内研究状况
- 研究不足与空白

### 2.3 研究评述与启示
- 已有研究贡献
- 存在问题分析
- 研究趋势判断

## 三、${paperTopic}理论分析
### 3.1 理论框架构建
- 理论基础
- 分析框架
- 核心要素

### 3.2 影响因素分析
- 内部因素
- 外部因素
- 因素关系

### 3.3 理论假设提出
- 主要假设
- 假设依据
- 假设关系

## 四、研究设计与方法
### 4.1 研究设计
- 研究类型
- 研究流程
- 技术路线

### 4.2 数据收集方法
- 数据来源
- 收集方式
- 样本选择

### 4.3 数据分析方法
- 分析工具
- 分析步骤
- 可靠性检验

## 五、实证分析
### 5.1 描述性统计分析
- 样本基本情况
- 变量描述统计
- 初步分析结果

### 5.2 相关性分析
- 相关系数计算
- 相关性检验
- 结果解释

### 5.3 回归分析
- 模型构建
- 参数估计
- 模型检验

## 六、结果讨论
### 6.1 主要发现
- 核心结论
- 重要发现
- 创新成果

### 6.2 理论贡献
- 理论发展
- 理论完善
- 理论创新

### 6.3 实践意义
- 应用价值
- 政策建议
- 实践指导

## 七、结论与展望
### 7.1 研究结论
- 主要结论
- 研究意义
- 理论价值

### 7.2 研究局限
- 样本局限
- 方法局限
- 其他限制

### 7.3 未来研究方向
- 深化研究
- 拓展研究
- 方法改进

## 参考文献

## 附录
- 调查问卷
- 访谈提纲
- 数据统计表`;

      setOutline(outlineContent);
      setIsGeneratingOutline(false);
      setCurrentStep('outline');
    }, 2000);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-50 p-4">
      <div className="container mx-auto max-w-screen-2xl">
        {/* 顶部工具栏 */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6 gap-4">
          <h1 className="text-2xl font-bold text-gray-800">论文写作助手</h1>
          {currentPaper && (
            <div className="flex flex-wrap items-center justify-center lg:justify-end gap-4 sm:gap-6 p-3 sm:p-4 bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl border border-gray-200 shadow-sm">
              <Button 
                onClick={() => setShowReferenceModal(true)}
                variant="outline"
                size="md"
                icon={<Database className="w-4 h-4" />}
                className="whitespace-nowrap min-w-[120px] sm:min-w-[130px] h-11 px-4 sm:px-6 text-sm font-medium transition-all hover:shadow-md"
              >
                文献管理
              </Button>
              <Button 
                onClick={() => setShowChartModal(true)}
                variant="outline"
                size="md"
                icon={<Grid3X3 className="w-4 h-4" />}
                className="whitespace-nowrap min-w-[120px] sm:min-w-[130px] h-11 px-4 sm:px-6 text-sm font-medium transition-all hover:shadow-md"
              >
                图表工具
              </Button>
              <Button
                onClick={exportToWord}
                variant="outline"
                size="md"
                icon={<FileText className="w-4 h-4" />}
                className="whitespace-nowrap min-w-[120px] sm:min-w-[130px] h-11 px-4 sm:px-6 text-sm font-medium transition-all hover:shadow-md"
              >
                导出Word
              </Button>
              <Button 
                onClick={exportToLatex}
                variant="outline"
                size="md"
                icon={<Sigma className="w-4 h-4" />}
                className="whitespace-nowrap min-w-[120px] sm:min-w-[130px] h-11 px-4 sm:px-6 text-sm font-medium transition-all hover:shadow-md"
              >
                导出LaTeX
              </Button>
            </div>
          )}
        </div>

        {/* 工作流步骤指示器 */}
        <div className="mb-6">
          <div className="flex items-center justify-center">
            <div className="flex items-center space-x-4 bg-white rounded-full p-2 shadow-md">
              {/* 步骤1：基础信息 */}
              <div className={`flex items-center space-x-2 px-4 py-2 rounded-full transition-all duration-300 ${
                currentStep === 'info' ? 'bg-blue-500 text-white' : 'text-gray-500 hover:bg-gray-100'
              }`}>
                <div className={`w-2 h-2 rounded-full ${currentStep === 'info' ? 'bg-white' : 'bg-gray-400'}`}></div>
                <span className="text-sm font-medium">基础信息</span>
              </div>
              
              {/* 连接线 */}
              <div className="w-8 h-0.5 bg-gray-300"></div>
              
              {/* 步骤2：生成大纲 */}
              <div 
                className={`flex items-center space-x-2 px-4 py-2 rounded-full transition-all duration-300 cursor-pointer ${
                  currentStep === 'outline' ? 'bg-blue-500 text-white' : 'text-gray-500 hover:bg-gray-100'
                }`}
                onClick={() => paperTitle && paperTopic && setCurrentStep('outline')}
              >
                <div className={`w-2 h-2 rounded-full ${currentStep === 'outline' ? 'bg-white' : 'bg-gray-400'}`}></div>
                <span className="text-sm font-medium">生成大纲</span>
              </div>
              
              {/* 连接线 */}
              <div className="w-8 h-0.5 bg-gray-300"></div>
              
              {/* 步骤3：论文生成 */}
              <div 
                className={`flex items-center space-x-2 px-4 py-2 rounded-full transition-all duration-300 cursor-pointer ${
                  currentStep === 'content' ? 'bg-blue-500 text-white' : 'text-gray-500 hover:bg-gray-100'
                }`}
                onClick={() => (outline || currentPaper) && setCurrentStep('content')}
              >
                <div className={`w-2 h-2 rounded-full ${currentStep === 'content' ? 'bg-white' : 'bg-gray-400'}`}></div>
                <span className="text-sm font-medium">论文生成</span>
              </div>
              
              {/* 连接线 */}
              <div className="w-8 h-0.5 bg-gray-300"></div>
              
              {/* 步骤4：审阅完善 */}
              <div 
                className={`flex items-center space-x-2 px-4 py-2 rounded-full transition-all duration-300 cursor-pointer ${
                  currentStep === 'review' ? 'bg-blue-500 text-white' : 'text-gray-500 hover:bg-gray-100'
                }`}
                onClick={() => currentPaper && setCurrentStep('review')}
              >
                <div className={`w-2 h-2 rounded-full ${currentStep === 'review' ? 'bg-white' : 'bg-gray-400'}`}></div>
                <span className="text-sm font-medium">审阅完善</span>
              </div>
            </div>
          </div>
        </div>

        {/* 主要内容区域 */}
        <div className="grid grid-cols-1 xl:grid-cols-5 gap-6 min-h-0">
          {/* 左侧：论文基本信息 */}
          <div className="xl:col-span-2 space-y-4 sidebar-content">
            <Card className="h-fit">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <FileText className="h-5 w-5" />
                  论文信息
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">论文标题</label>
                  <Input
                    value={paperTitle}
                    onChange={(e) => setPaperTitle(e.target.value)}
                    placeholder="请输入论文标题..."
                    className="w-full"
                    disabled={!!currentPaper}
                  />
                  </div>

                <div>
                  <label className="block text-sm font-medium mb-2">研究主题</label>
                  <Input
                    value={paperTopic}
                    onChange={(e) => setPaperTopic(e.target.value)}
                    placeholder="请输入研究主题..."
                    className="w-full"
                    disabled={!!currentPaper}
                  />
                    </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">关键词</label>
                  <Input
                    value={paperKeywords}
                    onChange={(e) => setPaperKeywords(e.target.value)}
                    placeholder="用逗号分隔..."
                    className="w-full"
                    disabled={!!currentPaper}
                  />
                  </div>

                <div>
                  <label className="block text-sm font-medium mb-2">论文模板</label>
                  <div className="space-y-2">
                    {templates.map((template) => (
                      <div
                        key={template.id}
                        className={`p-3 border rounded-lg cursor-pointer transition-all ${
                          selectedTemplate === template.id
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        } ${currentPaper ? 'opacity-50 pointer-events-none' : ''}`}
                        onClick={() => !currentPaper && setSelectedTemplate(template.id)}
                      >
                        <div className="font-medium text-sm">{template.name}</div>
                        <div className="text-xs text-gray-600 mt-1">{template.description}</div>
                    </div>
                    ))}
                  </div>
                </div>

                {!currentPaper && (
                  <div className="space-y-3">
                    <Button
                      onClick={generateOutline}
                      className="w-full"
                      disabled={!paperTitle || !paperTopic || isGeneratingOutline}
                      variant="outline"
                    >
                      {isGeneratingOutline ? (
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        <BookMarked className="h-4 w-4 mr-2" />
                      )}
                      {isGeneratingOutline ? '生成中...' : '生成大纲'}
                    </Button>
                    
                    {outline && (
                      <Button
                        onClick={generateAcademicPaper}
                        className="w-full"
                        disabled={!paperTitle || !paperTopic}
                      >
                        <Zap className="h-4 w-4 mr-2" />
                        生成论文
                      </Button>
                    )}
                  </div>
                )}

                {currentPaper && (
                  <div className="space-y-3 pt-3 border-t">
                    <div className="text-sm space-y-1">
                      <div className="flex justify-between">
                        <span className="text-gray-600">状态：</span>
                        <span className="font-medium">{currentPaper.status === 'draft' ? '草稿' : '完成'}</span>
                  </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">字数：</span>
                        <span className="font-medium">{currentPaper.wordCount}</span>
                </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">文献：</span>
                        <span className="font-medium">{currentPaper.references.length}</span>
      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">图表：</span>
                        <span className="font-medium">{currentPaper.figures.length + currentPaper.tables.length}</span>
    </div>
                    </div>
        <Button
                      onClick={() => {
                        setCurrentPaper(null);
                        setPaperTitle('');
                        setPaperTopic('');
                        setPaperKeywords('');
                        setSelectedTemplate('academic');
                        setOutline('');
                        setCurrentStep('info');
                        setAiPrompt('');
                        setAiSuggestions([]);
                      }}
                      variant="outline"
                      size="sm"
                      className="w-full"
                    >
                      新建论文
        </Button>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* AI写作助手 */}
            <Card className="h-fit">
          <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Brain className="h-5 w-5" />
                  AI助手
                </CardTitle>
          </CardHeader>
              <CardContent className="space-y-3">
                <Input
                  value={aiPrompt}
                  onChange={(e) => setAiPrompt(e.target.value)}
                  placeholder="描述你需要帮助的问题..."
                  className="w-full"
                />
                <Button
                  onClick={getAISuggestions}
                  className="w-full"
                  size="sm"
                  disabled={!aiPrompt.trim()}
                >
                  <Zap className="h-4 w-4 mr-2" />
                  获取建议
                </Button>
                
                {aiSuggestions.length > 0 && (
                  <div className="space-y-2">
                    <div className="text-sm font-medium">AI建议：</div>
                    <div className="max-h-40 overflow-y-auto space-y-2">
                      {aiSuggestions.map((suggestion, index) => (
                        <div key={index} className="p-2 bg-blue-50 rounded text-xs leading-relaxed">
                          {suggestion}
                </div>
                      ))}
              </div>
                </div>
                )}
              </CardContent>
            </Card>
              </div>

          {/* 右侧：论文生成和编辑 */}
          <div className="xl:col-span-3 main-content">
            {currentStep === 'info' && !outline && !currentPaper && (
              <Card className="h-96 flex items-center justify-center">
                <div className="text-center text-gray-500">
                  <FileText className="h-16 w-16 mx-auto mb-4 text-gray-300" />
                  <h3 className="text-lg font-medium mb-2">开始您的学术写作</h3>
                  <p className="text-sm">请在左侧填写论文基本信息，然后点击"生成大纲"开始创作</p>
                </div>
              </Card>
            )}

            {currentStep === 'outline' && outline && !currentPaper && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <BookMarked className="h-5 w-5" />
                    论文大纲
                  </CardTitle>
                  <CardDescription>
                    基于您的研究主题生成的论文大纲，您可以进行修改后再生成论文
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <textarea
                      value={outline}
                      onChange={(e) => setOutline(e.target.value)}
                      className="w-full h-96 p-4 border rounded-lg resize-none font-mono text-sm"
                      placeholder="论文大纲将在这里显示..."
                    />
                    <div className="flex gap-3">
                      <Button
                        onClick={() => {
                          generateAcademicPaper();
                          setCurrentStep('content');
                        }}
                        className="flex-1"
                        disabled={!outline.trim()}
                      >
                        <Zap className="h-4 w-4 mr-2" />
                        基于大纲生成论文
                      </Button>
                      <Button
                        onClick={generateOutline}
                        variant="outline"
                        disabled={isGeneratingOutline}
                      >
                        {isGeneratingOutline ? (
                          <RefreshCw className="h-4 w-4 animate-spin" />
                        ) : (
                          <RefreshCw className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {(currentStep === 'content' || currentStep === 'review') && currentPaper && (
              <div className="space-y-6">
                {/* 论文编辑器 */}
                <Card>
                  <CardHeader>
                    <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                      <div className="flex-1 min-w-0">
                        <CardTitle className="flex items-center gap-2 text-lg truncate">
                          <Edit3 className="h-5 w-5 flex-shrink-0" />
                          <span className="truncate">{currentPaper.title}</span>
                        </CardTitle>
                        <CardDescription className="mt-1">
                          字数：{currentPaper.content.length} | 最后修改：{currentPaper.lastModified.toLocaleString()}
                        </CardDescription>
                      </div>
                      <div className="flex items-center gap-2 flex-shrink-0">
                        <div className="flex rounded-lg border">
                          <button
                            className={`px-3 py-1 text-sm rounded-l-lg transition-colors ${
                              editMode === 'markdown' 
                                ? 'bg-blue-500 text-white' 
                                : 'text-gray-600 hover:bg-gray-50'
                            }`}
                            onClick={() => setEditMode('markdown')}
                          >
                            Markdown
                          </button>
                          <button
                            className={`px-3 py-1 text-sm rounded-r-lg transition-colors ${
                              editMode === 'latex' 
                                ? 'bg-blue-500 text-white' 
                                : 'text-gray-600 hover:bg-gray-50'
                            }`}
                            onClick={() => setEditMode('latex')}
                          >
                            LaTeX
                          </button>
                        </div>
                        <Button variant="outline" size="sm">
                          <Save className="h-4 w-4" />
                </Button>
              </div>
            </div>
                  </CardHeader>
                  <CardContent>
                    <div className="relative">
                      <textarea
                        value={currentPaper.content}
                        onChange={(e) => setCurrentPaper({
                          ...currentPaper,
                          content: e.target.value,
                          wordCount: e.target.value.length,
                          lastModified: new Date()
                        })}
                        className="w-full h-[600px] p-4 border border-gray-200 rounded-lg resize-none font-mono text-sm leading-relaxed focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="开始编写您的论文内容..."
                      />
                    </div>
          </CardContent>
        </Card>


                      </div>
            )}
                    </div>
            </div>
    </div>

              {/* 文献管理模态框 */}
        {showReferenceModal && (
          <div className="modal-overlay fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
            <div className="p-6 border-b">
          <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">文献管理</h3>
                <Button
                  onClick={() => setShowReferenceModal(false)}
                  variant="ghost"
                  size="sm"
                >
                  <X className="h-4 w-4" />
                </Button>
            </div>
            </div>
            
            <div className="p-6 flex-1 overflow-y-auto">
              <div className="space-y-6">
                <div className="flex gap-3">
                  <Input
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="搜索文献..."
                    className="flex-1"
                  />
                  <Button
                    onClick={searchReferences}
                    disabled={isSearching}
                    className="flex-shrink-0 px-4"
                  >
                    {isSearching ? (
                      <RefreshCw className="h-4 w-4 animate-spin" />
                    ) : (
                      <Search className="h-4 w-4" />
                    )}
                  </Button>
                </div>
                
                {searchResults.length > 0 && (
                  <div className="space-y-4">
                    <h4 className="font-medium text-lg">搜索结果</h4>
                    <div className="grid gap-4">
                      {searchResults.map((ref) => (
                        <div key={ref.id} className="p-4 border rounded-lg">
                          <div className="font-medium text-lg mb-2">{ref.title}</div>
                          <div className="text-sm text-gray-600 mb-3">{ref.authors.join(', ')} ({ref.year})</div>
            <div className="flex items-center gap-3">
                            {ref.verified && (
                              <div className="flex items-center gap-1">
                                <CheckCircle className="h-4 w-4 text-green-500" />
                                <span className="text-xs text-green-600">已验证</span>
                              </div>
                            )}
              <Button
                              size="sm"
                              onClick={() => {
                                if (currentPaper) {
                                  setCurrentPaper({
                                    ...currentPaper,
                                    references: [...currentPaper.references, ref]
                                  });
                                }
                              }}
                            >
                              添加到论文
              </Button>
            </div>
          </div>
                      ))}
        </div>
      </div>
                )}

                {currentPaper && currentPaper.references.length > 0 && (
                  <div className="space-y-4">
                    <h4 className="font-medium text-lg">已添加文献 ({currentPaper.references.length})</h4>
                    <div className="grid gap-3">
                      {currentPaper.references.map((ref, index) => (
                        <div key={ref.id} className="p-4 bg-gray-50 rounded-lg">
                          <div className="font-medium">[{index + 1}] {ref.title}</div>
                          <div className="text-sm text-gray-600 mt-1">{ref.authors.join(', ')}</div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

              {/* 图表工具模态框 */}
        {showChartModal && (
          <div className="modal-overlay fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
              <div className="p-6 border-b flex-shrink-0">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold">图表工具</h3>
                  <Button
                    onClick={() => setShowChartModal(false)}
                    variant="ghost"
                    size="sm"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              
              <div className="p-6 flex-1 overflow-y-auto">
                <div className="space-y-8">
                  {/* 智能数据表生成 */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-4 flex items-center gap-2">
                      <Grid3X3 className="h-5 w-5" />
                      智能数据表生成
                    </h4>
                    <div className="grid grid-cols-2 gap-3">
                      <Button
                        onClick={() => {
                          generatePresetTable('survey');
                          setShowChartModal(false);
                        }}
                        className="h-16 p-4 justify-start"
                        variant="outline"
                      >
                        <div className="text-left">
                          <div className="font-medium">调查统计表</div>
                          <div className="text-xs text-gray-500 mt-1">问卷调查结果分析</div>
                        </div>
                      </Button>
                      <Button
                        onClick={() => {
                          generatePresetTable('comparison');
                          setShowChartModal(false);
                        }}
                        className="h-16 p-4 justify-start"
                        variant="outline"
                      >
                        <div className="text-left">
                          <div className="font-medium">对比分析表</div>
                          <div className="text-xs text-gray-500 mt-1">方法性能对比</div>
                        </div>
                      </Button>
                      <Button
                        onClick={() => {
                          generatePresetTable('statistics');
                          setShowChartModal(false);
                        }}
                        className="h-16 p-4 justify-start"
                        variant="outline"
                      >
                        <div className="text-left">
                          <div className="font-medium">统计分析表</div>
                          <div className="text-xs text-gray-500 mt-1">描述性统计数据</div>
                        </div>
                      </Button>
                      <Button
                        onClick={() => {
                          generatePresetTable('experiment');
                          setShowChartModal(false);
                        }}
                        className="h-16 p-4 justify-start"
                        variant="outline"
                      >
                        <div className="text-left">
                          <div className="font-medium">实验结果表</div>
                          <div className="text-xs text-gray-500 mt-1">实验数据记录</div>
                        </div>
                      </Button>
                    </div>
                  </div>

                  {/* 智能图表生成 */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-4 flex items-center gap-2">
                      <TrendingUp className="h-5 w-5" />
                      智能图表生成
                    </h4>
                    <div className="grid grid-cols-2 gap-3">
                      <Button
                        onClick={() => {
                          generateSmartChart({
                            type: 'bar',
                            title: '实验数据对比分析',
                            description: '不同实验组的效果对比，展示各组在关键指标上的表现差异。'
                          });
                          setShowChartModal(false);
                        }}
                        className="h-16 p-4 justify-start"
                        variant="outline"
                      >
                        <div className="text-left">
                          <div className="font-medium flex items-center gap-2">
                            <BarChart3 className="h-4 w-4" />
                            柱状图
                          </div>
                          <div className="text-xs text-gray-500 mt-1">数据对比分析</div>
                        </div>
                      </Button>
                      <Button
                        onClick={() => {
                          generateSmartChart({
                            type: 'line',
                            title: '变化趋势分析图',
                            description: '展示关键指标随时间或阶段的变化趋势，揭示数据的发展规律。'
                          });
                          setShowChartModal(false);
                        }}
                        className="h-16 p-4 justify-start"
                        variant="outline"
                      >
                        <div className="text-left">
                          <div className="font-medium flex items-center gap-2">
                            <TrendingUp className="h-4 w-4" />
                            折线图
                          </div>
                          <div className="text-xs text-gray-500 mt-1">趋势变化分析</div>
                        </div>
                      </Button>
                      <Button
                        onClick={() => {
                          generateSmartChart({
                            type: 'pie',
                            title: '占比分布分析图',
                            description: '展示各个类别在总体中的占比情况，便于了解数据结构。'
                          });
                          setShowChartModal(false);
                        }}
                        className="h-16 p-4 justify-start"
                        variant="outline"
                      >
                        <div className="text-left">
                          <div className="font-medium flex items-center gap-2">
                            <PieChart className="h-4 w-4" />
                            饼图
                          </div>
                          <div className="text-xs text-gray-500 mt-1">占比分布分析</div>
                        </div>
                      </Button>
                      <Button
                        onClick={() => {
                          generateSmartChart({
                            type: 'scatter',
                            title: '相关性散点图',
                            description: '展示两个变量之间的相关关系，发现数据中的潜在联系。'
                          });
                          setShowChartModal(false);
                        }}
                        className="h-16 p-4 justify-start"
                        variant="outline"
                      >
                        <div className="text-left">
                          <div className="font-medium flex items-center gap-2">
                            <Calculator className="h-4 w-4" />
                            散点图
                          </div>
                          <div className="text-xs text-gray-500 mt-1">相关性分析</div>
                        </div>
                      </Button>
                    </div>
                  </div>

                  {/* 快速插入（兼容） */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-4">快速插入</h4>
                    <div className="grid grid-cols-4 gap-3">
                      <Button
                        onClick={() => {
                          addChart('bar');
                          setShowChartModal(false);
                        }}
                        className="h-12"
                        variant="outline"
                        size="sm"
                      >
                        <BarChart3 className="h-4 w-4" />
                      </Button>
                      <Button
                        onClick={() => {
                          addChart('line');
                          setShowChartModal(false);
                        }}
                        className="h-12"
                        variant="outline"
                        size="sm"
                      >
                        <TrendingUp className="h-4 w-4" />
                      </Button>
                      <Button
                        onClick={() => {
                          addChart('pie');
                          setShowChartModal(false);
                        }}
                        className="h-12"
                        variant="outline"
                        size="sm"
                      >
                        <PieChart className="h-4 w-4" />
                      </Button>
                      <Button
                        onClick={() => {
                          addTable();
                          setShowChartModal(false);
                        }}
                        className="h-12"
                        variant="outline"
                        size="sm"
                      >
                        <Table className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
    </div>
  );
};

export default Paper;
