import React from 'react';
import { Home } from 'lucide-react';

const NotFound: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center p-6">
      <div className="text-center space-y-6">
        <div className="text-8xl font-bold text-gray-300">404</div>
        <h1 className="text-3xl font-bold text-gray-800">页面未找到</h1>
        <p className="text-gray-600">抱歉，您访问的页面不存在</p>
        
        <a 
          href="/" 
          className="inline-flex items-center space-x-2 minimal-button"
        >
          <Home size={20} />
          <span>返回首页</span>
        </a>
      </div>
    </div>
  );
};

export default NotFound; 