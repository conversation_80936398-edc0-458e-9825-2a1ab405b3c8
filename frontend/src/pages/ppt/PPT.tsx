import React, { useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Plus,
  Upload,
  FileText,
  Settings,
  Download,
  Eye,
  Edit3,
  Copy,
  Trash2,
  Play,
  Code,
  Palette,
  Layout,
  Image as ImageIcon,
  BarChart3,
  FileSpreadsheet,
  Presentation,
  Sparkles,
  RefreshCw,
  ChevronLeft,
  ChevronRight,
  MoreHorizontal,
  Search,
  Clock,
  User,
  Calendar,
  Monitor,
  Zap,
  Bot,
  Wand2,
  PlayCircle,
  Share2,
  Bookmark,
  Lightbulb,
  BookOpen,
  Microscope,
  Calculator,
  Stethoscope,
  Building,
  Atom,
  Globe,
  PenTool,
  Save,
  RotateCcw,
  TypeIcon,
  Mouse,
  MousePointer,
  Square,
  Circle,
  Triangle,
  ArrowLeft,
  X
} from 'lucide-react';
import Button from '../../components/ui/Button';
import Input from '../../components/ui/Input';
import Card, { CardHeader, CardTitle, CardDescription, CardContent } from '../../components/ui/Card';

interface PPTTemplate {
  id: string;
  title: string;
  description: string;
  theme: string;
  slides: number;
  preview: string;
  category: 'business' | 'academic' | 'creative' | 'minimal' | 'science' | 'medical' | 'engineering' | 'arts';
  tags: string[];
  color: string;
  academicField?: string;
}

interface PPTProject {
  id: string;
  title: string;
  template: string;
  slides: PPTSlide[];
  theme: string;
  status: 'draft' | 'editing' | 'completed';
  createdAt: Date;
  lastModified: Date;
  vbaCode?: string;
}

interface PPTSlide {
  id: string;
  title: string;
  content: string;
  layout: 'title' | 'content' | 'image' | 'chart' | 'comparison' | 'timeline';
  order: number;
  elements: SlideElement[];
  notes?: string;
}

interface SlideElement {
  id: string;
  type: 'text' | 'image' | 'chart' | 'table' | 'shape';
  position: { x: number; y: number };
  size: { width: number; height: number };
  content: any;
  style: any;
}

const PPT: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'templates' | 'projects' | 'create' | 'edit'>('templates');
  const [selectedTemplate, setSelectedTemplate] = useState<PPTTemplate | null>(null);
  const [currentSlide, setCurrentSlide] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentProject, setCurrentProject] = useState<PPTProject | null>(null);
  const [selectedSlide, setSelectedSlide] = useState<PPTSlide | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // 扩展学术模板库
  const [templates] = useState<PPTTemplate[]>([
    // 学术类模板
    {
      id: '1',
      title: '学术报告标准版',
      description: '适合学术研究和论文答辩的标准模板',
      theme: 'academic',
      slides: 20,
      preview: '/templates/academic.jpg',
      category: 'academic',
      tags: ['学术', '研究', '答辩', '标准'],
      color: 'from-green-600 to-green-700',
      academicField: '通用'
    },
    {
      id: '2',
      title: '理工科研究报告',
      description: '专为理工科研究设计的学术模板',
      theme: 'science',
      slides: 25,
      preview: '/templates/science.jpg',
      category: 'science',
      tags: ['理工科', '实验', '数据分析', '公式'],
      color: 'from-blue-600 to-cyan-600',
      academicField: '理工科'
    },
    {
      id: '3',
      title: '医学研究展示',
      description: '医学研究和临床案例展示模板',
      theme: 'medical',
      slides: 22,
      preview: '/templates/medical.jpg',
      category: 'medical',
      tags: ['医学', '临床', '病例', '研究'],
      color: 'from-red-500 to-pink-600',
      academicField: '医学'
    },
    {
      id: '4',
      title: '工程项目汇报',
      description: '工程设计和项目进展汇报模板',
      theme: 'engineering',
      slides: 28,
      preview: '/templates/engineering.jpg',
      category: 'engineering',
      tags: ['工程', '设计', '项目', '技术'],
      color: 'from-orange-600 to-red-600',
      academicField: '工程'
    },
    {
      id: '5',
      title: '文科学术研究',
      description: '人文社科研究和理论分析模板',
      theme: 'arts',
      slides: 18,
      preview: '/templates/arts.jpg',
      category: 'arts',
      tags: ['文科', '人文', '社科', '理论'],
      color: 'from-purple-600 to-indigo-600',
      academicField: '文科'
    },
    {
      id: '6',
      title: '商业提案',
      description: '专业的商业计划和项目提案模板',
      theme: 'business',
      slides: 25,
      preview: '/templates/business.jpg',
      category: 'business',
      tags: ['商业', '提案', '专业'],
      color: 'from-blue-600 to-blue-700'
    },
    {
      id: '7',
      title: '创意展示',
      description: '充满创意的设计作品展示模板',
      theme: 'creative',
      slides: 15,
      preview: '/templates/creative.jpg',
      category: 'creative',
      tags: ['创意', '设计', '展示'],
      color: 'from-purple-600 to-purple-700'
    },
    {
      id: '8',
      title: '简约学术风',
      description: '简洁明了的学术风格模板',
      theme: 'minimal',
      slides: 16,
      preview: '/templates/minimal.jpg',
      category: 'minimal',
      tags: ['简约', '学术', '清爽'],
      color: 'from-gray-600 to-gray-700'
    },
    {
      id: '9',
      title: '数据科学展示',
      description: '数据分析和机器学习项目展示',
      theme: 'data-science',
      slides: 24,
      preview: '/templates/data-science.jpg',
      category: 'science',
      tags: ['数据科学', '机器学习', 'AI', '算法'],
      color: 'from-teal-600 to-green-600',
      academicField: '数据科学'
    },
    {
      id: '10',
      title: '化学实验报告',
      description: '化学实验和分析报告专用模板',
      theme: 'chemistry',
      slides: 20,
      preview: '/templates/chemistry.jpg',
      category: 'science',
      tags: ['化学', '实验', '分析', '分子'],
      color: 'from-indigo-600 to-purple-600',
      academicField: '化学'
    }
  ]);

  const [projects, setProjects] = useState<PPTProject[]>([
    {
      id: '1',
      title: '机器学习项目汇报',
      template: '数据科学展示',
      slides: [
        {
          id: 'slide1',
          title: '项目简介',
          content: '机器学习在图像识别中的应用研究',
          layout: 'title',
          order: 1,
          elements: [],
          notes: '项目背景和目标介绍'
        },
        {
          id: 'slide2',
          title: '研究背景',
          content: '深度学习算法的发展历程',
          layout: 'content',
          order: 2,
          elements: [],
          notes: '相关技术发展回顾'
        }
      ],
      theme: 'data-science',
      status: 'editing',
      createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
      lastModified: new Date(Date.now() - 1 * 60 * 60 * 1000),
      vbaCode: generateSampleVBACode()
    },
    {
      id: '2',
      title: '有机化学实验分析',
      template: '化学实验报告',
      slides: [
        {
          id: 'slide1',
          title: '实验目的',
          content: '有机合成反应机制研究',
          layout: 'title',
          order: 1,
          elements: [],
          notes: '实验设计思路'
        }
      ],
      theme: 'chemistry',
      status: 'completed',
      createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      lastModified: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
      vbaCode: generateSampleVBACode()
    }
  ]);

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<'all' | 'business' | 'academic' | 'creative' | 'minimal' | 'science' | 'medical' | 'engineering' | 'arts'>('all');
  const [aiPrompt, setAiPrompt] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [showAiPanel, setShowAiPanel] = useState(false);
  const [documentContent, setDocumentContent] = useState('');
  const [uploadProgress, setUploadProgress] = useState(0);
  const [generatedVBA, setGeneratedVBA] = useState('');

  const categoryNames = {
    all: '全部',
    science: '理工科',
    arts: '文科',
    business: '商科',
    academic: '学术通用',
    medical: '医学',
    engineering: '工程',
    creative: '创意',
    minimal: '简约'
  };

  const statusNames = {
    draft: '草稿',
    editing: '编辑中',
    completed: '已完成'
  };

  const academicFields = {
    '通用': '适用于各学科的通用模板',
    '理工科': '数学、物理、化学、生物等',
    '医学': '临床医学、基础医学、药学等',
    '工程': '机械、电子、土木、软件等',
    '文科': '文学、历史、哲学、语言等',
    '数据科学': '数据分析、AI、机器学习等',
    '化学': '有机化学、无机化学、分析化学等'
  };

  // 文档上传处理函数
  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    const file = files[0];
    const validTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
      'text/markdown'
    ];

    if (!validTypes.includes(file.type)) {
      alert('请上传支持的文件格式：PDF, Word, TXT, Markdown');
      return;
    }

    setUploadProgress(0);
    
    // 模拟文件上传和解析过程
    const uploadInterval = setInterval(() => {
      setUploadProgress(prev => {
        if (prev >= 100) {
          clearInterval(uploadInterval);
          return 100;
        }
        return prev + 10;
      });
    }, 200);

    // 模拟文档内容提取
    setTimeout(() => {
      const sampleContent = `
# ${file.name.split('.')[0]}

## 摘要
这是从上传文档中提取的内容。本研究探讨了现代技术在学术研究中的应用...

## 引言
随着技术的发展，学术研究方法正在发生深刻变化...

## 方法
本研究采用了定量和定性相结合的方法...

## 结果
研究结果表明...

## 讨论
基于以上结果，我们可以得出...

## 结论
本研究为相关领域提供了新的见解...
      `;
      
      setDocumentContent(sampleContent);
      
      // 基于文档内容生成PPT大纲
      generatePPTFromDocument(sampleContent, file.name);
    }, 2000);
  };

  // 基于文档生成PPT
  const generatePPTFromDocument = (content: string, fileName: string) => {
    const sections = content.split('#').filter(section => section.trim());
    const slides: PPTSlide[] = sections.map((section, index) => ({
      id: `slide_${index + 1}`,
      title: section.split('\n')[0].trim(),
      content: section.split('\n').slice(1).join('\n').trim(),
      layout: index === 0 ? 'title' : 'content',
      order: index + 1,
      elements: [],
      notes: `从文档${fileName}自动生成`
    }));

    const newProject: PPTProject = {
      id: Date.now().toString(),
      title: `基于${fileName}生成的PPT`,
      template: '学术报告标准版',
      slides: slides,
      theme: 'academic',
      status: 'draft',
      createdAt: new Date(),
      lastModified: new Date(),
      vbaCode: generateSampleVBACode()
    };

    setProjects(prev => [newProject, ...prev]);
    setCurrentProject(newProject);
    setActiveTab('edit');
  };

  // VBA代码生成函数
  function generateVBACode(type: 'animation' | 'chart' | 'automation' = 'animation'): string {
    switch (type) {
      case 'animation':
        return `' PowerPoint VBA - 幻灯片动画代码
Sub AddSlideAnimations()
    Dim slide As Slide
    Dim shape As Shape
    Dim effect As Effect
    
    Set slide = ActivePresentation.Slides(1)
    
    ' 为标题添加淡入效果
    Set shape = slide.Shapes("Title 1")
    Set effect = slide.TimeLine.MainSequence.AddEffect(shape, msoAnimEffectFade)
    effect.Timing.Duration = 1.5
    
    ' 为内容添加擦除效果
    Set shape = slide.Shapes("Content Placeholder 2")
    Set effect = slide.TimeLine.MainSequence.AddEffect(shape, msoAnimEffectWipe)
    effect.Timing.Duration = 2
    effect.Timing.TriggerDelayTime = 1
    
    MsgBox "动画效果已添加!"
End Sub`;

      case 'chart':
        return `' PowerPoint VBA - 图表生成代码
Sub CreateDataChart()
    Dim slide As Slide
    Dim chart As Chart
    Dim chartData As ChartData
    
    Set slide = ActivePresentation.Slides.Add(2, ppLayoutChart)
    
    ' 创建柱状图
    Set chart = slide.Shapes.AddChart2(227, xlColumnClustered).Chart
    
    ' 设置图表数据
    chart.ChartData.Workbook.Worksheets(1).Cells(1, 1) = "类别"
    chart.ChartData.Workbook.Worksheets(1).Cells(1, 2) = "数值"
    chart.ChartData.Workbook.Worksheets(1).Cells(2, 1) = "A组"
    chart.ChartData.Workbook.Worksheets(1).Cells(2, 2) = 25
    chart.ChartData.Workbook.Worksheets(1).Cells(3, 1) = "B组"
    chart.ChartData.Workbook.Worksheets(1).Cells(3, 2) = 35
    
    ' 设置图表样式
    chart.ChartTitle.Text = "数据分析结果"
    chart.Axes(xlCategory).CategoryNames = Array("A组", "B组")
    
    MsgBox "图表已创建!"
End Sub`;

      case 'automation':
        return `' PowerPoint VBA - 自动化脚本
Sub AutoFormatPresentation()
    Dim slide As Slide
    Dim shape As Shape
    
    ' 遍历所有幻灯片
    For Each slide In ActivePresentation.Slides
        ' 设置统一字体
        For Each shape In slide.Shapes
            If shape.HasTextFrame Then
                With shape.TextFrame.TextRange.Font
                    .Name = "Microsoft YaHei"
                    .Size = 18
                    .Color.RGB = RGB(51, 51, 51)
                End With
            End If
        Next shape
        
        ' 设置幻灯片背景
        slide.Background.Fill.ForeColor.RGB = RGB(248, 249, 250)
    Next slide
    
    MsgBox "格式化完成!"
End Sub`;

      default:
        return generateSampleVBACode();
    }
  }

  // 示例VBA代码
  function generateSampleVBACode(): string {
    return `' PowerPoint VBA - 示例代码
Sub EnhancePresentation()
    ' 自动优化演示文稿
    Dim slide As Slide
    
    For Each slide In ActivePresentation.Slides
        ' 添加页码
        slide.HeadersFooters.SlideNumber.Visible = True
        
        ' 设置过渡效果
        slide.SlideShowTransition.Type = ppEffectFade
        slide.SlideShowTransition.Duration = 1
    Next slide
    
    MsgBox "演示文稿已优化!"
End Sub`;
  }

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const stats = {
    totalProjects: projects.length,
    inProgress: projects.filter(p => p.status === 'editing').length,
    completed: projects.filter(p => p.status === 'completed').length,
    totalSlides: projects.reduce((acc, p) => acc + p.slides.length, 0)
  };

  const handleCreateFromTemplate = (templateId: string) => {
    const template = templates.find(t => t.id === templateId);
    if (!template) return;

    console.log('Creating from template:', templateId); // 添加调试日志

    // 生成示例幻灯片
    const sampleSlides: PPTSlide[] = [
      {
        id: 'slide1',
        title: '研究题目',
        content: '在此输入您的研究题目和副标题',
        layout: 'title',
        order: 1,
        elements: [],
        notes: '开场幻灯片，介绍研究主题'
      },
      {
        id: 'slide2',
        title: '研究背景',
        content: '• 研究领域的现状\n• 存在的问题\n• 研究的必要性',
        layout: 'content',
        order: 2,
        elements: [],
        notes: '阐述研究的背景和意义'
      },
      {
        id: 'slide3',
        title: '研究目标',
        content: '• 主要目标\n• 具体目标\n• 预期成果',
        layout: 'content',
        order: 3,
        elements: [],
        notes: '明确研究要达到的目标'
      },
      {
        id: 'slide4',
        title: '研究方法',
        content: '• 研究方法论\n• 数据收集方法\n• 分析方法',
        layout: 'content',
        order: 4,
        elements: [],
        notes: '详细说明研究方法'
      },
      {
        id: 'slide5',
        title: '研究结果',
        content: '[在此插入图表和数据分析]',
        layout: 'chart',
        order: 5,
        elements: [],
        notes: '展示主要研究发现'
      }
    ];

    const newProject: PPTProject = {
      id: Date.now().toString(),
      title: `新建${template.title}`,
      template: template.title,
      slides: sampleSlides,
      theme: template.theme,
      status: 'draft',
      createdAt: new Date(),
      lastModified: new Date(),
      vbaCode: generateVBACode()
    };

    setProjects(prev => [newProject, ...prev]);
    setCurrentProject(newProject);
    setSelectedTemplate(template); // 设置选中的模板
    setActiveTab('edit'); // 切换到编辑模式
    
    // 添加成功提示
    console.log('Template created successfully, switching to edit mode');
  };

  const handleAiGenerate = async () => {
    if (!aiPrompt.trim()) return;
    
    setIsGenerating(true);
    
    // 模拟AI生成过程
    setTimeout(() => {
      const slideTopics = [
        '研究概述',
        '文献综述',
        '研究方法',
        '实验设计',
        '数据分析',
        '结果讨论',
        '结论与展望'
      ];

      const aiSlides: PPTSlide[] = slideTopics.map((topic, index) => ({
        id: `ai_slide_${index + 1}`,
        title: topic,
        content: `基于AI分析生成的${topic}内容。这里包含了针对"${aiPrompt}"主题的专业分析和见解。`,
        layout: index === 0 ? 'title' : (index === 4 ? 'chart' : 'content'),
        order: index + 1,
        elements: [],
        notes: `AI生成的${topic}演讲备注`
      }));

      const newProject: PPTProject = {
        id: Date.now().toString(),
        title: `AI生成：${aiPrompt}`,
        template: 'AI智能生成',
        slides: aiSlides,
        theme: 'ai-generated',
        status: 'draft',
        createdAt: new Date(),
        lastModified: new Date(),
        vbaCode: generateVBACode('automation')
      };

      setProjects(prev => [newProject, ...prev]);
      setCurrentProject(newProject);
      setIsGenerating(false);
      setAiPrompt('');
      setShowAiPanel(false);
      setActiveTab('edit');
    }, 3000);
  };

  // 编辑单个幻灯片
  const editSlide = (slideId: string) => {
    if (!currentProject) return;
    
    const slide = currentProject.slides.find(s => s.id === slideId);
    if (slide) {
      setSelectedSlide(slide);
      setIsEditing(true);
    }
  };

  // 重新生成单个幻灯片
  const regenerateSlide = async (slideId: string) => {
    if (!currentProject) return;
    
    // 模拟AI重新生成幻灯片内容
    const slide = currentProject.slides.find(s => s.id === slideId);
    if (!slide) return;

    const regeneratedContent = `重新生成的${slide.title}内容：
• 更新的观点和分析
• 最新的研究数据
• 改进的表达方式
• 更清晰的逻辑结构`;

    const updatedSlides = currentProject.slides.map(s => 
      s.id === slideId 
        ? { ...s, content: regeneratedContent, notes: `${slide.notes} - 已重新生成` }
        : s
    );

    const updatedProject = {
      ...currentProject,
      slides: updatedSlides,
      lastModified: new Date()
    };

    setCurrentProject(updatedProject);
    setProjects(prev => prev.map(p => p.id === currentProject.id ? updatedProject : p));
  };

  // 保存幻灯片编辑
  const saveSlideEdit = (updatedSlide: PPTSlide) => {
    if (!currentProject) return;

    const updatedSlides = currentProject.slides.map(s => 
      s.id === updatedSlide.id ? updatedSlide : s
    );

    const updatedProject = {
      ...currentProject,
      slides: updatedSlides,
      lastModified: new Date()
    };

    setCurrentProject(updatedProject);
    setProjects(prev => prev.map(p => p.id === currentProject.id ? updatedProject : p));
    setSelectedSlide(null);
    setIsEditing(false);
  };

  // 导出PPT
  const exportPPT = () => {
    if (!currentProject) return;
    
    // 模拟PPT导出
    const pptContent = `PPT标题: ${currentProject.title}
模板: ${currentProject.template}
幻灯片数量: ${currentProject.slides.length}

幻灯片内容:
${currentProject.slides.map((slide, index) => 
  `${index + 1}. ${slide.title}
     内容: ${slide.content}
     备注: ${slide.notes || '无'}
`).join('\n')}

VBA代码:
${currentProject.vbaCode || '无'}`;
    
    const blob = new Blob([pptContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${currentProject.title}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getCategoryIcon = (category: PPTTemplate['category']) => {
    switch (category) {
      case 'business': return <Building className="h-4 w-4" />;
      case 'academic': return <BookOpen className="h-4 w-4" />;
      case 'science': return <Atom className="h-4 w-4" />;
      case 'medical': return <Stethoscope className="h-4 w-4" />;
      case 'engineering': return <Calculator className="h-4 w-4" />;
      case 'arts': return <PenTool className="h-4 w-4" />;
      case 'creative': return <Palette className="h-4 w-4" />;
      case 'minimal': return <Layout className="h-4 w-4" />;
      default: return <Presentation className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: PPTProject['status']) => {
    switch (status) {
      case 'draft': return 'text-gray-600 bg-gray-100';
      case 'editing': return 'text-orange-600 bg-orange-100';
      case 'completed': return 'text-green-600 bg-green-100';
    }
  };

  return (
    <div className="h-full flex flex-col bg-gradient-to-br from-purple-50 to-pink-50">
      {/* Header */}
      <div className="flex-shrink-0 border-b border-purple-200 bg-white/80 backdrop-blur-sm">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-xl font-semibold text-slate-900">AI PPT生成器</h1>
              <p className="text-sm text-slate-600 mt-1">
                专业学术PPT模板，支持VBA代码生成和智能编辑
              </p>
            </div>
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setActiveTab('templates')}
                className={activeTab === 'templates' ? 'bg-purple-50 border-purple-200' : ''}
              >
                <Layout className="h-4 w-4 mr-2" />
                模板库
              </Button>
              <Button
                variant="outline" 
                size="sm"
                onClick={() => setActiveTab('projects')}
                className={activeTab === 'projects' ? 'bg-purple-50 border-purple-200' : ''}
              >
                <Presentation className="h-4 w-4 mr-2" />
                我的项目
              </Button>
              <Button
                onClick={() => setActiveTab('create')}
                className="bg-gradient-to-r from-purple-600 to-pink-600 text-white"
              >
                <Plus className="h-4 w-4 mr-2" />
                创建PPT
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-hidden">
        <AnimatePresence mode="wait">
          {activeTab === 'templates' && (
            <motion.div
              key="templates"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              className="h-full flex flex-col"
            >
              {/* Search and Filter Bar */}
              <div className="p-6 border-b border-slate-200 bg-white/50">
                <div className="flex flex-col lg:flex-row gap-4 mb-6">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                      <Input
                        placeholder="搜索模板或输入关键词..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>
                  <Button
                    onClick={() => setShowAiPanel(true)}
                    className="bg-gradient-to-r from-purple-600 to-pink-600 text-white hover:from-purple-700 hover:to-pink-700 flex items-center gap-2 whitespace-nowrap"
                  >
                    <Sparkles className="h-4 w-4" />
                    AI智能生成
                  </Button>
                </div>

                {/* Category Filter */}
                <div className="flex flex-wrap gap-2">
                  <Button
                    variant={selectedCategory === 'all' ? 'primary' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedCategory('all')}
                    className="whitespace-nowrap"
                  >
                    全部
                  </Button>
                  <Button
                    variant={selectedCategory === 'academic' ? 'primary' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedCategory('academic')}
                    className="whitespace-nowrap"
                  >
                    理工科
                  </Button>
                  <Button
                    variant={selectedCategory === 'arts' ? 'primary' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedCategory('arts')}
                    className="whitespace-nowrap"
                  >
                    文科
                  </Button>
                  <Button
                    variant={selectedCategory === 'medical' ? 'primary' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedCategory('medical')}
                    className="whitespace-nowrap"
                  >
                    医学
                  </Button>
                  <Button
                    variant={selectedCategory === 'engineering' ? 'primary' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedCategory('engineering')}
                    className="whitespace-nowrap"
                  >
                    工程
                  </Button>
                  <Button
                    variant={selectedCategory === 'creative' ? 'primary' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedCategory('creative')}
                    className="whitespace-nowrap"
                  >
                    创意
                  </Button>
                  <Button
                    variant={selectedCategory === 'business' ? 'primary' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedCategory('business')}
                    className="whitespace-nowrap"
                  >
                    商科
                  </Button>
                  <Button
                    variant={selectedCategory === 'minimal' ? 'primary' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedCategory('minimal')}
                    className="whitespace-nowrap"
                  >
                    简约
                  </Button>
                </div>

                {/* Academic Field Quick Filters */}
                <div className="mt-4">
                  <h3 className="text-sm font-medium text-slate-700 mb-2">学术领域模板</h3>
                  <div className="flex flex-wrap gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSelectedCategory('academic')}
                      className="whitespace-nowrap"
                    >
                      理工科
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSelectedCategory('medical')}
                      className="whitespace-nowrap"
                    >
                      医学
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSelectedCategory('engineering')}
                      className="whitespace-nowrap"
                    >
                      工程
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSelectedCategory('arts')}
                      className="whitespace-nowrap"
                    >
                      文科
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSelectedCategory('science')}
                      className="whitespace-nowrap"
                    >
                      数据科学
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSelectedCategory('science')}
                      className="whitespace-nowrap"
                    >
                      化学
                    </Button>
                  </div>
                </div>
              </div>

              {/* Templates Grid */}
              <div className="flex-1 overflow-y-auto p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {filteredTemplates.map((template) => (
                    <motion.div
                      key={template.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      whileHover={{ y: -5, scale: 1.02 }}
                      className="group cursor-pointer"
                    >
                      <Card className="overflow-hidden hover:shadow-xl transition-all duration-300 border-2 hover:border-purple-200">
                        <CardContent className="p-0">
                          {/* Template Preview with PPT Background */}
                          <div className="aspect-video bg-gradient-to-br from-slate-100 via-white to-slate-50 relative overflow-hidden border-b">
                            {/* PPT Background Pattern */}
                            <div className="absolute inset-0 opacity-10">
                              <div className="grid grid-cols-8 grid-rows-6 h-full">
                                {Array.from({ length: 48 }).map((_, i) => (
                                  <div key={i} className="border border-slate-300" />
                                ))}
                              </div>
                            </div>
                            
                            {/* PPT Slide Content */}
                            <div className="absolute inset-4 bg-white rounded-lg shadow-lg flex flex-col justify-center items-center p-4 border">
                              <div className="w-full h-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full mb-4" />
                              <Presentation className="h-8 w-8 text-slate-400 mb-2" />
                              <div className="text-center">
                                <div className="h-2 bg-slate-200 rounded w-24 mx-auto mb-2" />
                                <div className="h-1 bg-slate-100 rounded w-16 mx-auto mb-1" />
                                <div className="h-1 bg-slate-100 rounded w-20 mx-auto" />
                              </div>
                            </div>
                            
                            {/* Category Badge */}
                            <div className={`absolute top-2 right-2 px-2 py-1 rounded-full text-xs font-medium ${template.color} text-white flex items-center justify-center`}>
                              {getCategoryIcon(template.category)}
                            </div>
                            
                            {/* Slide Count */}
                            <div className="absolute bottom-2 left-2 bg-white/90 backdrop-blur-sm rounded-full px-2 py-1 text-xs font-medium text-slate-600">
                              {template.slides}页
                            </div>
                          </div>
                          
                          {/* Template Info */}
                          <div className="p-4">
                            <div className="flex items-center justify-between mb-2">
                              <h3 className="font-semibold text-slate-900 text-sm">{template.title}</h3>
                              <span className="text-xs text-slate-500">{template.category}</span>
                            </div>
                            <p className="text-xs text-slate-600 mb-3 line-clamp-2">{template.description}</p>
                            
                            {/* Tags */}
                            <div className="flex flex-wrap gap-1 mb-3">
                              {template.tags.slice(0, 3).map((tag) => (
                                <span key={tag} className="px-2 py-1 bg-slate-100 text-slate-600 text-xs rounded-full">
                                  {tag}
                                </span>
                              ))}
                            </div>
                            
                            {/* Action Buttons */}
                            <div className="flex items-center gap-2">
                              <Button
                                variant="primary"
                                size="sm"
                                onClick={() => handleCreateFromTemplate(template.id)}
                                className="flex-1 h-8 flex items-center justify-center gap-2 whitespace-nowrap"
                              >
                                <Plus className="w-4 h-4 flex-shrink-0" />
                                <span className="text-sm">使用模板</span>
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                className="w-8 h-8 p-0 flex items-center justify-center flex-shrink-0"
                              >
                                <Eye className="w-4 h-4" />
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  ))}
                </div>
              </div>
            </motion.div>
          )}

          {activeTab === 'projects' && (
            <motion.div
              key="projects"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              className="h-full flex flex-col"
            >
              <div className="flex-1 overflow-y-auto p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {projects.map((project) => (
                    <motion.div
                      key={project.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      whileHover={{ y: -5 }}
                    >
                      <Card className="overflow-hidden hover:shadow-lg transition-all duration-300">
                        <div className="aspect-video bg-gradient-to-br from-blue-100 to-purple-100 relative overflow-hidden">
                          <div className="absolute inset-0 flex items-center justify-center">
                            <FileText className="h-12 w-12 text-blue-400" />
                          </div>
                          <div className="absolute top-2 right-2 bg-white/80 backdrop-blur-sm rounded-full px-2 py-1 text-xs font-medium">
                            {project.slides.length}页
                          </div>
                          <div className={`absolute top-2 left-2 rounded-full px-2 py-1 text-xs font-medium ${
                            project.status === 'completed' ? 'bg-green-100 text-green-600' :
                            project.status === 'editing' ? 'bg-yellow-100 text-yellow-600' :
                            'bg-slate-100 text-slate-600'
                          }`}>
                            {statusNames[project.status]}
                          </div>
                        </div>
                        
                        <div className="p-4">
                          <h3 className="font-semibold text-slate-900 mb-2">{project.title}</h3>
                          <p className="text-sm text-slate-600 mb-2">模板：{project.template}</p>
                          <p className="text-xs text-slate-500 mb-3">
                            最后修改：{project.lastModified.toLocaleDateString()}
                          </p>
                          
                          <div className="flex gap-2">
                            <Button variant="outline" size="sm" className="flex-1 h-8 flex items-center justify-center gap-2 whitespace-nowrap">
                              <Edit3 className="h-4 w-4 flex-shrink-0" />
                              <span className="text-sm">编辑</span>
                            </Button>
                            <Button variant="outline" size="sm" className="w-8 h-8 p-0 flex items-center justify-center flex-shrink-0">
                              <Download className="h-4 w-4" />
                            </Button>
                            <Button variant="outline" size="sm" className="w-8 h-8 p-0 flex items-center justify-center flex-shrink-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </Card>
                    </motion.div>
                  ))}
                </div>
              </div>
            </motion.div>
          )}

          {activeTab === 'create' && (
            <motion.div
              key="create"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              className="h-full flex"
            >
              {/* Left Panel - Creation Options */}
              <div className="w-80 border-r border-slate-200 bg-white/50 flex flex-col">
                <div className="p-6 border-b border-slate-200">
                  <h2 className="text-lg font-semibold text-slate-900 mb-4">创建PPT</h2>
                  
                  {selectedTemplate && (
                    <div className="mb-4 p-3 bg-purple-50 rounded-lg border border-purple-200">
                      <div className="flex items-center gap-2 mb-2">
                        <Layout className="h-4 w-4 text-purple-600" />
                        <span className="text-sm font-medium text-purple-900">使用模板</span>
                      </div>
                      <p className="text-sm text-purple-700">{selectedTemplate.title}</p>
                    </div>
                  )}
                  
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-slate-700 mb-2">
                        PPT标题
                      </label>
                      <Input placeholder="输入PPT标题..." />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-slate-700 mb-2">
                        创建方式
                      </label>
                      <div className="space-y-2">
                        <Button
                          variant="outline"
                          className="w-full flex items-center justify-start gap-2 whitespace-nowrap h-8"
                          onClick={() => fileInputRef.current?.click()}
                        >
                          <Upload className="h-4 w-4 flex-shrink-0" />
                          <span className="text-sm">上传文档生成</span>
                        </Button>
                        <Button variant="outline" className="w-full flex items-center justify-start gap-2 whitespace-nowrap h-8">
                          <Edit3 className="h-4 w-4 flex-shrink-0" />
                          <span className="text-sm">手动创建大纲</span>
                        </Button>
                        <Button 
                          variant="outline" 
                          className="w-full flex items-center justify-start gap-2 whitespace-nowrap h-8"
                          onClick={() => setShowAiPanel(true)}
                        >
                          <Sparkles className="h-4 w-4 flex-shrink-0" />
                          <span className="text-sm">AI智能生成</span>
                        </Button>
                      </div>
                      
                      {/* 上传进度显示 */}
                      {uploadProgress > 0 && uploadProgress < 100 && (
                        <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                          <div className="flex items-center gap-2 mb-2">
                            <Upload className="h-4 w-4 text-blue-600" />
                            <span className="text-sm font-medium text-blue-900">文档上传中...</span>
                          </div>
                          <div className="w-full bg-blue-200 rounded-full h-2">
                            <div 
                              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${uploadProgress}%` }}
                            />
                          </div>
                          <p className="text-xs text-blue-700 mt-1">{uploadProgress}% 完成</p>
                        </div>
                      )}
                      
                      {documentContent && (
                        <div className="mt-4 p-3 bg-green-50 rounded-lg border border-green-200">
                          <div className="flex items-center gap-2 mb-2">
                            <FileText className="h-4 w-4 text-green-600" />
                            <span className="text-sm font-medium text-green-900">文档解析完成</span>
                          </div>
                          <p className="text-xs text-green-700">已自动生成PPT大纲，请查看编辑界面</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                
                {/* VBA Code Generator */}
                <div className="p-6 border-b border-slate-200">
                  <h3 className="text-sm font-semibold text-slate-900 mb-3">VBA代码生成</h3>
                  <div className="space-y-2">
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="w-full flex items-center justify-start gap-2 whitespace-nowrap h-8"
                      onClick={() => setGeneratedVBA(generateVBACode('animation'))}
                    >
                      <Code className="h-4 w-4 flex-shrink-0" />
                      <span className="text-sm">生成动画代码</span>
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="w-full flex items-center justify-start gap-2 whitespace-nowrap h-8"
                      onClick={() => setGeneratedVBA(generateVBACode('automation'))}
                    >
                      <RefreshCw className="h-4 w-4 flex-shrink-0" />
                      <span className="text-sm">自动化脚本</span>
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="w-full flex items-center justify-start gap-2 whitespace-nowrap h-8"
                      onClick={() => setGeneratedVBA(generateVBACode('chart'))}
                    >
                      <BarChart3 className="h-4 w-4 flex-shrink-0" />
                      <span className="text-sm">图表生成代码</span>
                    </Button>
                  </div>
                </div>

                <div className="flex-1 p-6">
                  <div className="bg-slate-50 rounded-lg p-3 border">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-xs font-medium text-slate-600">VBA代码预览</span>
                      <Button 
                        size="sm" 
                        variant="ghost" 
                        className="h-6 w-6 p-0"
                        onClick={() => {
                          navigator.clipboard.writeText(generatedVBA || generateSampleVBACode());
                        }}
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                    </div>
                    <pre className="text-xs text-slate-700 overflow-auto max-h-32">
                      {(generatedVBA || generateSampleVBACode()).slice(0, 400)}...
                    </pre>
                  </div>
                </div>
              </div>

              {/* Right Panel - Preview */}
              <div className="flex-1 flex flex-col">
                <div className="p-6 border-b border-slate-200 bg-white/50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <h3 className="text-lg font-semibold text-slate-900">PPT预览</h3>
                      <div className="flex items-center gap-2">
                        <Button variant="outline" size="sm">
                          <ChevronLeft className="h-4 w-4" />
                        </Button>
                        <span className="text-sm text-slate-600">{currentSlide} / 15</span>
                        <Button variant="outline" size="sm">
                          <ChevronRight className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm" className="flex items-center gap-2 whitespace-nowrap h-8">
                        <Palette className="h-4 w-4 flex-shrink-0" />
                        <span className="text-sm">样式</span>
                      </Button>
                      <Button variant="outline" size="sm" className="flex items-center gap-2 whitespace-nowrap h-8">
                        <Settings className="h-4 w-4 flex-shrink-0" />
                        <span className="text-sm">设置</span>
                      </Button>
                      <Button className="bg-gradient-to-r from-purple-600 to-pink-600 text-white flex items-center gap-2 whitespace-nowrap h-8">
                        <Download className="h-4 w-4 flex-shrink-0" />
                        <span className="text-sm">导出PPT</span>
                      </Button>
                    </div>
                  </div>
                </div>

                <div className="flex-1 p-6 overflow-auto">
                  <div className="max-w-4xl mx-auto">
                    <Card className="aspect-video bg-white border-2 border-slate-200 shadow-lg">
                      <div className="h-full flex items-center justify-center bg-gradient-to-br from-purple-50 to-pink-50">
                        <div className="text-center">
                          <Presentation className="h-16 w-16 text-purple-400 mx-auto mb-4" />
                          <h2 className="text-2xl font-bold text-slate-800 mb-2">幻灯片标题</h2>
                          <p className="text-slate-600">这里是幻灯片内容预览区域</p>
                        </div>
                      </div>
                    </Card>
                    
                    {/* Slide Thumbnails */}
                    <div className="mt-6">
                      <h4 className="text-sm font-medium text-slate-700 mb-3">幻灯片缩略图</h4>
                      <div className="flex gap-2 overflow-x-auto pb-2">
                        {Array.from({ length: 15 }, (_, i) => (
                          <div
                            key={i}
                            className={`flex-shrink-0 w-24 h-16 bg-white border-2 rounded cursor-pointer hover:border-purple-300 transition-colors ${
                              currentSlide === i + 1 ? 'border-purple-500' : 'border-slate-200'
                            }`}
                            onClick={() => setCurrentSlide(i + 1)}
                          >
                            <div className="h-full flex items-center justify-center">
                              <span className="text-xs text-slate-500">{i + 1}</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileUpload}
                multiple
                accept=".doc,.docx,.pdf,.txt,.md"
                className="hidden"
              />
            </motion.div>
          )}

          {activeTab === 'edit' && currentProject && (
            <motion.div
              key="edit"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              className="h-full flex"
            >
              {/* Left Panel - Slide List */}
              <div className="w-80 border-r border-slate-200 bg-white/50 flex flex-col">
                <div className="p-4 border-b border-slate-200">
                  <div className="flex items-center justify-between mb-4">
                    <h2 className="text-lg font-semibold text-slate-900">幻灯片管理</h2>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm" className="flex items-center gap-2 whitespace-nowrap h-8">
                        <Plus className="h-4 w-4 flex-shrink-0" />
                        <span className="text-sm">新增</span>
                      </Button>
                      <Button variant="outline" size="sm" onClick={exportPPT} className="flex items-center gap-2 whitespace-nowrap h-8">
                        <Download className="h-4 w-4 flex-shrink-0" />
                        <span className="text-sm">导出</span>
                      </Button>
                    </div>
                  </div>
                  
                  <div className="bg-purple-50 rounded-lg p-3 border border-purple-200">
                    <div className="flex items-center gap-2 mb-2">
                      <Layout className="h-4 w-4 text-purple-600" />
                      <span className="text-sm font-medium text-purple-900">{currentProject.title}</span>
                    </div>
                    <p className="text-xs text-purple-700">模板：{currentProject.template}</p>
                    <p className="text-xs text-purple-600">共 {currentProject.slides.length} 张幻灯片</p>
                  </div>
                </div>

                {/* Slide List */}
                <div className="flex-1 overflow-y-auto">
                  {currentProject.slides.map((slide, index) => (
                    <div
                      key={slide.id}
                      className={`p-4 border-b border-slate-200 cursor-pointer hover:bg-slate-50 transition-colors ${
                        selectedSlide?.id === slide.id ? 'bg-purple-50 border-purple-200' : ''
                      }`}
                      onClick={() => setSelectedSlide(slide)}
                    >
                      <div className="flex items-start gap-3">
                        <div className="flex-shrink-0 w-8 h-8 bg-slate-100 rounded-lg flex items-center justify-center text-sm font-medium text-slate-600">
                          {index + 1}
                        </div>
                        <div className="flex-1 min-w-0">
                          <h4 className="font-medium text-slate-900 text-sm mb-1 truncate">{slide.title}</h4>
                          <p className="text-xs text-slate-600 line-clamp-2 mb-2">{slide.content}</p>
                          <div className="flex items-center gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                editSlide(slide.id);
                              }}
                              className="text-xs px-2 py-1 h-6 flex items-center gap-1 whitespace-nowrap"
                            >
                              <Edit3 className="h-3 w-3 flex-shrink-0" />
                              <span className="text-xs">编辑</span>
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                regenerateSlide(slide.id);
                              }}
                              className="text-xs px-2 py-1 h-6 flex items-center gap-1 whitespace-nowrap"
                            >
                              <RefreshCw className="h-3 w-3 flex-shrink-0" />
                              <span className="text-xs">重新生成</span>
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Right Panel - Edit/Preview */}
              <div className="flex-1 flex flex-col">
                {isEditing && selectedSlide ? (
                  // Edit Mode
                  <div className="h-full flex">
                    {/* Edit Panel */}
                    <div className="w-1/2 border-r border-slate-200 flex flex-col">
                      <div className="p-4 border-b border-slate-200 bg-white/50">
                        <div className="flex items-center justify-between">
                          <h3 className="text-lg font-semibold text-slate-900">编辑幻灯片</h3>
                          <div className="flex items-center gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                setIsEditing(false);
                                setSelectedSlide(null);
                              }}
                              className="flex items-center gap-2 whitespace-nowrap h-8"
                            >
                              <X className="h-4 w-4 flex-shrink-0" />
                              <span className="text-sm">取消</span>
                            </Button>
                            <Button
                              variant="primary"
                              size="sm"
                              onClick={() => {
                                if (selectedSlide) {
                                  saveSlideEdit(selectedSlide);
                                }
                              }}
                              className="flex items-center gap-2 whitespace-nowrap h-8"
                            >
                              <Save className="h-4 w-4 flex-shrink-0" />
                              <span className="text-sm">保存</span>
                            </Button>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex-1 p-4 overflow-y-auto">
                        <div className="space-y-4">
                          <div>
                            <label className="block text-sm font-medium text-slate-700 mb-2">
                              幻灯片标题
                            </label>
                            <Input
                              value={selectedSlide.title}
                              onChange={(e) => setSelectedSlide({
                                ...selectedSlide,
                                title: e.target.value
                              })}
                              placeholder="输入幻灯片标题..."
                            />
                          </div>
                          
                          <div>
                            <label className="block text-sm font-medium text-slate-700 mb-2">
                              幻灯片内容
                            </label>
                            <textarea
                              value={selectedSlide.content}
                              onChange={(e) => setSelectedSlide({
                                ...selectedSlide,
                                content: e.target.value
                              })}
                              placeholder="输入幻灯片内容..."
                              className="w-full h-40 p-3 border border-slate-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                            />
                          </div>
                          
                          <div>
                            <label className="block text-sm font-medium text-slate-700 mb-2">
                              演讲备注
                            </label>
                            <textarea
                              value={selectedSlide.notes || ''}
                              onChange={(e) => setSelectedSlide({
                                ...selectedSlide,
                                notes: e.target.value
                              })}
                              placeholder="输入演讲备注..."
                              className="w-full h-24 p-3 border border-slate-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    {/* Preview Panel */}
                    <div className="w-1/2 flex flex-col">
                      <div className="p-4 border-b border-slate-200 bg-white/50">
                        <h3 className="text-lg font-semibold text-slate-900">实时预览</h3>
                      </div>
                      
                      <div className="flex-1 p-4 bg-slate-50">
                        <Card className="aspect-video bg-white border-2 border-slate-200 shadow-lg">
                          <div className="h-full flex flex-col justify-center items-center p-6">
                            <h2 className="text-xl font-bold text-slate-800 mb-4 text-center">{selectedSlide.title}</h2>
                            <div className="text-slate-600 text-center whitespace-pre-line">{selectedSlide.content}</div>
                          </div>
                        </Card>
                      </div>
                    </div>
                  </div>
                ) : (
                  // Preview Mode
                  <div className="h-full flex flex-col">
                    <div className="p-4 border-b border-slate-200 bg-white/50">
                      <div className="flex items-center justify-between">
                        <h3 className="text-lg font-semibold text-slate-900">PPT预览</h3>
                        <div className="flex items-center gap-4">
                          <div className="flex items-center gap-2">
                            <Button variant="outline" size="sm" className="whitespace-nowrap">
                              <ChevronLeft className="h-4 w-4" />
                            </Button>
                            <span className="text-sm text-slate-600">
                              {selectedSlide ? currentProject.slides.findIndex(s => s.id === selectedSlide.id) + 1 : 1} / {currentProject.slides.length}
                            </span>
                            <Button variant="outline" size="sm" className="whitespace-nowrap">
                              <ChevronRight className="h-4 w-4" />
                            </Button>
                          </div>
                          
                          <div className="flex items-center gap-2">
                            <Button variant="outline" size="sm" className="flex items-center gap-2 whitespace-nowrap h-8">
                              <Palette className="h-4 w-4 flex-shrink-0" />
                              <span className="text-sm">样式</span>
                            </Button>
                            <Button variant="outline" size="sm" className="flex items-center gap-2 whitespace-nowrap h-8">
                              <Settings className="h-4 w-4 flex-shrink-0" />
                              <span className="text-sm">设置</span>
                            </Button>
                            <Button 
                              onClick={exportPPT}
                              className="bg-gradient-to-r from-purple-600 to-pink-600 text-white flex items-center gap-2 whitespace-nowrap h-8"
                            >
                              <Download className="h-4 w-4 flex-shrink-0" />
                              <span className="text-sm">导出PPT</span>
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="flex-1 p-6 overflow-auto bg-slate-50">
                      <div className="max-w-4xl mx-auto">
                        <Card className="aspect-video bg-white border-2 border-slate-200 shadow-lg">
                          <div className="h-full flex flex-col justify-center items-center p-8 bg-gradient-to-br from-purple-50 to-pink-50">
                            {selectedSlide ? (
                              <>
                                <h2 className="text-3xl font-bold text-slate-800 mb-6 text-center">{selectedSlide.title}</h2>
                                <div className="text-slate-600 text-center text-lg whitespace-pre-line max-w-2xl">{selectedSlide.content}</div>
                              </>
                            ) : (
                              <>
                                <Presentation className="h-16 w-16 text-purple-400 mb-4" />
                                <h2 className="text-2xl font-bold text-slate-800 mb-2">选择幻灯片进行预览</h2>
                                <p className="text-slate-600">点击左侧幻灯片列表中的任意幻灯片</p>
                              </>
                            )}
                          </div>
                        </Card>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </motion.div>
          )}

          {activeTab === 'edit' && !currentProject && (
            <motion.div
              key="edit-empty"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              className="h-full flex items-center justify-center"
            >
              <div className="text-center">
                <FileText className="h-16 w-16 text-slate-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-slate-900 mb-2">没有选择项目</h3>
                <p className="text-slate-600 mb-4">请先选择一个模板创建PPT项目</p>
                <Button onClick={() => setActiveTab('templates')}>
                  <Layout className="h-4 w-4 mr-2" />
                  选择模板
                </Button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* AI创作面板 */}
      <AnimatePresence>
        {showAiPanel && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-xl p-6 w-full max-w-md mx-4"
            >
              <div className="flex items-center gap-2 mb-4">
                <Bot className="h-5 w-5 text-purple-600" />
                <h3 className="text-lg font-semibold">AI 智能创作</h3>
              </div>
              
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-slate-700 mb-2 block">
                    描述你的演示主题
                  </label>
                  <textarea
                    value={aiPrompt}
                    onChange={(e) => setAiPrompt(e.target.value)}
                    placeholder="例如：关于机器学习在医疗诊断中应用的学术报告..."
                    className="w-full h-24 p-3 border border-slate-200 rounded-lg text-sm resize-none focus:outline-none focus:ring-2 focus:ring-purple-500/20"
                  />
                </div>
                
                <div className="flex justify-end gap-3">
                  <Button 
                    variant="outline" 
                    onClick={() => setShowAiPanel(false)}
                  >
                    取消
                  </Button>
                  <Button 
                    onClick={handleAiGenerate}
                    disabled={!aiPrompt.trim() || isGenerating}
                  >
                    {isGenerating ? (
                      <>
                        <Sparkles className="h-4 w-4 mr-2 animate-spin" />
                        生成中...
                      </>
                    ) : (
                      <>
                        <Wand2 className="h-4 w-4 mr-2" />
                        开始创作
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default PPT; 
 
 
 
 
 