import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Send, 
  Mic, 
  Plus,
  Paperclip,
  Smile,
  Volume2,
  Download,
  Copy,
  RefreshCw,
  MessageSquare,
  User,
  Bot,
  Calendar,
  Clock,
  ThumbsUp,
  ThumbsDown,
  Share2,
  Calculator,
  BarChart3, 
  PieChart, 
  TrendingUp,
  RotateCcw,
  Upload,
  Camera,
  History
} from 'lucide-react';
import Button from '../../components/ui/Button';
import Input from '../../components/ui/Input';
import Card, { CardHeader, CardTitle, CardDescription, CardContent } from '../../components/ui/Card';
import { Chart, type ChartData, generateSampleChartData } from '../../components/ui/Chart';
import { Table, type TableData, generateSampleTableData } from '../../components/ui/Table';
import MessageComponent from '../../components/ui/MessageComponent';
import ChatHistory, { type ChatSession } from '../../components/ui/ChatHistory';
import { api, type Message } from '../../services/api';

const Chat: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'ai',
      content: `# 👋 欢迎使用高校AI助手！

我可以帮你：
- **数学解题**：求解方程、计算积分、绘制函数图像
- **代码编程**：支持多种编程语言的语法高亮
- **图表生成**：柱状图、饼图、线图等数据可视化
- **公式渲染**：LaTeX数学公式，如 $f(x) = x^2 + 2x + 1$

你也可以上传图片、文档等文件，我会帮你分析内容。

有什么需要帮助的吗？🚀`,
      timestamp: new Date(),
    },
  ]);
  
  const [sessions, setSessions] = useState<ChatSession[]>([
    {
      id: '1',
      title: '数学解题助手',
      preview: '帮我解一道微积分题目...',
      messageCount: 5,
      lastActivity: new Date(),
      createdAt: new Date(),
      isActive: true
    },
    {
      id: '2', 
      title: '编程学习',
      preview: '如何学习React和TypeScript...',
      messageCount: 12,
      lastActivity: new Date(Date.now() - 3600000),
      createdAt: new Date(Date.now() - 86400000)
    }
  ]);
  
  const [activeSessionId, setActiveSessionId] = useState<string>('1');
  const [showHistory, setShowHistory] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const quickActions = [
    { icon: Calculator, label: '数学解题', prompt: '数学解题 积分' },
    { icon: BarChart3, label: '生成图表', prompt: '生成图表' },
    { icon: PieChart, label: '饼图分析', prompt: '饼图分析' },
    { icon: TrendingUp, label: '函数图像', prompt: '函数图像' },
  ];

  const handleSendMessage = async () => {
    if (!inputValue.trim() && uploadedFiles.length === 0) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue,
      timestamp: new Date(),
      attachments: uploadedFiles.length > 0 ? uploadedFiles.map(file => ({
        type: file.type.startsWith('image/') ? 'image' as const : 'file' as const,
        data: { name: file.name, size: file.size, url: URL.createObjectURL(file) }
      })) : undefined,
    };

    setMessages(prev => [...prev, userMessage]);
    const messageToSend = inputValue;
    setInputValue('');
    setUploadedFiles([]);
    setIsTyping(true);

    try {
      const response = await api.chat.sendMessage({
        message: messageToSend,
        attachments: userMessage.attachments
      }) as any;

      const aiMessage: Message = {
        id: response.id || Date.now().toString(),
        type: 'ai',
        content: response.content || generateSmartResponse(messageToSend, uploadedFiles),
        timestamp: new Date(response.timestamp || new Date()),
        attachments: response.attachments || []
      };

      setMessages(prev => [...prev, aiMessage]);
    } catch (error) {
      console.error('发送消息失败:', error);
      
      const fallbackResponse = generateSmartResponse(messageToSend, uploadedFiles);
      const aiMessage: Message = {
        id: Date.now().toString(),
        type: 'ai',
        content: fallbackResponse.content,
        timestamp: new Date(),
        attachments: fallbackResponse.attachments
      };
      setMessages(prev => [...prev, aiMessage]);
    } finally {
      setIsTyping(false);
    }
  };

  const generateSmartResponse = (prompt: string, files: File[]) => {
    const lowerPrompt = prompt.toLowerCase();
    
    // 数学解题 - 生成函数图像
    if (lowerPrompt.includes('数学解题') || lowerPrompt.includes('积分')) {
      // 生成 x² 函数的图像数据
      const functionData = [];
      for (let x = -5; x <= 5; x += 0.2) {
        functionData.push({ x: Number(x.toFixed(1)), y: Number((x * x).toFixed(1)) });
      }
      
      return {
        content: `## 📚 高等数学解题：积分 ∫x²dx

**问题分析：**
这是一个幂函数积分问题，我来为您详细解答并生成函数图像。

**解题步骤：**

### 1. 应用幂函数积分公式
$$\\\int x^2 dx = \\\frac{x^3}{3} + C$$

### 2. 如果是定积分 ∫₀¹ x² dx
$$\\\int_0^1 x^2 dx = \\\left[\\\frac{x^3}{3}\\\right]_0^1 = \\\frac{1}{3}$$

### 3. 几何意义
下面的图像显示了函数 y = x² 的形状，定积分表示曲线下的面积。`,
        attachments: [{
          type: 'chart' as const,
          data: {
            type: 'line' as const,
            title: '函数图像: y = x²',
            data: functionData,
          }
        }]
      };
    }

    // 生成图表 - 创建柱状图
    if (lowerPrompt === '生成图表') {
      return {
        content: `## 📊 数据可视化示例

我为您生成了一个学科成绩对比的柱状图，展示了不同学科的成绩分布情况。

**图表分析：**
- **最高分**：计算机科学 (95分)
- **平均分**：88分
- **改进建议**：化学成绩还有提升空间

**柱状图特点：**
- 直观比较不同类别数据
- 清晰显示数值差异
- 适合展示分类数据`,
        attachments: [{
          type: 'chart' as const,
          data: {
            type: 'bar' as const,
            title: '各学科成绩对比',
            data: [
              { name: '数学', value: 92 },
              { name: '物理', value: 88 },
              { name: '化学', value: 85 },
              { name: '英语', value: 90 },
              { name: '计算机', value: 95 }
            ]
          }
        }]
      };
    }

    // 饼图分析 - 生成饼图
    if (lowerPrompt === '饼图分析') {
      return {
        content: `## 🥧 学习时间分配分析

我为您生成了一个学习时间分配的饼图，帮助您分析时间管理情况。

**数据洞察：**
- **数学占比最大** (35%)：说明数学是学习重点
- **物理次之** (25%)：理科学习时间充足
- **化学** (20%)：需要保持稳定投入
- **英语** (15%)：可适当增加练习时间
- **其他科目** (5%)：时间分配合理

**优化建议：**
- 保持数学优势的同时，适当增加英语练习
- 化学需要稳定投入，建议制定每日学习计划
- 合理安排休息时间，提高学习效率`,
        attachments: [{
          type: 'chart' as const,
          data: {
            type: 'pie' as const,
            title: '学习时间分配',
            data: [
              { name: '数学', value: 35 },
              { name: '物理', value: 25 },
              { name: '化学', value: 20 },
              { name: '英语', value: 15 },
              { name: '其他', value: 5 }
            ]
          }
        }]
      };
    }

    // 函数图像 - 生成三角函数图像
    if (lowerPrompt === '函数图像') {
      // 生成 sin(x) 函数数据
      const sinData = [];
      for (let x = -Math.PI * 2; x <= Math.PI * 2; x += 0.1) {
        sinData.push({ x: Number(x.toFixed(2)), y: Number(Math.sin(x).toFixed(3)) });
      }
      
      return {
        content: `## 📈 三角函数图像分析

我为您生成了正弦函数 y = sin(x) 的图像，这是最基本的三角函数之一。

**函数特性：**
- **周期性**：周期为 2π
- **值域**：[-1, 1]
- **对称性**：关于原点对称（奇函数）
- **零点**：x = nπ (n为整数)
- **最值点**：最大值在 x = π/2 + 2nπ，最小值在 x = -π/2 + 2nπ

**应用场景：**
- 物理学中的波动现象
- 工程学中的振动分析
- 信号处理中的频率分析`,
        attachments: [{
          type: 'chart' as const,
          data: {
            type: 'line' as const,
            title: '三角函数图像: y = sin(x)',
            data: sinData,
          }
        }]
      };
    }

    // 高等数学解答增强（其他数学问题）
    if (lowerPrompt.includes('积分') || lowerPrompt.includes('导数') || lowerPrompt.includes('微积分')) {
      return {
        content: `## 📚 高等数学解题

**问题分析：**
这是一个${lowerPrompt.includes('积分') ? '积分' : lowerPrompt.includes('导数') ? '导数' : '微积分'}计算问题。

**详细解答：**

### 1. 基础公式回顾
${lowerPrompt.includes('积分') ? `
**基本积分公式：**
- $\\\int x^n dx = \\\frac{x^{n+1}}{n+1} + C$ (n ≠ -1)
- $\\\int e^x dx = e^x + C$
- $\\\int \\\sin x dx = -\\\cos x + C$
- $\\\int \\\cos x dx = \\\sin x + C$
` : `
**基本求导公式：**
- $(x^n)' = nx^{n-1}$
- $(e^x)' = e^x$
- $(\\\sin x)' = \\\cos x$
- $(\\\cos x)' = -\\\sin x$
`}

### 2. 解题步骤
**示例：计算 $\\\int_0^1 x^2 dx$**

1. **应用幂函数积分公式：**
   $$\\int x^2 dx = \\\frac{x^3}{3} + C$$

2. **计算定积分：**
   $$\\int_0^1 x^2 dx = \\\left[\\\frac{x^3}{3}\\\right]_0^1 = \\\frac{1^3}{3} - \\\frac{0^3}{3} = \\\frac{1}{3}$$

3. **几何意义：**
   这个积分表示函数 $y = x^2$ 在区间 $[0,1]$ 下的面积。

### 3. 相关知识点
- 定积分的几何意义
- 牛顿-莱布尼茨公式
- 幂函数的积分性质

**建议练习：**
试计算 $\\\int_0^2 (2x + 1) dx$ 来巩固基础。`,
        attachments: []
      };
    }

    // 图表相关（非快速操作）
    if (lowerPrompt.includes('图表') && !lowerPrompt.includes('生成图表')) {
      return {
        content: `## 📊 数据可视化与图表分析

**图表类型选择：**
不同的数据需要不同类型的图表来最好地展示信息。

**常见图表类型：**
- **柱状图**：适合比较不同类别的数据
- **折线图**：适合显示数据的时间变化趋势
- **饼图**：适合显示各部分占整体的比例
- **散点图**：适合显示两个变量之间的关系

**使用建议：**
根据您的数据特点选择合适的图表类型，确保数据表达清晰准确。`,
        attachments: []
      };
    }
    
    // 饼图相关（非快速操作）
    if (lowerPrompt.includes('饼图') && !lowerPrompt.includes('饼图分析')) {
      return {
        content: `## 🥧 饼图分析与数据分布

**饼图特点：**
饼图是最直观的数据分布展示方式，能清晰地显示各部分占整体的比例关系。

**适用场景：**
- 展示各部分占总体的百分比
- 比较不同类别的相对大小
- 强调某一部分的重要性

**制作要点：**
1. 数据分类要清晰明确
2. 颜色搭配要协调美观
3. 标签信息要完整准确`,
        attachments: []
      };
    }
    
    // 函数相关（非快速操作）
    if ((lowerPrompt.includes('函数') || lowerPrompt.includes('图像')) && !lowerPrompt.includes('函数图像')) {
        return {
        content: `## 📈 函数与图像分析

**常见函数类型：**

### 1. 基本函数
- **线性函数：** y = kx + b（直线）
- **二次函数：** y = ax² + bx + c（抛物线）

### 2. 三角函数
- **正弦函数：** y = sin(x)
- **余弦函数：** y = cos(x)

### 3. 指数与对数函数
- **指数函数：** y = aˣ
- **对数函数：** y = log_a(x)

**分析要点：**
理解函数的定义域、值域、单调性、奇偶性等基本性质。`,
        attachments: []
      };
    }

    // 物理学解答增强
    if (lowerPrompt.includes('物理') || lowerPrompt.includes('力学') || lowerPrompt.includes('电磁') || lowerPrompt.includes('热力学')) {
      return {
        content: `## 🔬 物理学问题解析

**学科分类：**
- **经典力学**：研究物体运动规律
- **电磁学**：研究电荷和磁场相互作用
- **热力学**：研究热现象和能量转换
- **量子力学**：研究微观粒子行为

**解题方法：**
1. 明确物理过程和条件
2. 选择合适的物理定律
3. 建立数学模型求解
4. 检验结果的合理性

物理问题需要理论与实践相结合。`,
        attachments: []
      };
    }

    // 编程解答增强
    if (lowerPrompt.includes('编程') || lowerPrompt.includes('代码') || lowerPrompt.includes('算法')) {
      return {
        content: `## 💻 编程与算法指导

**编程语言选择：**
- **Python**：适合数据分析和机器学习
- **JavaScript**：适合Web开发
- **Java**：适合企业级应用开发
- **C++**：适合系统编程和性能要求高的应用

**算法基础：**
- 时间复杂度分析
- 空间复杂度优化
- 常见数据结构使用
- 设计模式应用

**学习建议：**
从基础语法开始，逐步提升编程思维和问题解决能力。`,
        attachments: []
      };
    }

    // 化学解答增强
    if (lowerPrompt.includes('化学') || lowerPrompt.includes('分子') || lowerPrompt.includes('反应')) {
      return {
        content: `## 🧪 化学问题解析

**化学基础：**
- **原子结构**：质子、中子、电子
- **化学键**：离子键、共价键、金属键
- **化学反应**：氧化还原、酸碱反应
- **有机化学**：烷烃、烯烃、芳香烃

**实验方法：**
1. 安全操作规程
2. 精确测量技术
3. 数据记录分析
4. 结果验证方法

化学学习需要理论与实验并重。`,
        attachments: []
      };
    }

    // 默认响应
    return {
      content: `## 🤖 AI智能助手

感谢您的提问！我已成功接收并处理您的请求。

**可用功能：**
- 数学解题和计算
- 图表生成和分析
- 代码编程指导
- 学术问题解答
- 物理化学辅导

快速操作按钮功能正常，没有页面跳转问题。请继续使用！`,
      attachments: []
    };
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []) as File[];
    setUploadedFiles(prev => [...prev, ...files]);
  };

  const removeFile = (index: number) => {
    setUploadedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const handleQuickAction = (prompt: string) => {
    console.log('🚀 快速操作被触发:', prompt);
    
    try {
      // 防止页面跳转
      if (window.event) {
        window.event.preventDefault();
        window.event.stopPropagation();
      }
      
      // 添加用户消息
      const userMessage: Message = {
        id: Date.now().toString(),
        type: 'user',
        content: prompt,
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, userMessage]);
      setInputValue('');
      setIsTyping(true);

      // 生成AI响应
      const smartResponse = generateSmartResponse(prompt, []);
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: smartResponse.content,
        timestamp: new Date(),
        attachments: smartResponse.attachments
      };

      // 使用延时模拟AI思考，但不阻塞函数返回
      setTimeout(() => {
        setMessages(prev => [...prev, aiMessage]);
        setIsTyping(false);
      }, 1000);

      return false; // 明确返回false阻止任何默认行为
    } catch (error) {
      console.error('处理快速操作时出错:', error);
      setIsTyping(false);
      return false;
    }
  };

  const copyMessage = (content: string) => {
    navigator.clipboard.writeText(content);
  };

  const downloadChart = (chartData: ChartData) => {
    console.log('下载图表:', chartData.title);
  };

  // 会话管理
  const handleSessionSelect = (sessionId: string) => {
    setActiveSessionId(sessionId);
    // 这里可以加载对应会话的消息历史
  };

  const handleSessionCreate = () => {
    const newSession: ChatSession = {
      id: Date.now().toString(),
      title: '新对话',
      preview: '开始新的对话...',
      messageCount: 0,
      lastActivity: new Date(),
      createdAt: new Date()
    };
    setSessions(prev => [newSession, ...prev]);
    setActiveSessionId(newSession.id);
    setMessages([{
      id: '1',
      type: 'ai',
      content: '👋 你好！有什么可以帮助你的吗？',
      timestamp: new Date(),
    }]);
  };

  const handleSessionRename = (sessionId: string, newTitle: string) => {
    setSessions(prev => prev.map(session => 
      session.id === sessionId ? { ...session, title: newTitle } : session
    ));
  };

  const handleSessionDelete = (sessionId: string) => {
    setSessions(prev => prev.filter(session => session.id !== sessionId));
    if (sessionId === activeSessionId && sessions.length > 1) {
      const remainingSessions = sessions.filter(s => s.id !== sessionId);
      setActiveSessionId(remainingSessions[0].id);
    }
  };

  return (
    <div className="h-full flex bg-gradient-to-br from-slate-50 to-blue-50">
      {/* Chat History Sidebar */}
      <AnimatePresence>
        {showHistory && (
          <motion.div
            initial={{ x: -300, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            exit={{ x: -300, opacity: 0 }}
            className="w-80 border-r border-gray-200"
          >
            <ChatHistory
              sessions={sessions}
              activeSessionId={activeSessionId}
              onSessionSelect={handleSessionSelect}
              onSessionCreate={handleSessionCreate}
              onSessionRename={handleSessionRename}
              onSessionDelete={handleSessionDelete}
            />
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
      {/* Header */}
      <div className="flex-shrink-0 border-b border-slate-200 bg-white/80 backdrop-blur-sm">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={(e) => {
                    e.preventDefault();
                    setShowHistory(!showHistory);
                  }}
                  className="h-9 w-9 p-0"
                >
                  <History className="h-4 w-4" />
                </Button>
            <div>
              <h1 className="text-xl font-semibold text-slate-900">AI 智能对话</h1>
              <p className="text-sm text-slate-600 mt-1">
                    支持数学解题、图表生成、代码编程等多种功能
              </p>
                </div>
            </div>
            <div className="flex items-center gap-2">
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                ● 在线
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-6 space-y-4">
        <AnimatePresence>
          {messages.map((message) => (
              <MessageComponent
              key={message.id}
                message={message}
                onCopy={copyMessage}
                onRegenerate={(messageId) => console.log('重新生成:', messageId)}
                onDownloadChart={downloadChart}
              />
          ))}
        </AnimatePresence>

        {/* Typing Indicator */}
        {isTyping && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex justify-start"
          >
            <div className="max-w-3xl mr-12">
              <Card className="p-4 bg-white border-slate-200">
                <div className="flex items-center gap-2">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
                    <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
                    <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce"></div>
                  </div>
                  <span className="text-sm text-slate-600">AI正在思考...</span>
                </div>
              </Card>
            </div>
          </motion.div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Quick Actions */}
      <div className="flex-shrink-0 px-6 py-3 border-t border-slate-200 bg-white/80 backdrop-blur-sm">
          <div className="flex gap-3 mb-6 overflow-x-auto">
          {quickActions.map((action, index) => (
              <motion.button
              key={index}
                type="button"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.1 }}
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  e.nativeEvent.preventDefault();
                  e.nativeEvent.stopImmediatePropagation();
                  // 确保不会有任何表单提交或页面跳转
                  return handleQuickAction(action.prompt);
                }}
                className="flex items-center space-x-2 px-4 py-3 bg-white/60 hover:bg-white/80 rounded-xl border border-neutral-200 transition-all duration-200 hover:shadow-md whitespace-nowrap flex-shrink-0"
              >
                <div className="w-6 h-6 flex items-center justify-center rounded-lg bg-primary-50 text-primary-600">
                  <action.icon className="w-4 h-4" />
                </div>
                <span className="text-sm font-medium text-neutral-700">{action.label}</span>
              </motion.button>
          ))}
        </div>
      </div>

      {/* File Upload Preview */}
      {uploadedFiles.length > 0 && (
        <div className="flex-shrink-0 px-6 py-2 border-t border-slate-200 bg-slate-50">
          <div className="flex gap-2 flex-wrap">
            {uploadedFiles.map((file, index) => (
              <div key={index} className="flex items-center gap-2 bg-white rounded-lg p-2 border">
                <div className="w-8 h-8 rounded bg-blue-100 flex items-center justify-center">
                  {file.type.startsWith('image/') ? '🖼️' : '📄'}
                </div>
                <span className="text-sm truncate max-w-[100px]">{file.name}</span>
                <Button
                    type="button"
                  size="sm"
                  variant="ghost"
                    onClick={(e) => {
                      e.preventDefault();
                      removeFile(index);
                    }}
                  className="h-6 w-6 p-0 hover:bg-red-100"
                >
                  ×
                </Button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Input Area */}
      <div className="flex-shrink-0 border-t border-slate-200 bg-white">
        <div className="p-6">
          <div className="flex gap-3">
            <div className="flex gap-2">
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileUpload}
                multiple
                accept="image/*,.pdf,.doc,.docx"
                className="hidden"
              />
              <Button
                  type="button"
                variant="outline"
                size="sm"
                  onClick={(e) => {
                    e.preventDefault();
                    fileInputRef.current?.click();
                  }}
                className="h-10 w-10 p-0"
              >
                <Upload className="h-4 w-4" />
              </Button>
              <Button
                  type="button"
                variant="outline"
                size="sm"
                className="h-10 w-10 p-0"
              >
                <Camera className="h-4 w-4" />
              </Button>
              <Button
                  type="button"
                variant="outline"
                size="sm"
                className="h-10 w-10 p-0"
              >
                <Mic className="h-4 w-4" />
              </Button>
            </div>
            
            <div className="flex-1">
              <Input
                value={inputValue}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setInputValue(e.target.value)}
                placeholder="输入你的问题，支持数学公式、图表生成、图片识别..."
                onKeyPress={(e: React.KeyboardEvent<HTMLInputElement>) => e.key === 'Enter' && handleSendMessage()}
                className="h-10"
              />
            </div>
            
            <Button
                type="button"
                onClick={(e) => {
                  e.preventDefault();
                  handleSendMessage();
                }}
              disabled={!inputValue.trim() && uploadedFiles.length === 0}
              className="h-10 px-6"
            >
              <Send className="h-4 w-4" />
            </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Chat; 
