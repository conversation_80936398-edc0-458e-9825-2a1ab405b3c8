import React from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import {
  MessageCircle,
  BookOpen,
  FileText,
  Presentation,
  BarChart3,
  Calendar,
  Clock,
  TrendingUp,
  Award,
  Target,
  Zap,
  Brain,
  Heart,
  Star,
  ChevronRight,
  Plus,
  Shield
} from 'lucide-react';
import Card, { CardHeader, CardTitle, CardDescription, CardContent } from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import { useUser } from '../../store';

const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const { user, isAuthenticated } = useUser();

  // 模拟数据
  const stats = {
    todayStudyTime: 3.5,
    weekStudyTime: 18.2,
    completedHomework: 12,
    writtenPapers: 3,
    createdPPTs: 5,
    currentStreak: 7,
  };

  const quickActions = [
    {
      title: '开始聊天',
      description: '与AI助手开始对话',
      icon: MessageCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      path: '/chat',
      gradient: 'from-green-500 to-emerald-500',
    },
    {
      title: '作业辅导',
      description: '获取学习帮助',
      icon: BookOpen,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      path: '/homework',
      gradient: 'from-purple-500 to-indigo-500',
    },
    {
      title: '写作助手',
      description: '论文写作指导',
      icon: FileText,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      path: '/paper',
      gradient: 'from-orange-500 to-red-500',
    },
    {
      title: 'PPT制作',
      description: '智能演示生成',
      icon: Presentation,
      color: 'text-pink-600',
      bgColor: 'bg-pink-50',
      path: '/ppt',
      gradient: 'from-pink-500 to-rose-500',
    },
    {
      title: '降低AI',
      description: '优化文档痕迹',
      icon: Shield,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
      path: '/reduce-ai',
      gradient: 'from-red-500 to-orange-500',
    },
  ];

  const recentActivities = [
    {
      type: 'homework',
      title: '完成数学作业 - 微积分练习',
      time: '30分钟前',
      score: 95,
    },
    {
      type: 'chat',
      title: '与AI讨论物理概念',
      time: '1小时前',
      score: null,
    },
    {
      type: 'paper',
      title: '撰写历史论文大纲',
      time: '2小时前',
      score: null,
    },
    {
      type: 'ppt',
      title: '创建生物课程演示',
      time: '昨天',
      score: null,
    },
  ];

  const learningGoals = [
    {
      title: '每日学习3小时',
      current: 3.5,
      target: 3,
      unit: '小时',
      progress: 100,
      color: 'bg-green-500',
    },
    {
      title: '完成10道数学题',
      current: 7,
      target: 10,
      unit: '题',
      progress: 70,
      color: 'bg-blue-500',
    },
    {
      title: '阅读2篇论文',
      current: 1,
      target: 2,
      unit: '篇',
      progress: 50,
      color: 'bg-purple-500',
    },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-8 pb-8"
    >
      {/* 欢迎区域 */}
      <motion.div variants={itemVariants}>
        <Card variant="gradient" size="lg" className="text-center">
          <div className="flex flex-col items-center space-y-4">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.3, type: "spring" }}
              className="w-20 h-20 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center text-white shadow-xl"
            >
              <Brain className="w-10 h-10" />
            </motion.div>
            
            <div>
              <motion.h1
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="text-3xl font-bold text-neutral-800"
              >
                欢迎回来，{user?.name || '同学'}！
              </motion.h1>
              <motion.p
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
                className="text-neutral-600 mt-2"
              >
                今天是学习的好时光，让我们一起进步吧 🚀
              </motion.p>
            </div>

            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.6 }}
              className="flex items-center space-x-6 text-sm text-neutral-600"
            >
              <div className="flex items-center space-x-2">
                <Calendar className="w-4 h-4" />
                <span>{new Date().toLocaleDateString('zh-CN', { 
                  year: 'numeric', 
                  month: 'long', 
                  day: 'numeric',
                  weekday: 'long' 
                })}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Zap className="w-4 h-4 text-yellow-500" />
                <span>连续学习 {stats.currentStreak} 天</span>
              </div>
            </motion.div>
          </div>
        </Card>
      </motion.div>

      {/* 统计卡片 */}
      <motion.div variants={itemVariants}>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          <Card hoverable className="text-center">
            <div className="flex flex-col items-center justify-center space-y-4 p-4">
              <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                <MessageCircle className="w-6 h-6 text-green-600" />
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-neutral-800">127</p>
                <p className="text-sm text-neutral-600">今日对话</p>
              </div>
            </div>
          </Card>

          <Card hoverable className="text-center">
            <div className="flex flex-col items-center justify-center space-y-4 p-4">
              <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                <Clock className="w-6 h-6 text-blue-600" />
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-neutral-800">{stats.todayStudyTime}h</p>
                <p className="text-sm text-neutral-600">今日学习</p>
              </div>
            </div>
          </Card>

          <Card hoverable className="text-center">
            <div className="flex flex-col items-center justify-center space-y-4 p-4">
              <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                <TrendingUp className="w-6 h-6 text-purple-600" />
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-neutral-800">{stats.currentStreak}</p>
                <p className="text-sm text-neutral-600">连续天数</p>
              </div>
            </div>
          </Card>

          <Card hoverable className="text-center">
            <div className="flex flex-col items-center justify-center space-y-4 p-4">
              <div className="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center">
                <Award className="w-6 h-6 text-orange-600" />
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-neutral-800">A+</p>
                <p className="text-sm text-neutral-600">平均成绩</p>
              </div>
            </div>
          </Card>
        </div>
      </motion.div>

      {/* 快速操作 */}
      <motion.div variants={itemVariants}>
        <Card>
          <CardHeader>
            <CardTitle>快速开始</CardTitle>
            <CardDescription>选择一个功能开始你的学习之旅</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
              {quickActions.map((action, index) => (
                <motion.div
                  key={action.title}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.1 * index }}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Card
                    variant="bordered"
                    hoverable
                    clickable
                    onClick={() => navigate(action.path)}
                    className="text-center p-6 cursor-pointer group"
                  >
                    <div className="flex flex-col items-center space-y-3">
                      <div className={`w-16 h-16 bg-gradient-to-br ${action.gradient} rounded-2xl flex items-center justify-center text-white shadow-lg group-hover:scale-110 transition-transform`}>
                        <action.icon className="w-8 h-8" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-neutral-800">{action.title}</h3>
                        <p className="text-sm text-neutral-600 mt-1">{action.description}</p>
                      </div>
                      <ChevronRight className="w-4 h-4 text-neutral-400 group-hover:text-neutral-600 transition-colors" />
                    </div>
                  </Card>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* 今日目标 */}
        <motion.div variants={itemVariants}>
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>今日目标</CardTitle>
                  <CardDescription>跟踪你的学习进度</CardDescription>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  icon={<Plus className="w-4 h-4" />}
                  onClick={() => navigate('/trace')}
                >
                  查看更多
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {learningGoals.map((goal, index) => (
                  <motion.div
                    key={goal.title}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.1 * index }}
                    className="space-y-2"
                  >
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium text-neutral-700">{goal.title}</span>
                      <span className="text-sm text-neutral-600">
                        {goal.current}/{goal.target} {goal.unit}
                      </span>
                    </div>
                    <div className="w-full bg-neutral-200 rounded-full h-2">
                      <motion.div
                        initial={{ width: 0 }}
                        animate={{ width: `${goal.progress}%` }}
                        transition={{ delay: 0.5 + 0.1 * index, duration: 0.8 }}
                        className={`h-2 rounded-full ${goal.color}`}
                      />
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* 最近活动 */}
        <motion.div variants={itemVariants}>
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>最近活动</CardTitle>
                  <CardDescription>你的学习记录</CardDescription>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  icon={<BarChart3 className="w-4 h-4" />}
                  onClick={() => navigate('/trace')}
                >
                  详细统计
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActivities.map((activity, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.1 * index }}
                    className="flex items-center space-x-3 p-3 rounded-xl hover:bg-neutral-50 transition-colors"
                  >
                    <div className={`w-10 h-10 rounded-xl flex items-center justify-center ${
                      activity.type === 'homework' ? 'bg-purple-100 text-purple-600' :
                      activity.type === 'chat' ? 'bg-green-100 text-green-600' :
                      activity.type === 'paper' ? 'bg-orange-100 text-orange-600' :
                      'bg-pink-100 text-pink-600'
                    }`}>
                      {activity.type === 'homework' && <BookOpen className="w-5 h-5" />}
                      {activity.type === 'chat' && <MessageCircle className="w-5 h-5" />}
                      {activity.type === 'paper' && <FileText className="w-5 h-5" />}
                      {activity.type === 'ppt' && <Presentation className="w-5 h-5" />}
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium text-neutral-800">{activity.title}</p>
                      <p className="text-xs text-neutral-600">{activity.time}</p>
                    </div>
                    {activity.score && (
                      <div className="flex items-center space-x-1">
                        <Star className="w-4 h-4 text-yellow-500" />
                        <span className="text-sm font-medium text-neutral-700">{activity.score}</span>
                      </div>
                    )}
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* 激励区域 */}
      <motion.div variants={itemVariants}>
        <Card variant="gradient" className="text-center">
          <div className="flex flex-col items-center space-y-4">
            <motion.div
              animate={{ 
                scale: [1, 1.1, 1],
                rotate: [0, 5, -5, 0]
              }}
              transition={{ 
                duration: 2,
                repeat: Infinity,
                repeatType: "reverse"
              }}
              className="w-16 h-16 bg-gradient-to-br from-pink-500 to-purple-600 rounded-2xl flex items-center justify-center text-white"
            >
              <Heart className="w-8 h-8" />
            </motion.div>
            <div>
              <h3 className="text-xl font-bold text-neutral-800">坚持就是胜利！</h3>
              <p className="text-neutral-600 mt-2">
                你已经连续学习了 {stats.currentStreak} 天，继续保持这个节奏，成功就在前方！
              </p>
            </div>
            <Button
              variant="primary"
              onClick={() => navigate('/chat')}
              className="bg-gradient-to-r from-primary-500 to-purple-600 hover:from-primary-600 hover:to-purple-700"
            >
              开始今天的学习
            </Button>
          </div>
        </Card>
      </motion.div>
    </motion.div>
  );
};

export default Dashboard; 
