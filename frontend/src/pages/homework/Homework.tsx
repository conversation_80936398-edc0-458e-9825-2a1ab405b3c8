import React, { useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Upload, 
  Camera, 
  FileText, 
  Image, 
  Download,
  BookOpen, 
  Calculator, 
  Atom,
  FileDown,
  Eye,
  RefreshCw,
  CheckCircle,
  Clock,
  Star,
  Trash2,
  Edit3,
  MessageSquare,
  Lightbulb,
  Target,
  Award,
  Zap
} from 'lucide-react';
import Button from '../../components/ui/Button';
import Input from '../../components/ui/Input';
import Card, { CardHeader, CardTitle, CardDescription, CardContent } from '../../components/ui/Card';

interface UploadedFile {
  id: string;
  name: string;
  type: string;
  size: number;
  url: string;
  uploadTime: Date;
  status: 'uploading' | 'processing' | 'completed' | 'error';
  progress?: number;
}

interface HomeworkSolution {
  id: string;
  fileName: string;
  subject: 'math' | 'physics' | 'chemistry';
  subjectName: string;
  problems: {
    id: string;
    question: string;
    solution: string;
    steps: string[];
    difficulty: 'easy' | 'medium' | 'hard';
    concepts: string[];
  }[];
  createdAt: Date;
  totalProblems: number;
  estimatedTime: number;
}

const Homework: React.FC = () => {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [solutions, setSolutions] = useState<HomeworkSolution[]>([
    {
      id: '1',
      fileName: '高等数学第三章习题.pdf',
      subject: 'math',
      subjectName: '高等数学',
      problems: [
        {
          id: '1-1',
          question: '求函数 f(x) = x³ - 3x² + 2x - 1 在 x = 2 处的导数值',
          solution: "f'(2) = 2",
          steps: [
            '首先对函数 f(x) = x³ - 3x² + 2x - 1 求导',
            '使用幂函数求导公式：(x^n)′ = n·x^(n-1)',
            "得到 f'(x) = 3x² - 6x + 2",
            "将 x = 2 代入：f'(2) = 3(2)² - 6(2) + 2 = 12 - 12 + 2 = 2"
          ],
      difficulty: 'medium',
          concepts: ['导数', '幂函数求导', '函数值计算']
        },
        {
          id: '1-2',
          question: '计算定积分 ∫₀¹ x² dx',
          solution: '1/3',
          steps: [
            '使用基本积分公式：∫ x^n dx = x^(n+1)/(n+1) + C',
            '对 x² 积分：∫ x² dx = x³/3 + C',
            '计算定积分：[x³/3]₀¹ = 1³/3 - 0³/3 = 1/3'
          ],
          difficulty: 'easy',
          concepts: ['定积分', '基本积分公式', '牛顿-莱布尼茨公式']
        }
      ],
      createdAt: new Date(Date.now() - 3600000),
      totalProblems: 2,
      estimatedTime: 15
    },
    {
      id: '2',
      fileName: '大学物理力学题目.jpg',
      subject: 'physics',
      subjectName: '大学物理',
      problems: [
        {
          id: '2-1',
          question: '一个质量为 2kg 的物体在恒力 F = 10N 作用下从静止开始运动，求 3s 后的速度',
          solution: 'v = 15 m/s',
          steps: [
            '根据牛顿第二定律：F = ma',
            '计算加速度：a = F/m = 10N / 2kg = 5 m/s²',
            '使用运动学公式：v = v₀ + at',
            '由于从静止开始，v₀ = 0',
            '计算最终速度：v = 0 + 5 × 3 = 15 m/s'
          ],
          difficulty: 'easy',
          concepts: ['牛顿第二定律', '运动学', '匀加速直线运动']
        }
      ],
      createdAt: new Date(Date.now() - 7200000),
      totalProblems: 1,
      estimatedTime: 8
    }
  ]);

  const [isDragOver, setIsDragOver] = useState(false);
  const [selectedSolution, setSelectedSolution] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const cameraInputRef = useRef<HTMLInputElement>(null);

  // 统计数据
  const stats = {
    totalFiles: solutions.length,
    totalProblems: solutions.reduce((sum, s) => sum + s.totalProblems, 0),
    avgDifficulty: solutions.length > 0 ? 
      solutions.reduce((sum, s) => sum + s.problems.length, 0) / solutions.length : 0,
    recentActivity: solutions.filter(s => 
      Date.now() - s.createdAt.getTime() < 24 * 60 * 60 * 1000
    ).length
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      handleFiles(Array.from(files));
    }
  };

  const handleFiles = (files: File[]) => {
    files.forEach(file => {
      // 验证文件类型
      const validTypes = [
        'image/jpeg', 'image/png', 'image/bmp', 'image/tiff', 'image/webp',
        'application/pdf', 'application/msword', 
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      ];
      
      if (!validTypes.includes(file.type)) {
        alert(`不支持的文件类型: ${file.type}`);
        return;
      }

      if (file.size > 50 * 1024 * 1024) {
        alert('文件大小不能超过50MB');
        return;
      }

      const newFile: UploadedFile = {
        id: Date.now().toString() + Math.random(),
        name: file.name,
        type: file.type,
        size: file.size,
        url: URL.createObjectURL(file),
        uploadTime: new Date(),
        status: 'uploading',
        progress: 0
      };

      setUploadedFiles(prev => [...prev, newFile]);
      
      // 模拟上传和处理过程
      simulateFileProcessing(newFile);
    });
  };

  const simulateFileProcessing = async (file: UploadedFile) => {
    // 模拟上传进度
    for (let progress = 0; progress <= 100; progress += 10) {
      setTimeout(() => {
        setUploadedFiles(prev => prev.map(f => 
          f.id === file.id ? { ...f, progress } : f
        ));
      }, progress * 20);
    }

    // 上传完成，开始处理
    setTimeout(() => {
      setUploadedFiles(prev => prev.map(f => 
        f.id === file.id ? { ...f, status: 'processing' } : f
      ));
    }, 2000);

    // 处理完成，生成解答
    setTimeout(() => {
      setUploadedFiles(prev => prev.map(f => 
        f.id === file.id ? { ...f, status: 'completed' } : f
      ));
      
      // 生成模拟解答
      const newSolution = generateMockSolution(file);
      setSolutions(prev => [newSolution, ...prev]);
    }, 5000);
  };

  const generateMockSolution = (file: UploadedFile): HomeworkSolution => {
    const isImageFile = file.type.startsWith('image/');
    const subject = file.name.toLowerCase().includes('物理') ? 'physics' : 
                   file.name.toLowerCase().includes('化学') ? 'chemistry' : 'math';
    
    const subjectNames = {
      math: '高等数学',
      physics: '大学物理',
      chemistry: '大学化学'
    };

    const mockProblems = {
      math: [
        {
          id: Date.now().toString(),
          question: '求极限 lim(x→0) (sin x)/x',
          solution: '1',
          steps: [
            '这是一个重要极限',
            '当 x → 0 时，sin x 与 x 是等价无穷小',
            '因此 lim(x→0) (sin x)/x = 1'
          ],
          difficulty: 'medium' as const,
          concepts: ['极限', '重要极限', '等价无穷小']
        }
      ],
      physics: [
        {
          id: Date.now().toString(),
          question: '计算弹簧振子的周期公式',
          solution: 'T = 2π√(m/k)',
          steps: [
            '弹簧振子满足简谐运动方程：ma = -kx',
            '得到微分方程：d²x/dt² = -(k/m)x',
            '角频率 ω = √(k/m)',
            '周期 T = 2π/ω = 2π√(m/k)'
          ],
          difficulty: 'hard' as const,
          concepts: ['简谐运动', '弹簧振子', '周期公式']
        }
      ],
      chemistry: [
        {
          id: Date.now().toString(),
          question: '计算理想气体状态方程中的摩尔数',
          solution: 'n = PV/(RT)',
          steps: [
            '理想气体状态方程：PV = nRT',
            '解出摩尔数：n = PV/(RT)',
            '其中 R = 8.314 J/(mol·K) 为气体常数'
          ],
          difficulty: 'easy' as const,
          concepts: ['理想气体', '状态方程', '摩尔数']
        }
      ]
    };

    return {
      id: Date.now().toString(),
      fileName: file.name,
      subject,
      subjectName: subjectNames[subject],
      problems: mockProblems[subject],
      createdAt: new Date(),
      totalProblems: mockProblems[subject].length,
      estimatedTime: Math.ceil(mockProblems[subject].length * 5)
    };
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    const files = Array.from(e.dataTransfer.files);
    handleFiles(files);
  };

  const downloadSolution = (solution: HomeworkSolution, format: 'word' | 'pdf') => {
    // 模拟文档生成
    const mockDownload = () => {
      const element = document.createElement('a');
      const content = generateDocumentContent(solution);
      const blob = new Blob([content], { type: 'text/plain' });
      element.href = URL.createObjectURL(blob);
      element.download = `${solution.fileName}_解答.${format === 'word' ? 'docx' : 'pdf'}`;
      element.click();
    };
    
    mockDownload();
  };

  const generateDocumentContent = (solution: HomeworkSolution): string => {
    let content = `${solution.subjectName} 作业解答\n`;
    content += `文件名：${solution.fileName}\n`;
    content += `生成时间：${solution.createdAt.toLocaleString()}\n`;
    content += `总题数：${solution.totalProblems}\n\n`;
    
    solution.problems.forEach((problem, index) => {
      content += `题目 ${index + 1}：${problem.question}\n`;
      content += `答案：${problem.solution}\n`;
      content += `解题步骤：\n`;
      problem.steps.forEach((step, stepIndex) => {
        content += `  ${stepIndex + 1}. ${step}\n`;
      });
      content += `涉及概念：${problem.concepts.join('、')}\n`;
      content += `难度：${problem.difficulty}\n\n`;
    });
    
    return content;
  };

  const deleteSolution = (id: string) => {
    setSolutions(prev => prev.filter(s => s.id !== id));
  };

  const getSubjectIcon = (subject: string) => {
    switch (subject) {
      case 'math': return Calculator;
      case 'physics': return Atom;
      case 'chemistry': return BookOpen;
      default: return BookOpen;
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'text-green-600 bg-green-50';
      case 'medium': return 'text-yellow-600 bg-yellow-50';
      case 'hard': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* 页面标题 */}
      <div className="text-center mb-6">
        <h1 className="text-3xl font-bold text-neutral-800 mb-2">AI理科作业助手</h1>
        <p className="text-neutral-600">支持高数、物理、化学作业智能解答与文档生成</p>
        </div>

      {/* 主要内容区域 */}
      <div className="flex-1 grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 左侧和中间：核心解答区域 */}
        <div className="lg:col-span-2 space-y-6">
          {/* 文件上传区域 */}
            <Card>
              <CardHeader>
              <CardTitle>上传作业文件</CardTitle>
              <CardDescription>支持图片（JPG、PNG、BMP、TIFF、WEBP）、Word文档（DOC、DOCX）、PDF文件，最大50MB</CardDescription>
              </CardHeader>
              <CardContent>
              <div
                className={`border-2 border-dashed rounded-xl p-8 text-center transition-all duration-200 ${
                  isDragOver 
                    ? 'border-primary-500 bg-primary-50' 
                    : 'border-neutral-300 hover:border-primary-400 hover:bg-neutral-50'
                }`}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
              >
                <div className="flex flex-col items-center space-y-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-primary-500 to-purple-600 rounded-2xl flex items-center justify-center text-white">
                    <Upload className="w-8 h-8" />
                  </div>
                  <div>
                    <p className="text-lg font-semibold text-neutral-800">拖拽文件到此处或点击上传</p>
                    <p className="text-sm text-neutral-600 mt-1">支持批量上传多个文件</p>
                  </div>
                  <div className="flex space-x-4">
                    <Button
                      variant="primary"
                      icon={<Upload className="w-4 h-4" />}
                      onClick={() => fileInputRef.current?.click()}
                    >
                      选择文件
                    </Button>
                    <Button
                      variant="secondary"
                      icon={<Camera className="w-4 h-4" />}
                      onClick={() => cameraInputRef.current?.click()}
                    >
                      拍照上传
                    </Button>
                  </div>
                </div>
              </div>

              <input
                ref={fileInputRef}
                type="file"
                multiple
                accept="image/*,.pdf,.doc,.docx"
                onChange={handleFileSelect}
                className="hidden"
              />
              <input
                ref={cameraInputRef}
                type="file"
                accept="image/*"
                capture="environment"
                onChange={handleFileSelect}
                className="hidden"
              />

              {/* 上传文件列表 */}
              {uploadedFiles.length > 0 && (
                <div className="mt-6 space-y-3">
                  <h3 className="font-semibold text-neutral-800">上传进度</h3>
                  {uploadedFiles.map(file => (
                    <div key={file.id} className="flex items-center space-x-3 p-3 bg-neutral-50 rounded-lg">
                      <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center">
                        {file.type.startsWith('image/') ? (
                          <Image className="w-5 h-5 text-primary-600" />
                        ) : (
                          <FileText className="w-5 h-5 text-primary-600" />
                        )}
                          </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-neutral-800">{file.name}</p>
                        <div className="flex items-center space-x-2 mt-1">
                          <div className="flex-1 h-2 bg-neutral-200 rounded-full overflow-hidden">
                            <div 
                              className="h-full bg-primary-500 transition-all duration-300"
                              style={{ width: `${file.progress || 0}%` }}
                            />
                          </div>
                          <span className="text-xs text-neutral-600">
                            {file.status === 'uploading' && `${file.progress}%`}
                            {file.status === 'processing' && '分析中...'}
                            {file.status === 'completed' && '完成'}
                            {file.status === 'error' && '失败'}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
              </CardContent>
            </Card>

          {/* 当前解答展示区域 */}
          {selectedSolution && (
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>解答详情</CardTitle>
                    <CardDescription>{solutions.find(s => s.id === selectedSolution)?.fileName}</CardDescription>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      icon={<Download className="w-4 h-4" />}
                      onClick={() => {
                        const solution = solutions.find(s => s.id === selectedSolution);
                        if (solution) downloadSolution(solution, 'word');
                      }}
                    >
                      Word
                      </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      icon={<FileDown className="w-4 h-4" />}
                      onClick={() => {
                        const solution = solutions.find(s => s.id === selectedSolution);
                        if (solution) downloadSolution(solution, 'pdf');
                      }}
                    >
                      PDF
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {(() => {
                  const solution = solutions.find(s => s.id === selectedSolution);
                  if (!solution) return null;
                  
                  return (
                    <div className="space-y-6">
                      {solution.problems.map((problem, index) => (
                        <div key={problem.id} className="bg-neutral-50 rounded-lg p-6">
                          <div className="flex items-start justify-between mb-4">
                            <h4 className="text-lg font-medium text-neutral-800">题目 {index + 1}</h4>
                            <span className={`px-3 py-1 rounded-full text-sm font-medium ${getDifficultyColor(problem.difficulty)}`}>
                              {problem.difficulty === 'easy' ? '简单' : problem.difficulty === 'medium' ? '中等' : '困难'}
                            </span>
                          </div>
                          
                          <div className="space-y-4">
                            <div>
                              <p className="text-sm text-neutral-600 mb-2 font-medium">题目：</p>
                              <p className="text-neutral-800 bg-white p-4 rounded-lg border text-lg">{problem.question}</p>
                            </div>
                            
                            <div>
                              <p className="text-sm text-neutral-600 mb-2 font-medium">答案：</p>
                              <p className="text-neutral-800 font-semibold bg-green-50 p-4 rounded-lg border border-green-200 text-lg">
                                {problem.solution}
                              </p>
                            </div>
                            
                            <div>
                              <p className="text-sm text-neutral-600 mb-3 font-medium">解题步骤：</p>
                              <ol className="space-y-3">
                                {problem.steps.map((step, stepIndex) => (
                                  <li key={stepIndex} className="flex items-start space-x-3">
                                    <span className="w-8 h-8 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0 mt-1">
                                      {stepIndex + 1}
                                    </span>
                                    <span className="text-neutral-700">{step}</span>
                                  </li>
                                ))}
                              </ol>
                            </div>
                            
                            <div>
                              <p className="text-sm text-neutral-600 mb-2 font-medium">涉及概念：</p>
                              <div className="flex flex-wrap gap-2">
                                {problem.concepts.map((concept, conceptIndex) => (
                                  <span key={conceptIndex} className="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm">
                                    {concept}
                                  </span>
                                ))}
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  );
                })()}
              </CardContent>
            </Card>
          )}
        </div>

        {/* 右侧：解答历史 */}
        <div className="space-y-6">
          <Card className="h-full">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>解答历史</CardTitle>
                  <CardDescription>查看和管理您的作业解答记录</CardDescription>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  icon={<RefreshCw className="w-4 h-4" />}
                  onClick={() => window.location.reload()}
                >
                  刷新
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {solutions.map(solution => {
                  const SubjectIcon = getSubjectIcon(solution.subject);
                    return (
                      <motion.div
                      key={solution.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                      className={`border rounded-lg p-3 cursor-pointer transition-all ${
                        selectedSolution === solution.id 
                          ? 'border-primary-500 bg-primary-50' 
                          : 'border-neutral-200 hover:border-neutral-300'
                      }`}
                      onClick={() => setSelectedSolution(solution.id)}
                      >
                        <div className="flex items-start space-x-3">
                        <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-purple-600 rounded-lg flex items-center justify-center text-white flex-shrink-0">
                          <SubjectIcon className="w-4 h-4" />
                          </div>
                          <div className="flex-1 min-w-0">
                          <h3 className="font-medium text-neutral-800 text-sm truncate">{solution.fileName}</h3>
                          <p className="text-xs text-neutral-600">{solution.subjectName}</p>
                          <p className="text-xs text-neutral-500">{solution.totalProblems} 道题目</p>
                            </div>
                        <div className="flex flex-col space-y-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            icon={<Download className="w-3 h-3" />}
                            onClick={(e) => {
                              e.stopPropagation();
                              downloadSolution(solution, 'word');
                            }}
                            className="h-6 w-6 p-0"
                          />
                          <Button
                            variant="ghost"
                            size="sm"
                            icon={<Trash2 className="w-3 h-3" />}
                            onClick={(e) => {
                              e.stopPropagation();
                              deleteSolution(solution.id);
                            }}
                            className="h-6 w-6 p-0 text-red-600 hover:text-red-700"
                          />
                          </div>
                        </div>
                      </motion.div>
                    );
                  })}
                
                {solutions.length === 0 && (
                  <div className="text-center py-8">
                    <div className="w-12 h-12 bg-neutral-100 rounded-xl flex items-center justify-center mx-auto mb-3">
                      <BookOpen className="w-6 h-6 text-neutral-400" />
                    </div>
                    <p className="text-neutral-600 text-sm">还没有解答记录</p>
                    <p className="text-xs text-neutral-500 mt-1">上传文件开始使用</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
                </div>
  );
};

export default Homework;
