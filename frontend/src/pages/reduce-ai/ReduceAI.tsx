import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Upload, 
  Shield, 
  FileText, 
  Download,
  CheckCircle,
  Clock,
  RefreshCw,
  Trash2,
  AlertCircle,
  Zap,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import Button from '../../components/ui/Button';
import Card, { CardHeader, CardTitle, CardDescription, CardContent } from '../../components/ui/Card';
import { reduceAIService, ProcessedDocument, ReduceAIStats } from '../../services/reduceAI';

interface UploadedFile {
  id: string;
  name: string;
  type: string;
  size: number;
  url: string;
  uploadTime: Date;
  status: 'uploading' | 'processing' | 'completed' | 'error';
  progress?: number;
  originalAIScore?: number;
  optimizedAIScore?: number;
}

const ReduceAI: React.FC = () => {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [processedDocs, setProcessedDocs] = useState<ProcessedDocument[]>([]);
  const [stats, setStats] = useState<ReduceAIStats>({
    totalProcessed: 0,
    avgReduction: 0,
    recentProcessed: 0,
    totalSaved: 0,
    successRate: 0
  });
  const [isDragOver, setIsDragOver] = useState(false);
  const [loading, setLoading] = useState(false);
  const [selectedDoc, setSelectedDoc] = useState<ProcessedDocument | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 加载数据
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [historyData, statsData] = await Promise.all([
        reduceAIService.getHistory(),
        reduceAIService.getStats()
      ]);
      setProcessedDocs(historyData);
      setStats(statsData);
    } catch (error) {
      console.error('加载数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      handleFiles(Array.from(files));
    }
  };

  const handleFiles = async (files: File[]) => {
    for (const file of files) {
      // 验证文件类型
      const validTypes = [
        'application/pdf', 
        'application/msword', 
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      ];
      
      if (!validTypes.includes(file.type)) {
        alert(`仅支持PDF、DOC、DOCX格式文件，当前文件类型: ${file.type}`);
        continue;
      }

      if (file.size > 50 * 1024 * 1024) {
        alert('文件大小不能超过50MB');
        continue;
      }

      const newFile: UploadedFile = {
        id: Date.now().toString() + Math.random(),
        name: file.name,
        type: file.type,
        size: file.size,
        url: URL.createObjectURL(file),
        uploadTime: new Date(),
        status: 'uploading',
        progress: 0
      };

      setUploadedFiles(prev => [...prev, newFile]);
      
      // 处理文件
      await processFile(newFile, file);
    }
  };

  const processFile = async (uploadedFile: UploadedFile, file: File) => {
    try {
      // 更新状态为处理中
      setUploadedFiles(prev => prev.map(f => 
        f.id === uploadedFile.id ? { ...f, status: 'processing' } : f
      ));

      // 调用API处理文档
      const result = await reduceAIService.processDocument(file);
      
      // 更新状态为完成
      setUploadedFiles(prev => prev.map(f => 
        f.id === uploadedFile.id ? { 
          ...f, 
          status: 'completed',
          originalAIScore: result.originalAIScore,
          optimizedAIScore: result.optimizedAIScore
        } : f
      ));
      
      // 添加到处理结果列表
      setProcessedDocs(prev => [result, ...prev]);
      
      // 自动选中新处理的文档在中间显示
      setSelectedDoc(result);
      
      // 更新统计数据
      await loadData();
      
    } catch (error) {
      console.error('文件处理失败:', error);
      setUploadedFiles(prev => prev.map(f => 
        f.id === uploadedFile.id ? { ...f, status: 'error' } : f
      ));
      alert('文件处理失败，请重试');
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    const files = Array.from(e.dataTransfer.files);
    handleFiles(files);
  };

  const downloadDocument = async (doc: ProcessedDocument) => {
    try {
      await reduceAIService.downloadDocument(doc.id, doc.processedFileName);
    } catch (error) {
      console.error('下载失败:', error);
      alert('下载失败，请重试');
    }
  };

  const deleteProcessedDoc = async (id: string) => {
    try {
      await reduceAIService.deleteRecord(id);
      setProcessedDocs(prev => prev.filter(doc => doc.id !== id));
      // 如果删除的是当前选中的文档，清空选择
      if (selectedDoc?.id === id) {
        setSelectedDoc(null);
      }
      // 重新加载统计数据
      await loadData();
    } catch (error) {
      console.error('删除失败:', error);
      alert('删除失败，请重试');
    }
  };

  const selectDocument = (doc: ProcessedDocument) => {
    setSelectedDoc(doc);
  };

  const getAIScoreColor = (score: number) => {
    if (score <= 30) return 'text-green-600 bg-green-50';
    if (score <= 60) return 'text-yellow-600 bg-yellow-50';
    return 'text-red-600 bg-red-50';
  };

  // 模拟原文和优化后内容
  const generateMockContent = (doc: ProcessedDocument) => {
    const originalContent = `这是一个关于人工智能在教育领域应用的研究报告。

人工智能技术在近年来取得了显著的进展，特别是在自然语言处理、机器学习和深度学习等领域。这些技术的发展为教育行业带来了前所未有的机遇和挑战。

本研究旨在探讨人工智能技术在高等教育中的具体应用场景，包括但不限于智能辅导系统、自动化评估工具、个性化学习推荐系统等。通过对现有文献的系统性回顾和实证研究，我们发现人工智能技术能够显著提升教学效率和学习效果。

研究方法采用了定量和定性相结合的混合研究方法。我们收集了来自10所高校的数据，涉及1000名学生和100名教师的问卷调查。同时，我们还进行了深度访谈和案例研究。

结果表明，人工智能技术在教育领域的应用具有巨大潜力，但同时也面临着技术、伦理和社会等多方面的挑战。`;

    const optimizedContent = `本研究深入分析了人工智能技术在高等教育领域的创新应用与发展前景。

随着科技革命的深入推进，人工智能已成为推动教育变革的重要力量。特别是在自然语言理解、机器学习算法、神经网络模型等核心技术方面的突破，为教育行业的数字化转型提供了强有力的技术支撑。

本文重点分析了AI技术在大学教育中的多元化应用模式，涵盖智能化教学辅助平台、自适应学习评价体系、精准化学习资源配置等关键领域。基于广泛的文献调研和实地调查，研究发现智能化教育工具能够有效优化教学流程，提升学习成效。

在研究设计上，本文采用了多元化的研究范式，整合了量化统计分析与质性深度探究。研究样本涵盖了十所不同类型高等院校，获得了千余名在校学生及百名一线教师的调研反馈。此外，还通过专家访谈和典型案例分析，深化了对相关问题的理解。

研究发现表明，人工智能在教育场景中展现出巨大的应用价值和发展空间，但在技术成熟度、伦理规范、社会接受度等维度仍存在需要持续关注的问题。`;

    return { originalContent, optimizedContent };
  };

  return (
    <div className="h-full flex flex-col">
      {/* 页面标题 */}
      <div className="text-center mb-6">
        <h1 className="text-3xl font-bold text-neutral-800 mb-2">AI痕迹降低工具</h1>
        <p className="text-neutral-600">智能优化文档内容，降低AI生成痕迹，支持PDF、Word格式</p>
      </div>

      {/* 三栏布局 */}
      <div className="flex-1 grid grid-cols-12 gap-6">
        {/* 左侧：文件上传区域 */}
        <div className="col-span-3 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">上传文档</CardTitle>
              <CardDescription className="text-sm">支持PDF、Word文档，最大50MB</CardDescription>
            </CardHeader>
            <CardContent>
              <div
                className={`border-2 border-dashed rounded-lg p-6 text-center transition-all duration-200 ${
                  isDragOver 
                    ? 'border-red-500 bg-red-50' 
                    : 'border-neutral-300 hover:border-red-400 hover:bg-neutral-50'
                }`}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
              >
                <div className="flex flex-col items-center space-y-3">
                  <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-orange-600 rounded-xl flex items-center justify-center text-white">
                    <Shield className="w-6 h-6" />
                  </div>
                  <div>
                    <p className="text-sm font-semibold text-neutral-800">拖拽文档到此处</p>
                    <p className="text-xs text-neutral-600 mt-1">或点击上传</p>
                  </div>
                  <Button
                    variant="primary"
                    size="sm"
                    icon={<Upload className="w-4 h-4" />}
                    onClick={() => fileInputRef.current?.click()}
                    disabled={loading}
                  >
                    选择文档
                  </Button>
                </div>
              </div>

              <input
                ref={fileInputRef}
                type="file"
                multiple
                accept=".pdf,.doc,.docx"
                onChange={handleFileSelect}
                className="hidden"
              />

              {/* 处理进度 */}
              {uploadedFiles.length > 0 && (
                <div className="mt-4 space-y-2">
                  <h3 className="text-sm font-semibold text-neutral-800">处理进度</h3>
                  {uploadedFiles.map(file => (
                    <div key={file.id} className="p-3 bg-neutral-50 rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <FileText className="w-4 h-4 text-neutral-600" />
                          <span className="text-sm font-medium text-neutral-800 truncate">{file.name}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          {file.status === 'uploading' && <Clock className="w-3 h-3 text-blue-500" />}
                          {file.status === 'processing' && <RefreshCw className="w-3 h-3 text-yellow-500 animate-spin" />}
                          {file.status === 'completed' && <CheckCircle className="w-3 h-3 text-green-500" />}
                          {file.status === 'error' && <AlertCircle className="w-3 h-3 text-red-500" />}
                        </div>
                      </div>
                      
                      <div className="space-y-1">
                        <div className="text-xs text-neutral-600">
                          {file.status === 'uploading' && '准备上传...'}
                          {file.status === 'processing' && 'AI检测与优化中...'}
                          {file.status === 'completed' && '处理完成'}
                          {file.status === 'error' && '处理失败'}
                        </div>
                        
                        {file.status === 'processing' && (
                          <div className="w-full h-1 bg-neutral-200 rounded-full overflow-hidden">
                            <div className="h-full bg-red-500 animate-pulse w-3/4" />
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* 中间：对比查看区域 */}
        <div className="col-span-6">
          <Card className="h-full">
            <CardHeader>
              <CardTitle>内容对比</CardTitle>
              <CardDescription>
                {selectedDoc ? `${selectedDoc.originalFileName}` : '选择文档查看对比结果'}
              </CardDescription>
            </CardHeader>
            <CardContent className="h-full">
              {selectedDoc ? (
                <div className="h-full flex flex-col">
                  {/* 对比信息 */}
                  <div className="mb-4 p-3 bg-neutral-50 rounded-lg">
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div className="text-center">
                        <p className="text-neutral-600 mb-1">原始AI痕迹</p>
                        <span className={`inline-block px-2 py-1 rounded ${getAIScoreColor(selectedDoc.originalAIScore)}`}>
                          {selectedDoc.originalAIScore}%
                        </span>
                      </div>
                      <div className="text-center">
                        <p className="text-neutral-600 mb-1">优化后痕迹</p>
                        <span className={`inline-block px-2 py-1 rounded ${getAIScoreColor(selectedDoc.optimizedAIScore)}`}>
                          {selectedDoc.optimizedAIScore}%
                        </span>
                      </div>
                      <div className="text-center">
                        <p className="text-neutral-600 mb-1">降低程度</p>
                        <span className="inline-block px-2 py-1 rounded bg-green-50 text-green-600 font-medium">
                          -{selectedDoc.reductionPercentage}%
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* 对比内容 */}
                  <div className="flex-1 grid grid-cols-2 gap-4 min-h-0">
                    {/* 原文 */}
                    <div className="flex flex-col">
                      <h3 className="font-semibold text-neutral-800 mb-2">原始内容</h3>
                      <div className="flex-1 p-3 bg-red-50 rounded-lg overflow-y-auto">
                        <div className="text-neutral-700 leading-relaxed whitespace-pre-line text-sm">
                          {generateMockContent(selectedDoc).originalContent}
                        </div>
                      </div>
                    </div>
                    
                    {/* 优化后 */}
                    <div className="flex flex-col">
                      <h3 className="font-semibold text-neutral-800 mb-2">优化后内容</h3>
                      <div className="flex-1 p-3 bg-green-50 rounded-lg overflow-y-auto">
                        <div className="text-neutral-700 leading-relaxed whitespace-pre-line text-sm">
                          {generateMockContent(selectedDoc).optimizedContent}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 操作按钮 */}
                  <div className="mt-4 flex justify-end space-x-2">
                    <Button
                      variant="primary"
                      size="sm"
                      icon={<Download className="w-4 h-4" />}
                      onClick={() => downloadDocument(selectedDoc)}
                    >
                      下载优化版本
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="h-full flex items-center justify-center">
                  <div className="text-center">
                    <Shield className="w-16 h-16 text-neutral-400 mx-auto mb-4" />
                    <p className="text-neutral-600 mb-2">暂未选择文档</p>
                    <p className="text-sm text-neutral-500">上传文档或从右侧历史记录中选择</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* 右侧：历史记录 */}
        <div className="col-span-3">
          <Card className="h-full">
            <CardHeader>
              <CardTitle className="text-lg">历史记录</CardTitle>
              <CardDescription className="text-sm">已处理的文档列表</CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="text-center py-8">
                  <RefreshCw className="w-6 h-6 text-neutral-400 mx-auto mb-2 animate-spin" />
                  <p className="text-sm text-neutral-600">加载中...</p>
                </div>
              ) : processedDocs.length === 0 ? (
                <div className="text-center py-8">
                  <FileText className="w-8 h-8 text-neutral-400 mx-auto mb-2" />
                  <p className="text-sm text-neutral-600">暂无记录</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {processedDocs.map(doc => (
                    <motion.div
                      key={doc.id}
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      className={`p-3 border rounded-lg cursor-pointer transition-all ${
                        selectedDoc?.id === doc.id 
                          ? 'border-red-500 bg-red-50' 
                          : 'border-neutral-200 hover:border-neutral-300 hover:bg-neutral-50'
                      }`}
                      onClick={() => selectDocument(doc)}
                    >
                      <div className="flex items-start justify-between mb-2">
                        <h4 className="text-sm font-medium text-neutral-800 truncate">{doc.originalFileName}</h4>
                        <div className="flex items-center space-x-1 ml-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            icon={<Download className="w-3 h-3" />}
                            onClick={(e) => {
                              e.stopPropagation();
                              downloadDocument(doc);
                            }}
                            className="p-1 h-auto"
                          />
                          <Button
                            variant="ghost"
                            size="sm"
                            icon={<Trash2 className="w-3 h-3" />}
                            onClick={(e) => {
                              e.stopPropagation();
                              deleteProcessedDoc(doc.id);
                            }}
                            className="p-1 h-auto"
                          />
                        </div>
                      </div>
                      
                      <p className="text-xs text-neutral-600 mb-2">
                        {doc.processedAt.toLocaleString()}
                      </p>
                      
                      <div className="flex items-center justify-between text-xs">
                        <span className={`px-1.5 py-0.5 rounded ${getAIScoreColor(doc.originalAIScore)}`}>
                          {doc.originalAIScore}%
                        </span>
                        <span className="text-neutral-400">→</span>
                        <span className={`px-1.5 py-0.5 rounded ${getAIScoreColor(doc.optimizedAIScore)}`}>
                          {doc.optimizedAIScore}%
                        </span>
                        <span className="text-green-600 font-medium">
                          -{doc.reductionPercentage}%
                        </span>
                      </div>
                    </motion.div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ReduceAI; 