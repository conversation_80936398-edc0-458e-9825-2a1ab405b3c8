import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Home,
  MessageCircle,
  BookOpen,
  FileText,
  Presentation,
  BarChart3,
  Settings,
  HelpCircle,
  ChevronLeft,
  ChevronRight,
  Sparkles,
  GraduationCap,
  Shield
} from 'lucide-react';
import { useUI, useUIActions } from '../../store';

interface NavigationItem {
  key: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  path: string;
  badge?: string | number;
  color?: string;
}

const AppSidebar: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { sidebarCollapsed } = useUI();
  const { setSidebarCollapsed } = useUIActions();

  const navigationItems: NavigationItem[] = [
    {
      key: 'dashboard',
      label: '仪表盘',
      icon: Home,
      path: '/',
      color: 'text-blue-600',
    },
    {
      key: 'chat',
      label: '智能聊天',
      icon: MessageCircle,
      path: '/chat',
      color: 'text-green-600',
      badge: '新',
    },
    {
      key: 'homework',
      label: '作业辅导',
      icon: BookOpen,
      path: '/homework',
      color: 'text-purple-600',
    },
    {
      key: 'paper',
      label: '论文写作',
      icon: FileText,
      path: '/paper',
      color: 'text-orange-600',
    },
    {
      key: 'ppt',
      label: 'PPT生成',
      icon: Presentation,
      path: '/ppt',
      color: 'text-pink-600',
    },
    {
      key: 'reduce-ai',
      label: '降低AI',
      icon: Shield,
      path: '/reduce-ai',
      color: 'text-red-600',
    },
    {
      key: 'trace',
      label: '学习轨迹',
      icon: BarChart3,
      path: '/trace',
      color: 'text-indigo-600',
    },
  ];

  const bottomItems: NavigationItem[] = [
    {
      key: 'settings',
      label: '系统设置',
      icon: Settings,
      path: '/settings',
      color: 'text-gray-600',
    },
    {
      key: 'help',
      label: '帮助中心',
      icon: HelpCircle,
      path: '/help',
      color: 'text-gray-600',
    },
  ];

  const handleItemClick = (path: string) => {
    navigate(path);
  };

  const isActive = (path: string) => {
    if (path === '/') {
      return location.pathname === '/';
    }
    return location.pathname.startsWith(path);
  };

  return (
    <motion.aside
      initial={{ x: -20, opacity: 0 }}
      animate={{ x: 0, opacity: 1 }}
      className={`flex h-full flex-col bg-white/90 backdrop-blur-xl border-r border-white/20 transition-all duration-300 ${
        sidebarCollapsed ? 'w-16' : 'w-80'
      }`}
    >
      {/* 顶部：Logo 和折叠按钮 */}
      <div className="flex h-16 items-center justify-between px-4 border-b border-white/10">
        <AnimatePresence>
          {!sidebarCollapsed && (
            <motion.div
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -10 }}
              className="flex items-center space-x-3"
            >
              <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-primary-500 to-primary-600 text-white shadow-soft">
                <GraduationCap className="h-6 w-6" />
              </div>
              
              <div className="flex flex-col">
                <h1 className="text-lg font-bold text-neutral-800">
                  高校AI助手
                </h1>
                <p className="text-xs text-neutral-500">
                  智能学习伙伴
                </p>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        <motion.button
          whileTap={{ scale: 0.95 }}
          onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
          className="hidden lg:flex h-8 w-8 items-center justify-center rounded-lg bg-white/60 text-neutral-600 shadow-soft transition-all hover:bg-white/80 hover:text-neutral-800"
        >
          {sidebarCollapsed ? (
            <ChevronRight className="h-4 w-4" />
          ) : (
            <ChevronLeft className="h-4 w-4" />
          )}
        </motion.button>
      </div>

      {/* 导航菜单 */}
      <nav className="flex-1 space-y-1 p-4">
        <div className="space-y-1">
          <AnimatePresence>
            {!sidebarCollapsed && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="mb-4 flex items-center space-x-2 px-2"
              >
                <Sparkles className="h-4 w-4 text-primary-500" />
                <span className="text-xs font-medium text-neutral-500 uppercase tracking-wider">
                  核心功能
                </span>
              </motion.div>
            )}
          </AnimatePresence>

          {navigationItems.map((item, index) => (
            <motion.button
              key={item.key}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.05 }}
              onClick={() => handleItemClick(item.path)}
              className={`group relative flex w-full items-center space-x-3 rounded-xl px-3 py-2.5 text-left transition-all ${
                isActive(item.path)
                  ? 'bg-primary-50 text-primary-700 shadow-soft'
                  : 'text-neutral-600 hover:bg-white/60 hover:text-neutral-800'
              } ${sidebarCollapsed ? 'justify-center' : ''}`}
              title={sidebarCollapsed ? item.label : undefined}
            >
              <AnimatePresence>
                {isActive(item.path) && (
                  <motion.div
                    initial={{ scale: 0, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    exit={{ scale: 0, opacity: 0 }}
                    className="absolute -left-1 h-6 w-1 rounded-full bg-primary-500"
                  />
                )}
              </AnimatePresence>

              <div className={`flex-shrink-0 ${item.color || 'text-neutral-600'}`}>
                <item.icon className="w-5 h-5" />
              </div>

              <AnimatePresence>
                {!sidebarCollapsed && (
                  <motion.div
                    initial={{ opacity: 0, width: 0 }}
                    animate={{ opacity: 1, width: 'auto' }}
                    exit={{ opacity: 0, width: 0 }}
                    className="flex items-center justify-between flex-1 min-w-0"
                  >
                    <span className="font-medium truncate">{item.label}</span>
                    {item.badge && (
                      <motion.span
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        className="ml-2 px-2 py-0.5 text-xs font-medium text-white bg-primary-500 rounded-full"
                      >
                        {item.badge}
                      </motion.span>
                    )}
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.button>
          ))}
        </div>
      </nav>

      {/* 底部功能 */}
      <div className="border-t border-white/10 p-4 space-y-1">
        <AnimatePresence>
          {!sidebarCollapsed && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="mb-4 flex items-center space-x-2 px-2"
            >
              <span className="text-xs font-medium text-neutral-500 uppercase tracking-wider">
                其他
              </span>
            </motion.div>
          )}
        </AnimatePresence>

        {bottomItems.map((item, index) => (
          <motion.button
            key={item.key}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: (navigationItems.length + index) * 0.05 }}
            onClick={() => handleItemClick(item.path)}
            className={`group relative flex w-full items-center space-x-3 rounded-xl px-3 py-2.5 text-left transition-all ${
              isActive(item.path)
                ? 'bg-primary-50 text-primary-700 shadow-soft'
                : 'text-neutral-600 hover:bg-white/60 hover:text-neutral-800'
            } ${sidebarCollapsed ? 'justify-center' : ''}`}
            title={sidebarCollapsed ? item.label : undefined}
          >
            <AnimatePresence>
              {isActive(item.path) && (
                <motion.div
                  initial={{ scale: 0, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  exit={{ scale: 0, opacity: 0 }}
                  className="absolute -left-1 h-6 w-1 rounded-full bg-primary-500"
                />
              )}
            </AnimatePresence>

            <div className={`flex-shrink-0 ${item.color || 'text-neutral-600'}`}>
              <item.icon className="w-5 h-5" />
            </div>

            <AnimatePresence>
              {!sidebarCollapsed && (
                <motion.div
                  initial={{ opacity: 0, width: 0 }}
                  animate={{ opacity: 1, width: 'auto' }}
                  exit={{ opacity: 0, width: 0 }}
                  className="flex items-center justify-between flex-1 min-w-0"
                >
                  <span className="font-medium truncate">{item.label}</span>
                  {item.badge && (
                    <motion.span
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="ml-2 px-2 py-0.5 text-xs font-medium text-white bg-primary-500 rounded-full"
                    >
                      {item.badge}
                    </motion.span>
                  )}
                </motion.div>
              )}
            </AnimatePresence>
          </motion.button>
        ))}
      </div>
    </motion.aside>
  );
};

export default AppSidebar; 