import React from 'react';
import { Outlet } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useUI, useUIActions } from '../../store';
import AppHeader from './AppHeader';
import AppSidebar from './AppSidebar';

const AppLayout: React.FC = () => {
  const { sidebarCollapsed, theme } = useUI();
  const { setSidebarCollapsed } = useUIActions();

  return (
    <div className={`h-screen flex bg-gradient-to-br from-slate-50 to-blue-50 ${theme === 'dark' ? 'dark' : ''}`}>
      {/* Sidebar */}
      <motion.div
        initial={false}
        animate={{
          width: sidebarCollapsed ? 80 : 320,
        }}
        transition={{ duration: 0.2, ease: 'easeInOut' }}
        className="flex-shrink-0"
      >
        <AppSidebar />
      </motion.div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col min-w-0 overflow-hidden">
        <AppHeader />
        
        <main className="flex-1 overflow-auto p-6">
          <div className="max-w-7xl mx-auto">
            <Outlet />
          </div>
        </main>
      </div>

      {/* Mobile Sidebar Overlay */}
      {!sidebarCollapsed && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/20 lg:hidden z-40"
          onClick={() => setSidebarCollapsed(true)}
        />
      )}
    </div>
  );
};

export default AppLayout;
