import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  Search,
  Bell,
  Settings,
  User,
  LogOut,
  Menu,
  Sun,
  Moon,
  Monitor,
  ChevronDown
} from 'lucide-react';
import Button from '../ui/Button';
import { useUser, useUI, useUIActions, useUserActions } from '../../store';

const AppHeader: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { sidebarCollapsed, theme } = useUI();
  const { setSidebarCollapsed, setTheme } = useUIActions();
  const { user, isAuthenticated } = useUser();
  const { logout } = useUserActions();

  const pageTitle = React.useMemo(() => {
    const path = location.pathname;
    const titleMap: Record<string, string> = {
      '/': '仪表盘',
      '/chat': '智能聊天',
      '/homework': '作业辅导',
      '/paper': '论文写作',
      '/ppt': 'PPT生成',
      '/reduce-ai': '降低AI',
      '/trace': '学习轨迹',
      '/settings': '系统设置',
      '/help': '帮助中心',
    };
    return titleMap[path] || '高校AI助手';
  }, [location.pathname]);

  const toggleTheme = () => {
    const nextTheme = theme === 'light' ? 'dark' : 'light';
    setTheme(nextTheme);
  };

  return (
    <motion.header
      initial={{ y: -10, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      className="sticky top-0 z-40 w-full border-b border-white/20 bg-white/80 backdrop-blur-xl"
    >
      <div className="flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8">
        {/* 左侧：菜单按钮 + 页面标题 */}
        <div className="flex items-center space-x-4">
          {/* 菜单按钮 */}
          <motion.button
            whileTap={{ scale: 0.95 }}
            onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
            className="flex h-10 w-10 items-center justify-center rounded-xl bg-white/60 text-neutral-600 shadow-soft transition-all hover:bg-white/80 hover:text-neutral-800 lg:hidden"
          >
            <Menu className="h-5 w-5" />
          </motion.button>

          {/* 页面标题 */}
          <motion.div
            key={pageTitle}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
            className="flex items-center space-x-3"
          >
            <h1 className="text-xl font-semibold text-neutral-800 sm:text-2xl">
              {pageTitle}
            </h1>
            
            {/* 面包屑导航 */}
            {location.pathname !== '/' && (
              <nav className="hidden sm:flex">
                <motion.button
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.1 }}
                  onClick={() => navigate('/')}
                  className="text-sm text-neutral-500 hover:text-neutral-700 transition-colors"
                >
                  首页
                </motion.button>
                <span className="mx-2 text-neutral-300">/</span>
                <span className="text-sm text-neutral-700">{pageTitle}</span>
              </nav>
            )}
          </motion.div>
        </div>

        {/* 中间：搜索框（桌面端） */}
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.2 }}
          className="hidden lg:flex flex-1 max-w-lg mx-8"
        >
          <div className="relative w-full">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-neutral-400" />
            <input
              type="text"
              placeholder="搜索功能、聊天记录、作业..."
              className="w-full rounded-xl border-0 bg-white/60 py-2.5 pl-10 pr-4 text-sm text-neutral-700 placeholder-neutral-400 shadow-soft transition-all focus:bg-white/80 focus:outline-none focus:ring-2 focus:ring-primary-500/20"
            />
          </div>
        </motion.div>

        {/* 右侧：操作按钮 */}
        <div className="flex items-center space-x-2">
          {/* 搜索按钮（移动端） */}
          <motion.button
            whileTap={{ scale: 0.95 }}
            className="flex h-10 w-10 items-center justify-center rounded-xl bg-white/60 text-neutral-600 shadow-soft transition-all hover:bg-white/80 hover:text-neutral-800 lg:hidden"
          >
            <Search className="h-5 w-5" />
          </motion.button>

          {/* 主题切换 */}
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleTheme}
            className="w-10 h-10 rounded-xl hover:bg-white/60 flex items-center justify-center"
            title={`切换到${theme === 'light' ? '深色' : '浅色'}模式`}
          >
            {theme === 'light' && <Sun className="h-5 w-5" />}
            {theme === 'dark' && <Moon className="h-5 w-5" />}
          </Button>

          {/* 通知按钮 */}
          <motion.button
            whileTap={{ scale: 0.95 }}
            className="relative flex h-10 w-10 items-center justify-center rounded-xl bg-white/60 text-neutral-600 shadow-soft transition-all hover:bg-white/80 hover:text-neutral-800"
          >
            <Bell className="h-5 w-5" />
            {/* 通知红点 */}
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              className="absolute -top-1 -right-1 h-3 w-3 rounded-full bg-red-500 border-2 border-white"
            />
          </motion.button>

          {/* 设置按钮 */}
          <motion.button
            whileTap={{ scale: 0.95 }}
            onClick={() => navigate('/settings')}
            className="flex h-10 w-10 items-center justify-center rounded-xl bg-white/60 text-neutral-600 shadow-soft transition-all hover:bg-white/80 hover:text-neutral-800"
          >
            <Settings className="h-5 w-5" />
          </motion.button>

          {/* 用户头像 */}
          <motion.button
            whileTap={{ scale: 0.95 }}
            className="flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-primary-500 to-primary-600 text-white shadow-soft transition-all hover:shadow-medium"
          >
            {user?.avatar ? (
              <img
                src={user.avatar}
                alt={user.name}
                className="h-full w-full rounded-xl object-cover"
              />
            ) : (
              <User className="h-5 w-5" />
            )}
          </motion.button>

          {/* 用户信息（桌面端） */}
          <motion.div
            initial={{ opacity: 0, x: 10 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3 }}
            className="hidden xl:block"
          >
            <div className="flex flex-col items-start">
              <p className="text-sm font-medium text-neutral-800">
                {user?.name || '用户'}
              </p>
              <p className="text-xs text-neutral-500">
                在线学习
              </p>
            </div>
          </motion.div>
        </div>
      </div>

      {/* 进度条（可选） */}
      <motion.div
        className="h-0.5 bg-gradient-to-r from-primary-500 via-purple-500 to-pink-500"
        initial={{ scaleX: 0 }}
        animate={{ scaleX: 1 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        style={{ transformOrigin: "left" }}
      />
    </motion.header>
  );
};

export default AppHeader; 