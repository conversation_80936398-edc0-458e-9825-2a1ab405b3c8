import React from 'react';
import { Loader2 } from 'lucide-react';
import { clsx } from 'clsx';

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger' | 'success';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  loading?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  fullWidth?: boolean;
  children?: React.ReactNode;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({
    variant = 'primary',
    size = 'md',
    loading = false,
    icon,
    iconPosition = 'left',
    fullWidth = false,
    disabled,
    children,
    className,
    ...props
  }, ref) => {
    // 基础样式
    const baseStyles = clsx(
      'inline-flex items-center justify-center font-medium transition-all duration-200',
      'focus:outline-none focus:ring-2 focus:ring-offset-2',
      'disabled:opacity-50 disabled:cursor-not-allowed',
      'rounded-xl shadow-soft',
      fullWidth && 'w-full'
    );

    // 尺寸样式
    const sizeStyles = {
      sm: 'px-3 py-1.5 text-sm gap-1.5 h-8',
      md: 'px-4 py-2 text-sm gap-2 h-10',
      lg: 'px-6 py-2.5 text-base gap-2.5 h-12',
      xl: 'px-8 py-3 text-lg gap-3 h-14',
    };

    // 变体样式
    const variantStyles = {
      primary: clsx(
        'bg-gradient-to-r from-primary-500 to-primary-600 text-white',
        'hover:from-primary-600 hover:to-primary-700',
        'focus:ring-primary-500/20',
        'shadow-primary-500/25'
      ),
      secondary: clsx(
        'bg-gradient-to-r from-neutral-100 to-neutral-200 text-neutral-800',
        'hover:from-neutral-200 hover:to-neutral-300',
        'focus:ring-neutral-500/20',
        'border border-neutral-300'
      ),
      outline: clsx(
        'bg-white/80 text-neutral-700 border border-neutral-300',
        'hover:bg-neutral-50 hover:border-neutral-400',
        'focus:ring-neutral-500/20'
      ),
      ghost: clsx(
        'bg-transparent text-neutral-700',
        'hover:bg-neutral-100',
        'focus:ring-neutral-500/20'
      ),
      danger: clsx(
        'bg-gradient-to-r from-red-500 to-red-600 text-white',
        'hover:from-red-600 hover:to-red-700',
        'focus:ring-red-500/20',
        'shadow-red-500/25'
      ),
      success: clsx(
        'bg-gradient-to-r from-green-500 to-green-600 text-white',
        'hover:from-green-600 hover:to-green-700',
        'focus:ring-green-500/20',
        'shadow-green-500/25'
      ),
    };

    // 图标样式
    const iconStyles = {
      sm: 'w-3.5 h-3.5',
      md: 'w-4 h-4',
      lg: 'w-5 h-5',
      xl: 'w-6 h-6',
    };

    const isDisabled = disabled || loading;

    return (
      <button
        ref={ref}
        className={clsx(
          baseStyles,
          sizeStyles[size],
          variantStyles[variant],
          'transform hover:scale-105 active:scale-95 transition-transform duration-150',
          className
        )}
        disabled={isDisabled}
        {...props}
      >
        {/* 加载状态 */}
        {loading && (
          <Loader2 className={clsx(iconStyles[size], 'animate-spin')} />
        )}

        {/* 左侧图标 */}
        {!loading && icon && iconPosition === 'left' && (
          <span className={iconStyles[size]}>
            {icon}
          </span>
        )}

        {/* 文本内容 */}
        {children && (
          <span className={loading ? 'ml-2' : ''}>
            {children}
          </span>
        )}

        {/* 右侧图标 */}
        {!loading && icon && iconPosition === 'right' && (
          <span className={iconStyles[size]}>
            {icon}
          </span>
        )}
      </button>
    );
  }
);

Button.displayName = 'Button';

export default Button;

