import React, { useEffect, useRef } from 'react';
import Card from './Card';

// 导入ECharts
import ReactECharts from 'echarts-for-react';

export interface ChartData {
  type: 'line' | 'bar' | 'pie' | 'scatter';
  title: string;
  data: any[];
  xAxis?: string[];
  yAxis?: string[];
  options?: any;
}

interface ChartProps {
  data: ChartData;
  width?: string | number;
  height?: string | number;
  className?: string;
}

export const Chart: React.FC<ChartProps> = ({
  data,
  width = '100%',
  height = 400,
  className = '',
}) => {
  const chartRef = useRef<any>(null);

  // 确保图表在容器大小变化时能重新调整大小
  useEffect(() => {
    const handleResize = () => {
      if (chartRef.current) {
        chartRef.current.getEchartsInstance().resize();
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 数据验证和错误处理
  if (!data || !Array.isArray(data.data) || data.data.length === 0) {
    return (
      <Card className={`${className} flex items-center justify-center`}>
        <div className="text-gray-500 text-center p-8" style={{ minHeight: height }}>
          <div className="text-4xl mb-2">📊</div>
          <div>暂无图表数据</div>
        </div>
      </Card>
    );
  }

  const getChartOption = () => {
    try {
      const baseOption = {
        title: {
          text: data.title || '图表',
          left: 'center',
          top: 10,
          textStyle: {
            fontSize: 16,
            fontWeight: 'normal',
            color: '#374151',
          },
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          borderColor: '#e5e7eb',
          borderWidth: 1,
          textStyle: {
            color: '#374151',
          },
        },
        grid: {
          left: '10%',
          right: '10%',
          top: '15%',
          bottom: '15%',
          containLabel: true,
        },
        ...data.options,
      };

      switch (data.type) {
        case 'line':
          // 处理线图数据
          const lineData = Array.isArray(data.data[0]?.points) ? data.data[0].points : data.data;
          
          // 安全检查数据
          if (!Array.isArray(lineData) || lineData.length === 0) {
            throw new Error('线图数据格式错误');
          }
          
          // 检查数据格式，如果是{x, y}格式的数值数据，使用数值x轴
          const hasNumericX = lineData.length > 0 && 
            lineData[0] && 
            typeof lineData[0] === 'object' &&
            typeof lineData[0].x === 'number';
          
          if (hasNumericX) {
            // 对于数值x轴（如函数图像）
            const seriesData = lineData.map((point: any) => {
              if (point && typeof point.x === 'number' && typeof point.y === 'number') {
                return [point.x, point.y];
              }
              return [0, 0]; // 默认值
            });
            
            return {
              ...baseOption,
              xAxis: {
                type: 'value',
                axisLine: {
                  lineStyle: { color: '#e5e7eb' },
                },
                axisLabel: {
                  color: '#6b7280',
                },
                splitLine: {
                  lineStyle: { color: '#f3f4f6' },
                },
              },
              yAxis: {
                type: 'value',
                axisLine: {
                  lineStyle: { color: '#e5e7eb' },
                },
                axisLabel: {
                  color: '#6b7280',
                },
                splitLine: {
                  lineStyle: { color: '#f3f4f6' },
                },
              },
              series: [{
                type: 'line',
                smooth: true,
                lineStyle: {
                  width: 3,
                  color: '#3b82f6',
                },
                symbolSize: 4,
                data: seriesData,
              }],
            };
          } else {
            // 对于类别x轴
            const xAxisData = lineData.map((point: any, index: number) => {
              if (point && typeof point === 'object') {
                return point.x || point.name || `项目${index + 1}`;
              }
              return `项目${index + 1}`;
            });
            const seriesData = lineData.map((point: any) => {
              if (point && typeof point === 'object') {
                return typeof point.y === 'number' ? point.y : 
                       typeof point.value === 'number' ? point.value : 0;
              }
              return 0;
            });

            return {
              ...baseOption,
              xAxis: {
                type: 'category',
                data: xAxisData,
                axisLine: {
                  lineStyle: { color: '#e5e7eb' },
                },
                axisLabel: {
                  color: '#6b7280',
                },
              },
              yAxis: {
                type: 'value',
                axisLine: {
                  lineStyle: { color: '#e5e7eb' },
                },
                axisLabel: {
                  color: '#6b7280',
                },
                splitLine: {
                  lineStyle: { color: '#f3f4f6' },
                },
              },
              series: [{
                type: 'line',
                smooth: true,
                lineStyle: {
                  width: 3,
                  color: '#3b82f6',
                },
                symbolSize: 6,
                data: seriesData,
              }],
            };
          }

        case 'bar':
          // 处理柱状图数据
          const barXAxis = data.data.map((item: any, index: number) => {
            if (item && typeof item === 'object') {
              return item.name || item.x || `类别${index + 1}`;
            }
            return `类别${index + 1}`;
          });
          const barSeriesData = data.data.map((item: any) => {
            if (item && typeof item === 'object') {
              return typeof item.value === 'number' ? item.value : 
                     typeof item.y === 'number' ? item.y : 0;
            }
            return 0;
          });

          return {
            ...baseOption,
            xAxis: {
              type: 'category',
              data: barXAxis,
              axisLine: {
                lineStyle: { color: '#e5e7eb' },
              },
              axisLabel: {
                color: '#6b7280',
              },
            },
            yAxis: {
              type: 'value',
              axisLine: {
                lineStyle: { color: '#e5e7eb' },
              },
              axisLabel: {
                color: '#6b7280',
              },
              splitLine: {
                lineStyle: { color: '#f3f4f6' },
              },
            },
            series: [{
              type: 'bar',
              barWidth: '60%',
              itemStyle: {
                borderRadius: [4, 4, 0, 0],
                color: '#3b82f6',
              },
              data: barSeriesData,
            }],
          };

        case 'pie':
          // 验证饼图数据
          const validPieData = data.data.filter((item: any) => {
            return item && 
                   typeof item === 'object' && 
                   typeof item.value === 'number' && 
                   item.value > 0 &&
                   typeof item.name === 'string';
          });

          if (validPieData.length === 0) {
            throw new Error('饼图数据格式错误或无有效数据');
          }

          return {
            ...baseOption,
            tooltip: {
              trigger: 'item',
              formatter: '{a} <br/>{b}: {c} ({d}%)',
              backgroundColor: 'rgba(255, 255, 255, 0.95)',
              borderColor: '#e5e7eb',
              borderWidth: 1,
              textStyle: {
                color: '#374151',
              },
            },
            legend: {
              orient: 'horizontal',
              bottom: '10',
              data: validPieData.map((item: any) => item.name),
            },
            series: [{
              name: data.title || '数据分布',
              type: 'pie',
              radius: ['40%', '70%'],
              center: ['50%', '50%'],
              avoidLabelOverlap: false,
              itemStyle: {
                borderRadius: 4,
                borderColor: '#fff',
                borderWidth: 2,
              },
              label: {
                show: false,
                position: 'center',
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: 20,
                  fontWeight: 'bold',
                },
              },
              labelLine: {
                show: false,
              },
              data: validPieData,
            }],
          };

        case 'scatter':
          // 处理散点图数据
          const scatterData = data.data.map((item: any) => {
            if (item && typeof item === 'object') {
              const x = typeof item.x === 'number' ? item.x : 0;
              const y = typeof item.y === 'number' ? item.y : 0;
              return [x, y];
            }
            return [0, 0];
          });

          return {
            ...baseOption,
            xAxis: {
              type: 'value',
              axisLine: {
                lineStyle: { color: '#e5e7eb' },
              },
              axisLabel: {
                color: '#6b7280',
              },
              splitLine: {
                lineStyle: { color: '#f3f4f6' },
              },
            },
            yAxis: {
              type: 'value',
              axisLine: {
                lineStyle: { color: '#e5e7eb' },
              },
              axisLabel: {
                color: '#6b7280',
              },
              splitLine: {
                lineStyle: { color: '#f3f4f6' },
              },
            },
            series: [{
              type: 'scatter',
              symbolSize: 8,
              itemStyle: {
                color: '#3b82f6',
                opacity: 0.7,
              },
              data: scatterData,
            }],
          };

        default:
          throw new Error(`不支持的图表类型: ${data.type}`);
      }
    } catch (error) {
      console.error('图表配置生成失败:', error);
      return {
        title: {
          text: '图表配置错误',
          left: 'center',
          textStyle: {
            color: '#ef4444',
          },
        },
      };
    }
  };

  const chartOption = getChartOption();

  return (
    <Card className={className}>
      <div 
        style={{ 
          width: typeof width === 'number' ? `${width}px` : width,
          height: typeof height === 'number' ? `${height}px` : height,
          minHeight: typeof height === 'number' ? `${height}px` : height,
          maxWidth: '100%',
          overflow: 'hidden'
        }}
        className="chart-responsive"
      >
        <ReactECharts
          ref={chartRef}
          option={chartOption}
          style={{
            width: '100%',
            height: '100%',
            minHeight: '300px'
          }}
          opts={{
            renderer: 'canvas',
            width: 'auto',
            height: 'auto',
            devicePixelRatio: window.devicePixelRatio || 1
          }}
          onChartReady={() => {
            // 图表准备就绪后，确保正确调整大小
            if (chartRef.current) {
              const instance = chartRef.current.getEchartsInstance();
              setTimeout(() => {
                instance.resize({
                  width: 'auto',
                  height: 'auto'
                });
              }, 200);
            }
          }}
          onEvents={{
            'finished': () => {
              // 图表渲染完成后再次调整尺寸
              if (chartRef.current) {
                setTimeout(() => {
                  chartRef.current.getEchartsInstance().resize();
                }, 100);
              }
            }
          }}
        />
      </div>
    </Card>
  );
};

// 生成示例图表数据的函数
export const generateSampleChartData = (type: ChartData['type']): ChartData => {
  switch (type) {
    case 'line':
      return {
        type: 'line',
        title: '月度增长趋势',
        data: [
          { x: '1月', y: 120 },
          { x: '2月', y: 132 },
          { x: '3月', y: 101 },
          { x: '4月', y: 134 },
          { x: '5月', y: 90 },
          { x: '6月', y: 230 },
        ],
      };

    case 'bar':
      return {
        type: 'bar',
        title: '各项目数据对比',
        data: [
          { name: '项目A', value: 320 },
          { name: '项目B', value: 280 },
          { name: '项目C', value: 450 },
          { name: '项目D', value: 380 },
          { name: '项目E', value: 420 },
        ],
      };

    case 'pie':
      return {
        type: 'pie',
        title: '市场份额分布',
        data: [
          { name: '产品A', value: 335 },
          { name: '产品B', value: 310 },
          { name: '产品C', value: 234 },
          { name: '产品D', value: 135 },
          { name: '产品E', value: 120 },
        ],
      };

    case 'scatter':
      return {
        type: 'scatter',
        title: '数据分布散点图',
        data: Array.from({ length: 50 }, (_, i) => ({
          x: Math.random() * 100,
          y: Math.random() * 100,
        })),
      };

    default:
      return generateSampleChartData('bar');
  }
};

export default Chart;