import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  MessageCircle, 
  Plus, 
  Search, 
  MoreVertical, 
  Edit3, 
  Trash2,
  Calendar,
  Clock
} from 'lucide-react';
import Button from './Button';
import Input from './Input';
import Card from './Card';

export interface ChatSession {
  id: string;
  title: string;
  preview: string;
  messageCount: number;
  lastActivity: Date;
  createdAt: Date;
  isActive?: boolean;
}

interface ChatHistoryProps {
  sessions: ChatSession[];
  activeSessionId?: string;
  onSessionSelect: (sessionId: string) => void;
  onSessionCreate: () => void;
  onSessionRename: (sessionId: string, newTitle: string) => void;
  onSessionDelete: (sessionId: string) => void;
  className?: string;
}

const ChatHistory: React.FC<ChatHistoryProps> = ({
  sessions,
  activeSessionId,
  onSessionSelect,
  onSessionCreate,
  onSessionRename,
  onSessionDelete,
  className = ''
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editTitle, setEditTitle] = useState('');

  const filteredSessions = sessions.filter(session =>
    session.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    session.preview.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleEditStart = (session: ChatSession) => {
    setEditingId(session.id);
    setEditTitle(session.title);
  };

  const handleEditSave = () => {
    if (editingId && editTitle.trim()) {
      onSessionRename(editingId, editTitle.trim());
    }
    setEditingId(null);
    setEditTitle('');
  };

  const handleEditCancel = () => {
    setEditingId(null);
    setEditTitle('');
  };

  const formatDate = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (days === 0) return '今天';
    if (days === 1) return '昨天';
    if (days < 7) return `${days}天前`;
    return date.toLocaleDateString('zh-CN');
  };

  return (
    <div className={`flex flex-col h-full bg-white border-r border-gray-200 ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-lg font-semibold text-gray-900">对话历史</h2>
          <Button
            variant="primary"
            size="sm"
            onClick={onSessionCreate}
            className="h-8 w-8 p-0"
          >
            <Plus className="h-4 w-4" />
          </Button>
        </div>
        
        <Input
          placeholder="搜索对话..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          prefix={<Search className="w-4 h-4" />}
          className="h-9"
        />
      </div>

      {/* Sessions List */}
      <div className="flex-1 overflow-y-auto">
        <AnimatePresence>
          {filteredSessions.map((session, index) => (
            <motion.div
              key={session.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ delay: index * 0.05 }}
              className={`relative group cursor-pointer border-b border-gray-100 hover:bg-gray-50 transition-colors ${
                session.id === activeSessionId ? 'bg-blue-50 border-blue-200' : ''
              }`}
              onClick={() => onSessionSelect(session.id)}
            >
              <div className="p-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0 mr-2">
                    {editingId === session.id ? (
                      <div className="flex items-center gap-2 mb-2">
                        <Input
                          value={editTitle}
                          onChange={(e) => setEditTitle(e.target.value)}
                          onKeyPress={(e) => e.key === 'Enter' && handleEditSave()}
                          onBlur={handleEditSave}
                          className="h-7 text-sm"
                          autoFocus
                        />
                      </div>
                    ) : (
                      <h3 className="font-medium text-gray-900 truncate mb-1">
                        {session.title}
                      </h3>
                    )}
                    
                    <p className="text-sm text-gray-600 line-clamp-2 mb-2">
                      {session.preview}
                    </p>
                    
                    <div className="flex items-center gap-3 text-xs text-gray-500">
                      <div className="flex items-center gap-1">
                        <MessageCircle className="w-3 h-3" />
                        <span>{session.messageCount}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="w-3 h-3" />
                        <span>{formatDate(session.lastActivity)}</span>
                      </div>
                    </div>
                  </div>
                  
                  {/* Session Actions */}
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                    <div className="relative">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0"
                        onClick={(e) => {
                          e.stopPropagation();
                          // 这里可以添加下拉菜单逻辑
                        }}
                      >
                        <MoreVertical className="h-3 w-3" />
                      </Button>
                      
                      {/* 简化的操作按钮 */}
                      <div className="absolute right-0 top-6 bg-white border border-gray-200 rounded-lg shadow-lg py-1 z-10 opacity-0 group-hover:opacity-100 transition-opacity">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEditStart(session);
                          }}
                          className="flex items-center gap-2 px-3 py-1 text-sm text-gray-700 hover:bg-gray-100 w-full"
                        >
                          <Edit3 className="h-3 w-3" />
                          重命名
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            onSessionDelete(session.id);
                          }}
                          className="flex items-center gap-2 px-3 py-1 text-sm text-red-600 hover:bg-red-50 w-full"
                        >
                          <Trash2 className="h-3 w-3" />
                          删除
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Active Indicator */}
                {session.id === activeSessionId && (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="absolute left-0 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-blue-500 rounded-r"
                  />
                )}
              </div>
            </motion.div>
          ))}
        </AnimatePresence>
        
        {filteredSessions.length === 0 && (
          <div className="p-8 text-center text-gray-500">
            <MessageCircle className="w-12 h-12 mx-auto mb-3 text-gray-300" />
            <p className="text-sm">
              {searchTerm ? '没有找到匹配的对话' : '还没有对话记录'}
            </p>
            {!searchTerm && (
              <Button
                variant="outline"
                size="sm"
                onClick={onSessionCreate}
                className="mt-3"
              >
                开始新对话
              </Button>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatHistory; 