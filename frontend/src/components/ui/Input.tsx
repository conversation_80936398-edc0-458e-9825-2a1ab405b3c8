import React, { forwardRef } from 'react';
import { motion } from 'framer-motion';
import { Eye, EyeOff, AlertCircle } from 'lucide-react';
import { clsx } from 'clsx';

export interface InputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size' | 'prefix'> {
  label?: string;
  hint?: string;
  error?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'filled' | 'minimal';
  prefix?: React.ReactNode;
  suffix?: React.ReactNode;
  showPasswordToggle?: boolean;
  fullWidth?: boolean;
}

const Input = forwardRef<HTMLInputElement, InputProps>(
  ({
    label,
    hint,
    error,
    size = 'md',
    variant = 'default',
    prefix,
    suffix,
    showPasswordToggle = false,
    fullWidth = true,
    type,
    className,
    disabled,
    ...props
  }, ref) => {
    const [showPassword, setShowPassword] = React.useState(false);
    const [isFocused, setIsFocused] = React.useState(false);

    const inputType = type === 'password' && showPassword ? 'text' : type;

    // 尺寸样式
    const sizeStyles = {
      sm: 'px-3 py-2 text-sm h-9',
      md: 'px-4 py-2.5 text-sm h-10',
      lg: 'px-5 py-3 text-base h-12',
    };

    // 变体样式
    const variantStyles = {
      default: clsx(
        'bg-white border border-neutral-300 rounded-xl',
        'hover:border-neutral-400',
        'focus:border-primary-500 focus:ring-2 focus:ring-primary-500/20',
        error && 'border-red-500 focus:border-red-500 focus:ring-red-500/20'
      ),
      filled: clsx(
        'bg-neutral-100 border border-transparent rounded-xl',
        'hover:bg-neutral-200',
        'focus:bg-white focus:border-primary-500 focus:ring-2 focus:ring-primary-500/20',
        error && 'bg-red-50 border-red-500 focus:border-red-500 focus:ring-red-500/20'
      ),
      minimal: clsx(
        'bg-transparent border-0 border-b-2 border-neutral-300 rounded-none',
        'hover:border-neutral-400',
        'focus:border-primary-500',
        error && 'border-red-500 focus:border-red-500'
      ),
    };

    // 容器样式
    const containerStyles = clsx(
      'relative flex items-center',
      sizeStyles[size],
      variantStyles[variant],
      disabled && 'opacity-50 cursor-not-allowed',
      className
    );

    // 输入框样式
    const inputStyles = clsx(
      'flex-1 bg-transparent border-0 outline-none',
      'placeholder:text-neutral-400',
      'text-neutral-900',
      prefix && 'pl-2',
      suffix && 'pr-2',
      (showPasswordToggle || suffix) && 'pr-10'
    );

    // 图标样式
    const iconStyles = {
      sm: 'w-4 h-4',
      md: 'w-5 h-5',
      lg: 'w-6 h-6',
    };

    return (
      <div className={clsx('space-y-1', fullWidth && 'w-full')}>
        {/* 标签 */}
        {label && (
          <motion.label
            initial={{ opacity: 0, y: -5 }}
            animate={{ opacity: 1, y: 0 }}
            className="block text-sm font-medium text-neutral-700"
          >
            {label}
            {props.required && <span className="text-red-500 ml-1">*</span>}
          </motion.label>
        )}

        {/* 输入框容器 */}
        <motion.div
          initial={{ opacity: 0, y: 5 }}
          animate={{ opacity: 1, y: 0 }}
          whileFocus={{ scale: 1.01 }}
          className={containerStyles}
        >
          {/* 前缀 */}
          {prefix && (
            <div className={clsx('flex items-center text-neutral-500', iconStyles[size])}>
              {prefix}
            </div>
          )}

          {/* 输入框 */}
          <input
            ref={ref}
            type={inputType}
            className={inputStyles}
            disabled={disabled}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            {...props}
          />

          {/* 密码显示切换 */}
          {showPasswordToggle && type === 'password' && (
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className={clsx(
                'flex items-center justify-center text-neutral-500 hover:text-neutral-700',
                'transition-colors',
                iconStyles[size]
              )}
            >
              {showPassword ? <EyeOff /> : <Eye />}
            </button>
          )}

          {/* 后缀 */}
          {suffix && !showPasswordToggle && (
            <div className={clsx('flex items-center text-neutral-500', iconStyles[size])}>
              {suffix}
            </div>
          )}

          {/* 错误图标 */}
          {error && !suffix && !showPasswordToggle && (
            <div className={clsx('flex items-center text-red-500', iconStyles[size])}>
              <AlertCircle />
            </div>
          )}

          {/* 焦点指示器 */}
          {isFocused && variant !== 'minimal' && (
            <motion.div
              layoutId="input-focus"
              className="absolute inset-0 rounded-xl ring-2 ring-primary-500/20 pointer-events-none"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            />
          )}
        </motion.div>

        {/* 提示文本或错误信息 */}
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ 
            opacity: (hint || error) ? 1 : 0, 
            height: (hint || error) ? 'auto' : 0 
          }}
          className="overflow-hidden"
        >
          {error ? (
            <p className="text-sm text-red-600 flex items-center gap-1">
              <AlertCircle className="w-4 h-4" />
              {error}
            </p>
          ) : hint ? (
            <p className="text-sm text-neutral-500">{hint}</p>
          ) : null}
        </motion.div>
      </div>
    );
  }
);

Input.displayName = 'Input';

export default Input;

