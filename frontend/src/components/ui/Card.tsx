import React from 'react';
import { motion } from 'framer-motion';
import { clsx } from 'clsx';

export interface CardProps {
  children: React.ReactNode;
  variant?: 'default' | 'bordered' | 'elevated' | 'glass' | 'gradient';
  size?: 'sm' | 'md' | 'lg';
  hoverable?: boolean;
  clickable?: boolean;
  loading?: boolean;
  className?: string;
  onClick?: () => void;
}

const Card: React.FC<CardProps> = ({
  children,
  variant = 'default',
  size = 'md',
  hoverable = false,
  clickable = false,
  loading = false,
  className,
  onClick,
}) => {
  // 基础样式
  const baseStyles = clsx(
    'relative overflow-hidden transition-all duration-200',
    'rounded-2xl',
    (hoverable || clickable) && 'cursor-pointer',
    clickable && 'select-none'
  );

  // 尺寸样式
  const sizeStyles = {
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8',
  };

  // 变体样式
  const variantStyles = {
    default: clsx(
      'bg-white border border-neutral-200',
      'shadow-soft',
      hoverable && 'hover:shadow-medium hover:border-neutral-300'
    ),
    bordered: clsx(
      'bg-white border-2 border-neutral-300',
      hoverable && 'hover:border-primary-300 hover:shadow-soft'
    ),
    elevated: clsx(
      'bg-white border-0',
      'shadow-large',
      hoverable && 'hover:shadow-xl hover:-translate-y-1'
    ),
    glass: clsx(
      'bg-white/70 backdrop-blur-xl border border-white/20',
      'shadow-medium',
      hoverable && 'hover:bg-white/80 hover:shadow-large'
    ),
    gradient: clsx(
      'bg-gradient-to-br from-white via-blue-50/50 to-purple-50/50',
      'border border-white/30 shadow-soft',
      hoverable && 'hover:shadow-medium hover:from-blue-50/30 hover:to-purple-50/70'
    ),
  };

  // 动画变体
  const motionVariants = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -20 },
    hover: hoverable ? { 
      scale: 1.02,
      transition: { type: "spring", stiffness: 300, damping: 30 }
    } : {},
    tap: clickable ? { scale: 0.98 } : {},
  };

  return (
    <motion.div
      initial="initial"
      animate="animate"
      exit="exit"
      whileHover="hover"
      whileTap="tap"
      variants={motionVariants}
      onClick={onClick}
      className={clsx(
        baseStyles,
        sizeStyles[size],
        variantStyles[variant],
        className
      )}
    >
      {/* 加载状态遮罩 */}
      {loading && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="absolute inset-0 bg-white/70 backdrop-blur-sm flex items-center justify-center z-10"
        >
          <div className="flex items-center space-x-2">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              className="w-5 h-5 border-2 border-primary-500 border-t-transparent rounded-full"
            />
            <span className="text-sm text-neutral-600">加载中...</span>
          </div>
        </motion.div>
      )}

      {/* 背景装饰 */}
      {variant === 'gradient' && (
        <div className="absolute inset-0 opacity-30">
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-blue-400/20 to-transparent rounded-full blur-2xl" />
          <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-purple-400/20 to-transparent rounded-full blur-xl" />
        </div>
      )}

      {/* 内容 */}
      <div className="relative z-1">
        {children}
      </div>

      {/* 悬浮光效 */}
      {hoverable && (
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-primary-500/5 via-purple-500/5 to-pink-500/5 opacity-0"
          whileHover={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        />
      )}
    </motion.div>
  );
};

// 卡片标题组件
export const CardHeader: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className }) => (
  <div className={clsx('mb-4 pb-4 border-b border-neutral-200', className)}>
    {children}
  </div>
);

// 卡片标题文本
export const CardTitle: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className }) => (
  <h3 className={clsx('text-lg font-semibold text-neutral-800', className)}>
    {children}
  </h3>
);

// 卡片描述
export const CardDescription: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className }) => (
  <p className={clsx('text-sm text-neutral-600 mt-1', className)}>
    {children}
  </p>
);

// 卡片内容
export const CardContent: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className }) => (
  <div className={clsx('space-y-4', className)}>
    {children}
  </div>
);

// 卡片底部
export const CardFooter: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className }) => (
  <div className={clsx('mt-6 pt-4 border-t border-neutral-200 flex items-center justify-between', className)}>
    {children}
  </div>
);

export default Card;

