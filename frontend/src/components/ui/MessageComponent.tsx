import React from 'react';
import { motion } from 'framer-motion';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import 'katex/dist/katex.min.css';
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';
import { Copy, RotateCcw, Download } from 'lucide-react';
import Button from './Button';
import Card from './Card';
import { Chart, type ChartData } from './Chart';
import { Table, type TableData } from './Table';

interface MessageAttachment {
  type: 'image' | 'chart' | 'table' | 'file';
  data: any;
}

interface Message {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  attachments?: MessageAttachment[];
}

interface MessageComponentProps {
  message: Message;
  onCopy?: (content: string) => void;
  onRegenerate?: (messageId: string) => void;
  onDownloadChart?: (chartData: ChartData) => void;
}

const MessageComponent: React.FC<MessageComponentProps> = ({
  message,
  onCopy,
  onRegenerate,
  onDownloadChart
}) => {
  const renderContent = (content: string) => {
    return (
      <ReactMarkdown
        remarkPlugins={[remarkMath]}
        rehypePlugins={[rehypeKatex]}
        components={{
          code({ className, children, ...props }: any) {
            const match = /language-(\w+)/.exec(className || '');
            return match ? (
              <SyntaxHighlighter
                style={oneDark as any}
                language={match[1]}
                PreTag="div"
                {...props}
              >
                {String(children).replace(/\n$/, '')}
              </SyntaxHighlighter>
            ) : (
              <code className="bg-gray-100 px-1 py-0.5 rounded text-sm" {...props}>
                {children}
              </code>
            );
          },
          p({ children }) {
            return <p className="mb-2 last:mb-0">{children}</p>;
          },
          h1({ children }) {
            return <h1 className="text-xl font-bold mb-3">{children}</h1>;
          },
          h2({ children }) {
            return <h2 className="text-lg font-semibold mb-2">{children}</h2>;
          },
          h3({ children }) {
            return <h3 className="text-base font-medium mb-2">{children}</h3>;
          },
          ul({ children }) {
            return <ul className="list-disc list-inside mb-2 space-y-1">{children}</ul>;
          },
          ol({ children }) {
            return <ol className="list-decimal list-inside mb-2 space-y-1">{children}</ol>;
          },
          blockquote({ children }) {
            return (
              <blockquote className="border-l-4 border-blue-200 pl-4 py-2 bg-blue-50 rounded-r-lg mb-2">
                {children}
              </blockquote>
            );
          }
        }}
      >
        {content}
      </ReactMarkdown>
    );
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
    >
      <div className={`max-w-3xl ${message.type === 'user' ? 'ml-12' : 'mr-12'}`}>
        <Card className={`p-4 ${
          message.type === 'user' 
            ? 'bg-blue-600 text-white' 
            : 'bg-white border-slate-200'
        }`}>
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="prose prose-sm max-w-none">
                {renderContent(message.content)}
              </div>
              
              {/* 附件渲染 */}
              {message.attachments && (
                <div className="mt-3 space-y-3">
                  {message.attachments.map((attachment: MessageAttachment, index: number) => (
                    <div key={index}>
                      {attachment.type === 'image' && (
                        <img 
                          src={attachment.data.url}
                          alt={attachment.data.name}
                          className="max-w-xs rounded-lg border border-slate-200"
                        />
                      )}
                      {attachment.type === 'chart' && (
                        <div className="relative">
                          <Chart data={attachment.data as ChartData} />
                          <div className="absolute top-2 right-2 flex gap-1">
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => onDownloadChart?.(attachment.data as ChartData)}
                              className="h-8 w-8 p-0 hover:bg-slate-100"
                            >
                              <Download className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      )}
                      {attachment.type === 'table' && (
                        <Table data={attachment.data as TableData} />
                      )}
                      {attachment.type === 'file' && (
                        <div className="flex items-center gap-2 p-2 bg-slate-50 rounded-lg">
                          <div className="w-8 h-8 rounded bg-slate-200 flex items-center justify-center">
                            📄
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium truncate">{attachment.data.name}</p>
                            <p className="text-xs text-slate-500">
                              {(attachment.data.size / 1024).toFixed(1)} KB
                            </p>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
            
            {/* 消息操作按钮 */}
            {message.type === 'ai' && (
              <div className="flex items-center gap-1 ml-2">
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => onCopy?.(message.content)}
                  className="h-8 w-8 p-0 hover:bg-slate-100"
                >
                  <Copy className="h-3 w-3" />
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => onRegenerate?.(message.id)}
                  className="h-8 w-8 p-0 hover:bg-slate-100"
                >
                  <RotateCcw className="h-3 w-3" />
                </Button>
              </div>
            )}
          </div>
          
          <div className={`text-xs mt-2 ${
            message.type === 'user' ? 'text-blue-100' : 'text-slate-500'
          }`}>
            {message.timestamp.toLocaleTimeString()}
          </div>
        </Card>
      </div>
    </motion.div>
  );
};

export default MessageComponent; 