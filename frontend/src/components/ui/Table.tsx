import React from 'react';
import Card from './Card';

export interface TableData {
  title: string;
  headers: string[];
  rows: (string | number)[][];
  className?: string;
}

interface TableProps {
  data: TableData;
  className?: string;
}

export const Table: React.FC<TableProps> = ({ data, className = '' }) => {
  if (!data || !data.headers || !data.rows) {
    return (
      <Card className={`${className} flex items-center justify-center p-8`}>
        <div className="text-gray-500 text-center">
          <div className="text-4xl mb-2">📋</div>
          <div>暂无表格数据</div>
        </div>
      </Card>
    );
  }

  return (
    <Card className={`${className} overflow-hidden`}>
      {data.title && (
        <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
          <h3 className="text-lg font-semibold text-gray-900">{data.title}</h3>
        </div>
      )}
      
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              {data.headers.map((header, index) => (
                <th
                  key={index}
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  {header}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {data.rows.map((row, rowIndex) => (
              <tr key={rowIndex} className={rowIndex % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                {row.map((cell, cellIndex) => (
                  <td key={cellIndex} className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {cell}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </Card>
  );
};

// 生成示例表格数据的辅助函数
export const generateSampleTableData = (type: 'math' | 'physics' | 'chemistry' | 'stats'): TableData => {
  switch (type) {
    case 'math':
      return {
        title: '三角函数值表',
        headers: ['角度 (°)', '角度 (弧度)', 'sin', 'cos', 'tan'],
        rows: [
          ['0°', '0', '0', '1', '0'],
          ['30°', 'π/6', '1/2', '√3/2', '√3/3'],
          ['45°', 'π/4', '√2/2', '√2/2', '1'],
          ['60°', 'π/3', '√3/2', '1/2', '√3'],
          ['90°', 'π/2', '1', '0', '∞'],
        ]
      };

    case 'physics':
      return {
        title: '物理常数表',
        headers: ['物理量', '符号', '数值', '单位'],
        rows: [
          ['光速', 'c', '2.998×10⁸', 'm/s'],
          ['重力加速度', 'g', '9.8', 'm/s²'],
          ['普朗克常数', 'h', '6.626×10⁻³⁴', 'J·s'],
          ['电子电荷', 'e', '1.602×10⁻¹⁹', 'C'],
          ['阿伏伽德罗常数', 'Nₐ', '6.022×10²³', 'mol⁻¹'],
        ]
      };

    case 'chemistry':
      return {
        title: '元素周期表（前10个元素）',
        headers: ['原子序数', '元素符号', '元素名称', '原子质量', '电子构型'],
        rows: [
          ['1', 'H', '氢', '1.008', '1s¹'],
          ['2', 'He', '氦', '4.003', '1s²'],
          ['3', 'Li', '锂', '6.941', '[He] 2s¹'],
          ['4', 'Be', '铍', '9.012', '[He] 2s²'],
          ['5', 'B', '硼', '10.81', '[He] 2s² 2p¹'],
          ['6', 'C', '碳', '12.01', '[He] 2s² 2p²'],
          ['7', 'N', '氮', '14.01', '[He] 2s² 2p³'],
          ['8', 'O', '氧', '16.00', '[He] 2s² 2p⁴'],
          ['9', 'F', '氟', '19.00', '[He] 2s² 2p⁵'],
          ['10', 'Ne', '氖', '20.18', '[He] 2s² 2p⁶'],
        ]
      };

    case 'stats':
      return {
        title: '学生成绩统计表',
        headers: ['科目', '平均分', '最高分', '最低分', '标准差'],
        rows: [
          ['数学', '85.2', '98', '72', '8.5'],
          ['物理', '78.6', '95', '65', '9.2'],
          ['化学', '82.4', '96', '68', '7.8'],
          ['英语', '88.1', '99', '75', '6.9'],
          ['编程', '91.3', '100', '78', '7.2'],
        ]
      };

    default:
      return {
        title: '数据表格',
        headers: ['项目', '数值'],
        rows: [['示例', '100']]
      };
  }
};

export default Table; 