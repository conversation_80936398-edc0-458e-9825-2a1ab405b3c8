import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import Chat from '../../../pages/chat/Chat';
import { api } from '../../../services/api';

// Mock API
vi.mock('../../../services/api', () => ({
  api: {
    chat: {
      sendMessage: vi.fn(),
    },
    upload: {
      uploadFile: vi.fn(),
    },
  },
}));

// Mock Chart组件
vi.mock('../../Chart', () => ({
  Chart: ({ data }: any) => <div data-testid="chart">{data.title}</div>,
  generateSampleChartData: vi.fn(() => ({
    type: 'line',
    title: '测试图表',
    data: [],
  })),
}));

describe('Chat组件测试', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('应该正确渲染初始界面', () => {
    render(<Chat />);
    
    // 检查欢迎消息
    expect(screen.getByText(/你好！我是高校AI助手/)).toBeInTheDocument();
    
    // 检查输入框
    expect(screen.getByRole('textbox')).toBeInTheDocument();
    
    // 检查快速操作按钮
    expect(screen.getByText('数学解题')).toBeInTheDocument();
    expect(screen.getByText('生成图表')).toBeInTheDocument();
    expect(screen.getByText('饼图分析')).toBeInTheDocument();
    expect(screen.getByText('函数图像')).toBeInTheDocument();
  });

  it('应该能够发送消息', async () => {
    const mockResponse = {
      id: '2',
      content: '这是AI的回复',
      timestamp: new Date(),
      attachments: []
    };

    (api.chat.sendMessage as any).mockResolvedValue(mockResponse);

    render(<Chat />);
    
    const input = screen.getByRole('textbox');
    const sendButton = screen.getByRole('button', { name: /发送/i });

    // 输入消息
    fireEvent.change(input, { target: { value: '测试消息' } });
    fireEvent.click(sendButton);

    // 检查用户消息是否显示
    expect(screen.getByText('测试消息')).toBeInTheDocument();

    // 等待AI回复
    await waitFor(() => {
      expect(screen.getByText('这是AI的回复')).toBeInTheDocument();
    });

    // 验证API调用
    expect(api.chat.sendMessage).toHaveBeenCalledWith({
      message: '测试消息',
      attachments: undefined
    });
  });

  it('应该处理API错误并回退到本地响应', async () => {
    (api.chat.sendMessage as any).mockRejectedValue(new Error('API错误'));

    render(<Chat />);
    
    const input = screen.getByRole('textbox');
    const sendButton = screen.getByRole('button', { name: /发送/i });

    // 输入数学题目
    fireEvent.change(input, { target: { value: '求解数学题' } });
    fireEvent.click(sendButton);

    // 等待回退响应
    await waitFor(() => {
      expect(screen.getByText(/数学解题/)).toBeInTheDocument();
    });
  });

  it('应该支持快速操作', () => {
    render(<Chat />);
    
    const mathButton = screen.getByText('数学解题');
    fireEvent.click(mathButton);

    const input = screen.getByRole('textbox') as HTMLInputElement;
    expect(input.value).toBe('请帮我解这道数学题：');
  });

  it('应该支持文件上传', async () => {
    const mockUploadResponse = {
      id: 'upload-1',
      filename: 'test.png',
      url: '/uploads/test.png'
    };

    (api.upload.uploadFile as any).mockResolvedValue(mockUploadResponse);

    render(<Chat />);
    
    const fileInput = screen.getByLabelText(/上传文件/i) || document.querySelector('input[type="file"]');
    const testFile = new File(['test'], 'test.png', { type: 'image/png' });

    if (fileInput) {
      fireEvent.change(fileInput, { target: { files: [testFile] } });

      await waitFor(() => {
        expect(api.upload.uploadFile).toHaveBeenCalledWith(testFile);
      });
    }
  });

  it('应该生成图表', async () => {
    const mockResponse = {
      id: '3',
      content: '我为你生成了一个柱状图',
      timestamp: new Date(),
      attachments: [{
        type: 'chart',
        data: {
          type: 'bar',
          title: '测试柱状图',
          data: []
        }
      }]
    };

    (api.chat.sendMessage as any).mockResolvedValue(mockResponse);

    render(<Chat />);
    
    const input = screen.getByRole('textbox');
    const sendButton = screen.getByRole('button', { name: /发送/i });

    fireEvent.change(input, { target: { value: '生成柱状图' } });
    fireEvent.click(sendButton);

    await waitFor(() => {
      expect(screen.getByText('我为你生成了一个柱状图')).toBeInTheDocument();
      expect(screen.getByTestId('chart')).toBeInTheDocument();
    });
  });

  it('应该显示typing指示器', async () => {
    // 延迟API响应以测试loading状态
    (api.chat.sendMessage as any).mockImplementation(() => 
      new Promise(resolve => setTimeout(() => resolve({
        id: '4',
        content: '延迟响应',
        timestamp: new Date(),
        attachments: []
      }), 100))
    );

    render(<Chat />);
    
    const input = screen.getByRole('textbox');
    const sendButton = screen.getByRole('button', { name: /发送/i });

    fireEvent.change(input, { target: { value: '测试loading' } });
    fireEvent.click(sendButton);

    // 检查typing指示器
    expect(screen.getByText('AI正在思考...')).toBeInTheDocument();

    // 等待响应完成
    await waitFor(() => {
      expect(screen.getByText('延迟响应')).toBeInTheDocument();
      expect(screen.queryByText('AI正在思考...')).not.toBeInTheDocument();
    });
  });
}); 