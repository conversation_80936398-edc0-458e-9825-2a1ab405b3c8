import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App.tsx';
import './index.css';

// 在开发环境启动Mock服务
async function enableMocking() {
  if (import.meta.env.DEV) {
    const { startWorker } = await import('./mocks/browser');
    return startWorker();
  }
}

// 启动应用
enableMocking().then(() => {
  ReactDOM.createRoot(document.getElementById('root')!).render(
    <React.StrictMode>
      <App />
    </React.StrictMode>,
  );
}); 