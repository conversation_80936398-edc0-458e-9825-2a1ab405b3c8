@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@layer base {
  * {
    box-sizing: border-box;
  }
  
  html, body {
    margin: 0;
    padding: 0;
    height: 100%;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    background-color: #ffffff;
    color: #1f2937;
    line-height: 1.6;
  }
  
  #root {
    height: 100%;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 active:bg-primary-800 text-white font-medium px-6 py-3 rounded-xl transition-all duration-200 shadow-soft hover:shadow-medium flex items-center justify-center;
  }
  
  .btn-secondary {
    @apply bg-orange-500 hover:bg-orange-600 active:bg-orange-700 text-white font-medium px-6 py-3 rounded-xl transition-all duration-200 shadow-soft hover:shadow-medium flex items-center justify-center;
  }
  
  .input-field {
    @apply w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent outline-none transition-all duration-200 placeholder-gray-400;
  }
  
  .card {
    @apply bg-white rounded-2xl shadow-soft border border-gray-100 p-6 hover:shadow-medium transition-all duration-200;
  }
  
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-primary-700 bg-clip-text text-transparent;
  }

  /* 统一图标尺寸规范 */
  .icon-nav {
    @apply w-5 h-5;
  }
  
  .icon-button {
    @apply w-4 h-4;
  }
  
  .icon-card {
    @apply w-6 h-6;
  }
  
  .icon-large {
    @apply w-8 h-8;
  }

  /* 统一对齐样式 */
  .flex-center {
    @apply flex items-center justify-center;
  }
  
  .flex-center-col {
    @apply flex flex-col items-center justify-center;
  }
  
  .flex-start {
    @apply flex items-center space-x-3;
  }

  /* 统一按钮高度 */
  .btn-height {
    @apply h-10;
  }
  
  .btn-height-lg {
    @apply h-12;
  }
}

/* 全局样式重置 */
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

#root {
  height: 100%;
}

/* 图表容器样式 */
.chart-container {
  width: 100%;
  height: 100%;
  min-height: 300px;
  position: relative;
  overflow: hidden;
}

/* 确保ECharts容器正确布局 */
.echarts-for-react {
  width: 100% !important;
  height: 100% !important;
}

/* 响应式网格布局修复 */
@media (max-width: 1279px) {
  .xl\:col-span-5 {
    grid-column: span 1 / span 1;
  }
  
  .xl\:col-span-2 {
    grid-column: span 1 / span 1;
  }
  
  .xl\:col-span-3 {
    grid-column: span 1 / span 1;
  }
  
  /* 小屏幕下调整边栏 */
  .sidebar-content {
    position: static;
    max-height: none;
    order: 2;
  }
  
  .main-content {
    order: 1;
  }
}

/* 修复图表在小屏幕的显示 */
@media (max-width: 1024px) {
  .chart-responsive {
    height: 250px;
    min-height: 200px;
  }
  
  .modal-overlay .bg-white {
    max-width: 95vw;
    max-height: 95vh;
  }
}

/* 修复超大屏幕的图表显示 */
@media (min-width: 1920px) {
  .chart-responsive {
    max-height: 600px;
  }
}

/* 模态框z-index修复 */
.modal-overlay {
  z-index: 1000;
}

/* 图表预览区域 */
.chart-preview {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 0.5rem;
  min-height: 200px;
}

/* 图表错误状态 */
.chart-error {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: #ef4444;
  background: #fef2f2;
  border: 1px dashed #fca5a5;
  border-radius: 0.5rem;
}

/* 图表加载状态 */
.chart-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: #6b7280;
  background: #f9fafb;
  border: 1px dashed #d1d5db;
  border-radius: 0.5rem;
}

/* 论文编辑器样式 */
.paper-editor {
  font-family: 'Fira Code', 'Monaco', 'Cascadia Code', monospace;
  font-size: 14px;
  line-height: 1.5;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1rem;
  background: #ffffff;
  min-height: 400px;
  resize: vertical;
}

.paper-editor:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 论文预览样式 */
.paper-preview {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 2rem;
  max-height: 400px;
  overflow-y: auto;
  line-height: 1.6;
}

.paper-preview h1 {
  font-size: 1.875rem;
  font-weight: 700;
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  color: #111827;
}

.paper-preview h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-top: 1.25rem;
  margin-bottom: 0.75rem;
  color: #374151;
}

.paper-preview h3 {
  font-size: 1.25rem;
  font-weight: 500;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
  color: #4b5563;
}

/* 滚动条样式 */
.paper-preview::-webkit-scrollbar,
.paper-editor::-webkit-scrollbar {
  width: 6px;
}

.paper-preview::-webkit-scrollbar-track,
.paper-editor::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.paper-preview::-webkit-scrollbar-thumb,
.paper-editor::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.paper-preview::-webkit-scrollbar-thumb:hover,
.paper-editor::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 修复flex布局问题 */
.flex-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  min-height: 0;
}

/* 响应式间距 */
@media (max-width: 1024px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .grid {
    gap: 1rem;
  }
}

/* 工具栏按钮组 */
.toolbar-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  align-items: center;
}

/* 确保按钮内容在同一行 */
.flex.items-center.gap-2.whitespace-nowrap,
.toolbar-buttons .flex.items-center {
  display: flex !important;
  align-items: center !important;
  white-space: nowrap !important;
  gap: 0.5rem !important;
}

/* 特别针对工具栏按钮的样式 */
button.flex.items-center.gap-2.whitespace-nowrap {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  flex-direction: row !important;
  white-space: nowrap !important;
  gap: 0.5rem !important;
}

button.flex.items-center.gap-2.whitespace-nowrap > * {
  flex-shrink: 0;
}

@media (max-width: 768px) {
  .toolbar-buttons {
    justify-content: center;
    gap: 0.25rem;
  }
  
  .toolbar-buttons button {
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
  }
}

/* 侧边栏布局修复 */
.sidebar-content {
  position: sticky;
  top: 1rem;
  max-height: calc(100vh - 2rem);
  overflow-y: auto;
}

/* 主内容区域 */
.main-content {
  min-height: 600px;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* 确保文本不会溢出 */
.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 模态框内容滚动 */
.modal-content {
  max-height: calc(100vh - 4rem);
  overflow-y: auto;
}

/* 图表响应式容器 */
.chart-responsive {
  width: 100%;
  height: 400px;
  min-height: 300px;
  position: relative;
}

@media (max-width: 768px) {
  .chart-responsive {
    height: 300px;
    min-height: 250px;
  }
}

/* 确保按钮不会换行导致布局错乱 */
.button-group {
  display: flex;
  gap: 0.5rem;
  flex-wrap: nowrap;
  align-items: center;
}

@media (max-width: 640px) {
  .button-group {
    flex-wrap: wrap;
    justify-content: center;
  }
} 