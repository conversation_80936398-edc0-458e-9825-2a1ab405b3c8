@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* 高校AI助手 - 极简设计系统全局样式 */

/* 字体设置 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  font-weight: 400;
  line-height: 1.6;
  color: #334155;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
}

/* 极简设计组件样式 */
.minimal-input {
  @apply w-full px-6 py-4 text-lg rounded-2xl border-2 border-gray-200 
         focus:border-blue-400 focus:ring-0 focus:outline-none
         transition-colors duration-200 placeholder-gray-400
         bg-white/80 backdrop-blur-sm;
}

.minimal-button {
  @apply px-8 py-4 rounded-2xl font-medium text-white
         bg-gradient-to-r from-blue-500 to-blue-600
         hover:from-blue-600 hover:to-blue-700
         transform hover:scale-[1.02] active:scale-[0.98]
         transition-all duration-200
         shadow-lg hover:shadow-xl
         focus:outline-none focus:ring-4 focus:ring-blue-200;
}

.minimal-button-secondary {
  @apply px-8 py-4 rounded-2xl font-medium
         text-gray-600 bg-white border-2 border-gray-200
         hover:bg-gray-50 hover:border-gray-300
         transform hover:scale-[1.02] active:scale-[0.98]
         transition-all duration-200
         shadow-md hover:shadow-lg
         focus:outline-none focus:ring-4 focus:ring-gray-200;
}

.minimal-card {
  @apply bg-white/70 backdrop-blur-sm border border-gray-200
         rounded-3xl shadow-lg hover:shadow-xl
         transition-all duration-300
         overflow-hidden;
}

.minimal-sidebar {
  @apply bg-white/60 backdrop-blur-md border-r border-gray-200;
}

.minimal-header {
  @apply bg-white/80 backdrop-blur-md border-b border-gray-200;
}

/* 加载动画 */
.loading-dots {
  display: inline-block;
}

.loading-dots::after {
  content: '';
  animation: dots 1.5s linear infinite;
}

@keyframes dots {
  0%, 20% { content: ''; }
  40% { content: '.'; }
  60% { content: '..'; }
  80%, 100% { content: '...'; }
}

/* 渐变文字 */
.gradient-text {
  @apply bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent;
}

/* 平滑滚动条 */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: #cbd5e1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: #94a3b8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .minimal-input {
    @apply px-4 py-3 text-base;
  }
  
  .minimal-button {
    @apply px-6 py-3 text-sm;
  }
  
  .minimal-button-secondary {
    @apply px-6 py-3 text-sm;
  }
}

/* 暗色模式支持 */
@media (prefers-color-scheme: dark) {
  body {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
    color: #e2e8f0;
  }
  
  .minimal-input {
    @apply bg-slate-800/80 border-slate-600 text-white placeholder-slate-400;
  }
  
  .minimal-card {
    @apply bg-slate-800/70 border-slate-600;
  }
  
  .minimal-sidebar {
    @apply bg-slate-900/60 border-slate-700;
  }
  
  .minimal-header {
    @apply bg-slate-900/80 border-slate-700;
  }
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.slide-up {
  animation: slideUp 0.6s ease-out;
}

.scale-in {
  animation: scaleIn 0.4s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
} 