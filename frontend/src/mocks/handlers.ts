import { http, HttpResponse } from 'msw';

// 模拟数据生成工具
const generateId = () => Math.random().toString(36).substr(2, 9);
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// AI对话模块Mock数据
const mockChatData = {
  conversations: [
    {
      id: 'conv_001',
      title: '数学问题求解',
      messages: [
        {
          id: 'msg_001',
          type: 'user',
          content: '请帮我解这道微积分题',
          timestamp: new Date('2024-12-27T10:00:00'),
          attachments: []
        },
        {
          id: 'msg_002',
          type: 'ai',
          content: '我来帮您解这道微积分题。首先我们需要分析函数的性质...',
          timestamp: new Date('2024-12-27T10:00:15'),
          attachments: [
            {
              type: 'chart',
              data: {
                title: '函数图像',
                type: 'line',
                data: [1, 2, 3, 4, 5]
              }
            }
          ]
        }
      ],
      createdAt: new Date('2024-12-27T10:00:00'),
      updatedAt: new Date('2024-12-27T10:00:15')
    }
  ]
};

// 作业辅导模块Mock数据
const mockHomeworkData = {
  subjects: ['高等数学', '大学物理', '大学化学', '线性代数', '概率统计'],
  uploadedFiles: [
    {
      id: 'file_001',
      name: '高等数学第三章习题.pdf',
      type: 'application/pdf',
      size: 2048576,
      uploadTime: new Date(),
      status: 'completed'
    }
  ],
  solutions: [
    {
      id: 'solution_001',
      fileName: '高等数学第三章习题.pdf',
      subject: 'math',
      subjectName: '高等数学',
      problems: [
        {
          id: 'prob_001',
          question: '求函数 f(x) = x³ - 3x² + 2x - 1 在 x = 2 处的导数值',
          solution: "f'(2) = 2",
          steps: [
            '首先对函数 f(x) = x³ - 3x² + 2x - 1 求导',
            '使用幂函数求导公式：(x^n)′ = n·x^(n-1)',
            "得到 f'(x) = 3x² - 6x + 2",
            "将 x = 2 代入：f'(2) = 3(2)² - 6(2) + 2 = 12 - 12 + 2 = 2"
          ],
          difficulty: 'medium',
          concepts: ['导数', '幂函数求导', '函数值计算']
        },
        {
          id: 'prob_002',
          question: '计算定积分 ∫₀¹ x² dx',
          solution: '1/3',
          steps: [
            '使用基本积分公式：∫ x^n dx = x^(n+1)/(n+1) + C',
            '对 x² 积分：∫ x² dx = x³/3 + C',
            '计算定积分：[x³/3]₀¹ = 1³/3 - 0³/3 = 1/3'
          ],
          difficulty: 'easy',
          concepts: ['定积分', '基本积分公式', '牛顿-莱布尼茨公式']
        }
      ],
      createdAt: new Date(),
      totalProblems: 2,
      estimatedTime: 15
    },
    {
      id: 'solution_002',
      fileName: '大学物理力学题目.jpg',
      subject: 'physics',
      subjectName: '大学物理',
      problems: [
        {
          id: 'prob_003',
          question: '一个质量为 2kg 的物体在恒力 F = 10N 作用下从静止开始运动，求 3s 后的速度',
          solution: 'v = 15 m/s',
          steps: [
            '根据牛顿第二定律：F = ma',
            '计算加速度：a = F/m = 10N / 2kg = 5 m/s²',
            '使用运动学公式：v = v₀ + at',
            '由于从静止开始，v₀ = 0',
            '计算最终速度：v = 0 + 5 × 3 = 15 m/s'
          ],
          difficulty: 'easy',
          concepts: ['牛顿第二定律', '运动学', '匀加速直线运动']
        }
      ],
      createdAt: new Date(Date.now() - 3600000),
      totalProblems: 1,
      estimatedTime: 8
    }
  ]
};

// PPT生成模块Mock数据
const mockPPTData = {
  templates: [
    {
      id: 'tpl_001',
      name: '学术报告模板',
      category: 'academic',
      preview: '/api/templates/academic/preview.jpg',
      description: '适用于学术论文答辩和研究报告'
    },
    {
      id: 'tpl_002',
      name: '商务演示模板',
      category: 'business',
      preview: '/api/templates/business/preview.jpg',
      description: '适用于商业计划和项目汇报'
    },
    {
      id: 'tpl_003',
      name: '教学课件模板',
      category: 'education',
      preview: '/api/templates/education/preview.jpg',
      description: '适用于课堂教学和培训'
    }
  ],
  projects: [
    {
      id: 'ppt_001',
      title: '人工智能发展趋势',
      template: 'tpl_001',
      slides: 15,
      status: 'completed',
      createdAt: new Date('2024-12-25'),
      updatedAt: new Date('2024-12-27')
    }
  ]
};

// 论文写作模块Mock数据
const mockPaperData = {
  types: ['学术论文', '毕业论文', '研究报告', '综述论文', '会议论文'],
  projects: [
    {
      id: 'paper_001',
      title: '基于深度学习的图像识别研究',
      type: '学术论文',
      status: 'draft',
      outline: [
        '摘要',
        '引言',
        '相关工作',
        '方法论',
        '实验结果',
        '结论与展望',
        '参考文献'
      ],
      wordCount: 8500,
      targetWords: 10000,
      createdAt: new Date('2024-12-20'),
      updatedAt: new Date('2024-12-27')
    }
  ],
  references: [
    {
      id: 'ref_001',
      title: 'Deep Learning for Computer Vision',
      authors: ['Ian Goodfellow', 'Yoshua Bengio'],
      year: 2023,
      journal: 'Nature Machine Intelligence',
      citation: 'Goodfellow, I., & Bengio, Y. (2023). Deep Learning for Computer Vision. Nature Machine Intelligence, 5(2), 123-145.'
    }
  ]
};

// AI痕迹消除模块Mock数据
const mockTraceData = {
  detectionResults: [
    {
      id: 'trace_001',
      fileName: 'research_paper.docx',
      aiProbability: 0.75,
      detectedSections: [
        {
          section: '引言部分',
          confidence: 0.8,
          suggestions: ['增加个人观点', '调整语言风格', '添加具体案例']
        },
        {
          section: '结论部分',
          confidence: 0.7,
          suggestions: ['重新组织逻辑', '增加原创性表述']
        }
      ],
      modificationSuggestions: [
        '使用更多主观性表达',
        '增加个人经验和观点',
        '调整句式结构',
        '添加具体的数据和案例'
      ]
    }
  ]
};

// API处理器
export const handlers = [
  // ===== AI对话模块API =====
  
  // 发送消息
  http.post('/api/v1/chat/send', async ({ request }) => {
    await delay(800); // 模拟网络延迟
    
    const body = await request.json() as any;
    const { message, conversationId, attachments } = body;
    
    // 智能分析用户问题
    const lowerMessage = message.toLowerCase();
    
    // 生成智能AI回复
    let aiResponse;
    
    // 高等数学相关
    if (lowerMessage.includes('积分') || lowerMessage.includes('导数') || lowerMessage.includes('微积分')) {
      aiResponse = {
        id: generateId(),
        type: 'ai',
        content: `## 📚 高等数学解题助手

**您的问题：** ${message}

**专业解答：**

### 📖 理论基础
${lowerMessage.includes('积分') ? `
积分是微积分的重要组成部分，用于计算曲线下的面积、体积等。

**基本积分公式：**
- ∫ x^n dx = x^(n+1)/(n+1) + C
- ∫ e^x dx = e^x + C  
- ∫ sin(x) dx = -cos(x) + C
- ∫ cos(x) dx = sin(x) + C
` : `
导数描述函数的变化率，是微积分的核心概念。

**基本求导公式：**
- (x^n)' = nx^(n-1)
- (e^x)' = e^x
- (sin x)' = cos x
- (cos x)' = -sin x
`}

### 🎯 解题示例
**问题：** 计算 ∫₀¹ x² dx

**步骤：**
1. 应用幂函数积分公式：∫ x² dx = x³/3 + C
2. 计算定积分：[x³/3]₀¹ = 1/3 - 0 = 1/3
3. 几何意义：函数 y = x² 在 [0,1] 区间下的面积

**相关知识点：**
- 牛顿-莱布尼茨公式
- 定积分几何意义
- 幂函数性质

需要更详细的解答，请告诉我具体的题目！`,
        timestamp: new Date(),
        attachments: [{
          type: 'chart',
          data: {
            title: '函数 y = x² 图像',
            type: 'line',
            data: Array.from({length: 21}, (_, i) => {
              const x = i * 0.1;
              return { x: x, y: x * x };
            })
          }
        }]
      };
    }
    // 物理学相关
    else if (lowerMessage.includes('物理') || lowerMessage.includes('力学') || lowerMessage.includes('电磁')) {
      aiResponse = {
        id: generateId(),
        type: 'ai',
        content: `## ⚛️ 大学物理解题助手

**您的问题：** ${message}

**物理分析：**

### 🔬 基本原理
${lowerMessage.includes('力学') ? `
**牛顿运动定律：**
1. 第一定律（惯性定律）：物体保持静止或匀速直线运动
2. 第二定律：F = ma，力等于质量乘以加速度
3. 第三定律：作用力与反作用力大小相等方向相反
` : `
**电磁学基础：**
- 库仑定律：F = kq₁q₂/r²
- 电场强度：E = F/q
- 欧姆定律：U = IR
- 法拉第电磁感应定律
`}

### 🧮 计算示例
**问题：** 质量2kg的物体受10N恒力，求3s后速度

**解答：**
1. 已知：m=2kg, F=10N, t=3s, v₀=0
2. 由F=ma得：a = F/m = 10/2 = 5 m/s²
3. 由v = v₀ + at得：v = 0 + 5×3 = 15 m/s

**物理图像：**
v-t图为过原点的直线，斜率等于加速度。

请提供具体的物理问题，我会给出详细的分析和解答！`,
        timestamp: new Date(),
        attachments: [{
          type: 'chart',
          data: {
            title: '匀加速运动 v-t 图',
            type: 'line',
            data: Array.from({length: 11}, (_, i) => {
              const t = i * 0.3;
              return { x: t, y: 5 * t };
            })
          }
        }]
      };
    }
    // 图像识别相关
    else if (attachments && attachments.length > 0) {
      const fileType = attachments[0].type;
      aiResponse = {
        id: generateId(),
        type: 'ai',
        content: `## 🖼️ 图像智能识别

**检测到上传文件：** ${attachments.length} 个文件

**识别分析：**

### 🔍 图像内容识别
我已接收到您上传的${fileType === 'image' ? '图像' : '文档'}文件，具备以下识别能力：

**数学内容识别：**
- 手写/印刷数学公式（准确率>95%）
- 几何图形和图表识别
- 函数图像和坐标系识别

**物理图示识别：**
- 力学分析图（受力图、运动图）
- 电路图和电学元件
- 光学实验装置图

**化学结构识别：**
- 分子结构式和化学方程式
- 实验装置和化学仪器
- 反应机理图

### 📝 处理建议
1. **具体描述：** 请告诉我您想了解图片中的哪个具体问题
2. **题目类型：** 说明是哪个学科领域的问题
3. **详细分析：** 我会提供分步骤的解答过程

**示例提问：**
- "请解答图片中的微积分题目"
- "分析这个力学图的受力情况"  
- "识别化学方程式并配平"

请描述您需要解决的具体问题，我会提供专业的学术解答！`,
        timestamp: new Date(),
        attachments: []
      };
    }
    // 函数图像生成
    else if (lowerMessage.includes('函数') || lowerMessage.includes('图像') || lowerMessage.includes('画') || lowerMessage.includes('绘制')) {
      aiResponse = {
        id: generateId(),
        type: 'ai',
        content: `## 📈 函数图像生成器

**图像生成请求：** ${message}

**函数分析：**

### 📊 常见函数类型
**基础函数：**
- 线性函数：y = ax + b
- 二次函数：y = ax² + bx + c  
- 幂函数：y = xⁿ
- 指数函数：y = aˣ
- 对数函数：y = log_a(x)
- 三角函数：y = sin(x), cos(x), tan(x)

### 🎨 图像特征分析
- **定义域与值域**
- **单调性与极值点**
- **奇偶性与周期性**
- **渐近线与间断点**

**示例图像生成：**
下图展示了几个典型函数的对比，帮助理解不同函数的特征。

如需生成特定函数图像，请告诉我：
1. 具体的函数表达式
2. 自变量的取值范围
3. 是否需要特殊标记（极值点、拐点等）`,
        timestamp: new Date(),
        attachments: [
          {
            type: 'chart',
            data: {
              title: '常见函数对比',
              type: 'line',
              data: [
                {
                  name: 'y = x²',
                  points: Array.from({length: 21}, (_, i) => {
                    const x = (i - 10) / 2;
                    return { x, y: x * x };
                  })
                },
                {
                  name: 'y = sin(x)',  
                  points: Array.from({length: 21}, (_, i) => {
                    const x = (i - 10) / 2;
                    return { x, y: Math.sin(x) };
                  })
                }
              ]
            }
          }
        ]
      };
    }
    // 化学相关
    else if (lowerMessage.includes('化学') || lowerMessage.includes('反应') || lowerMessage.includes('分子')) {
      aiResponse = {
        id: generateId(),
        type: 'ai',
        content: `## 🧪 化学问题解答

**您的问题：** ${message}

**化学分析：**

### ⚗️ 基础化学原理
**化学反应类型：**
- 化合反应：A + B → AB
- 分解反应：AB → A + B
- 置换反应：A + BC → AC + B
- 复分解反应：AB + CD → AD + CB

### 🧮 化学计量学
**重要公式：**
- 物质的量：n = m/M = V/Vm = N/NA
- 阿伏伽德罗常数：NA = 6.022×10²³ mol⁻¹
- 标准状况摩尔体积：Vm = 22.4 L/mol

### 🔬 实例分析
**甲烷燃烧反应：**
CH₄ + 2O₂ → CO₂ + 2H₂O

**反应特征：**
- 放热反应（ΔH < 0）
- 完全氧化反应
- 1:2:1:2的计量比

请提供具体的化学问题，我会给出详细的分析、计算和解答！`,
        timestamp: new Date(),
        attachments: []
      };
    }
    // 编程算法相关
    else if (lowerMessage.includes('代码') || lowerMessage.includes('编程') || lowerMessage.includes('算法')) {
      aiResponse = {
        id: generateId(),
        type: 'ai',
        content: `## 💻 编程算法助手

**编程问题：** ${message}

**算法分析：**

### 🔧 常用算法分类
**排序算法：**
- 冒泡排序：O(n²)，简单易懂
- 快速排序：平均O(n log n)，效率较高
- 归并排序：稳定的O(n log n)

**搜索算法：**
- 线性搜索：O(n)，适用于无序数组
- 二分搜索：O(log n)，适用于有序数组

### 👨‍💻 代码示例

\`\`\`python
# 二分搜索算法
def binary_search(arr, target):
    left, right = 0, len(arr) - 1
    
    while left <= right:
        mid = (left + right) // 2
        if arr[mid] == target:
            return mid
        elif arr[mid] < target:
            left = mid + 1
        else:
            right = mid - 1
    
    return -1

# 使用示例
numbers = [1, 3, 5, 7, 9, 11]
result = binary_search(numbers, 7)
print(f"位置: {result}")  # 输出: 3
\`\`\`

**复杂度分析：**
- 时间复杂度：O(log n)
- 空间复杂度：O(1)

请告诉我您需要解决的具体编程问题，我会提供相应的算法和代码实现！`,
        timestamp: new Date(),
        attachments: []
      };
    }
    // 默认通用回复  
    else {
      aiResponse = {
        id: generateId(),
        type: 'ai',
        content: `## 🤖 高校AI智能助手

感谢您的提问：**"${message}"**

### 🎓 我的专业能力

**理科解答专长：**
- **高等数学：** 微积分、线性代数、概率统计、复变函数
- **大学物理：** 力学、电磁学、热力学、量子物理
- **化学分析：** 有机化学、无机化学、物理化学
- **计算机科学：** 编程算法、数据结构、软件工程

**多模态交互：**
- **文字理解：** 支持自然语言和LaTeX数学公式
- **图像识别：** 数学公式、物理图示、化学结构、几何图形
- **文档处理：** PDF、Word文档内容分析

**图表生成：**
- **数学图形：** 函数图像、几何图形、统计图表
- **物理图示：** v-t图、F-x图、电路图分析
- **数据可视化：** 柱状图、折线图、饼图、散点图

### 💡 使用建议

您可以这样提问：
1. **具体题目：** "求∫₀¹ x²dx的值"
2. **概念解释：** "什么是牛顿第二定律？"
3. **图像生成：** "画出y=sin(x)的函数图像"
4. **文件分析：** 上传图片或文档让我分析

**请告诉我您想了解的具体问题，我会提供专业的学术解答！** 📚✨`,
        timestamp: new Date(),
        attachments: Math.random() > 0.8 ? [{
          type: 'chart',
          data: {
            title: '学科覆盖范围',
            type: 'pie',
            data: [
              { name: '高等数学', value: 30 },
              { name: '大学物理', value: 25 },
              { name: '化学', value: 20 },
              { name: '计算机', value: 15 },
              { name: '其他', value: 10 }
            ]
          }
        }] : []
      };
    }
    
    return HttpResponse.json({
      success: true,
      data: {
        message: aiResponse,
        conversationId: conversationId || generateId()
      }
    });
  }),
  
  // 获取对话历史
  http.get('/api/v1/chat/conversations', async () => {
    await delay(300);
    
    return HttpResponse.json({
      success: true,
      data: mockChatData.conversations
    });
  }),
  
  // 创建新对话
  http.post('/api/v1/chat/conversations', async ({ request }) => {
    await delay(200);
    
    const body = await request.json() as any;
    const newConversation = {
      id: generateId(),
      title: body.title || '新对话',
      messages: [],
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    return HttpResponse.json({
      success: true,
      data: newConversation
    });
  }),
  
  // ===== 作业辅导模块API =====
  
  // 文件上传处理
  http.post('/api/v1/homework/upload', async ({ request }) => {
    await delay(2000);
    
    const formData = await request.formData();
    const files = formData.getAll('files') as File[];
    
    const uploadResults = files.map(file => ({
      id: generateId(),
      name: file.name,
      type: file.type,
      size: file.size,
      uploadTime: new Date(),
      status: 'completed'
    }));
    
    return HttpResponse.json({
      success: true,
      data: uploadResults
    });
  }),
  
  // 文件解析和解答生成
  http.post('/api/v1/homework/analyze', async ({ request }) => {
    await delay(3000); // 模拟AI分析时间
    
    const body = await request.json() as any;
    const { fileId, fileName } = body;
    
    // 根据文件名生成模拟解答
    const subject = fileName.toLowerCase().includes('物理') ? 'physics' : 
                   fileName.toLowerCase().includes('化学') ? 'chemistry' : 'math';
    
    const subjectNames = {
      math: '高等数学',
      physics: '大学物理',
      chemistry: '大学化学'
    };
    
    const mockProblems = {
      math: [
        {
          id: generateId(),
          question: '求极限 lim(x→0) (sin x)/x',
          solution: '1',
          steps: [
            '这是一个重要极限',
            '当 x → 0 时，sin x 与 x 是等价无穷小',
            '因此 lim(x→0) (sin x)/x = 1'
          ],
          difficulty: 'medium',
          concepts: ['极限', '重要极限', '等价无穷小']
        }
      ],
      physics: [
        {
          id: generateId(),
          question: '计算弹簧振子的周期公式',
          solution: 'T = 2π√(m/k)',
          steps: [
            '弹簧振子满足简谐运动方程：ma = -kx',
            '得到微分方程：d²x/dt² = -(k/m)x',
            '角频率 ω = √(k/m)',
            '周期 T = 2π/ω = 2π√(m/k)'
          ],
          difficulty: 'hard',
          concepts: ['简谐运动', '弹簧振子', '周期公式']
        }
      ],
      chemistry: [
        {
          id: generateId(),
          question: '计算理想气体状态方程中的摩尔数',
          solution: 'n = PV/(RT)',
          steps: [
            '理想气体状态方程：PV = nRT',
            '解出摩尔数：n = PV/(RT)',
            '其中 R = 8.314 J/(mol·K) 为气体常数'
          ],
          difficulty: 'easy',
          concepts: ['理想气体', '状态方程', '摩尔数']
        }
      ]
    };
    
    const solution = {
      id: generateId(),
      fileName,
      subject,
      subjectName: subjectNames[subject as keyof typeof subjectNames],
      problems: mockProblems[subject as keyof typeof mockProblems],
      createdAt: new Date(),
      totalProblems: mockProblems[subject as keyof typeof mockProblems].length,
      estimatedTime: Math.ceil(mockProblems[subject as keyof typeof mockProblems].length * 5)
    };
    
    return HttpResponse.json({
      success: true,
      data: solution
    });
  }),
  
  // 获取解答历史
  http.get('/api/v1/homework/solutions', async () => {
    await delay(300);
    
    return HttpResponse.json({
      success: true,
      data: mockHomeworkData.solutions
    });
  }),
  
  // 获取科目列表
  http.get('/api/v1/homework/subjects', async () => {
    await delay(100);
    
    return HttpResponse.json({
      success: true,
      data: mockHomeworkData.subjects
    });
  }),
  
  // 文档下载生成
  http.post('/api/v1/homework/download', async ({ request }) => {
    await delay(1000);
    
    const body = await request.json() as any;
    const { solutionId, format } = body;
    
    // 模拟文档生成
    const downloadUrl = `/api/v1/homework/files/${solutionId}.${format}`;
    
    return HttpResponse.json({
      success: true,
      data: {
        downloadUrl,
        fileName: `作业解答.${format}`,
        size: Math.floor(Math.random() * 1000000) + 500000 // 0.5-1.5MB
      }
    });
  }),
  
  // ===== PPT生成模块API =====
  
  // 获取模板列表
  http.get('/api/v1/ppt/templates', async () => {
    await delay(400);
    
    return HttpResponse.json({
      success: true,
      data: mockPPTData.templates
    });
  }),
  
  // 创建PPT项目
  http.post('/api/v1/ppt/create', async ({ request }) => {
    await delay(2000); // 模拟较长的生成时间
    
    const body = await request.json() as any;
    const newProject = {
      id: generateId(),
      title: body.title,
      template: body.templateId,
      slides: Math.floor(Math.random() * 20) + 10,
      status: 'completed',
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    return HttpResponse.json({
      success: true,
      data: newProject
    });
  }),
  
  // 获取PPT项目列表
  http.get('/api/v1/ppt/projects', async () => {
    await delay(300);
    
    return HttpResponse.json({
      success: true,
      data: mockPPTData.projects
    });
  }),
  
  // ===== 论文写作模块API =====
  
  // 获取论文类型
  http.get('/api/v1/paper/types', async () => {
    await delay(100);
    
    return HttpResponse.json({
      success: true,
      data: mockPaperData.types
    });
  }),
  
  // 创建论文项目
  http.post('/api/v1/paper/create', async ({ request }) => {
    await delay(1500);
    
    const body = await request.json() as any;
    const newProject = {
      id: generateId(),
      title: body.title,
      type: body.type,
      status: 'draft',
      outline: [
        '摘要',
        '引言',
        '文献综述',
        '研究方法',
        '结果分析',
        '讨论',
        '结论',
        '参考文献'
      ],
      wordCount: 0,
      targetWords: body.targetWords || 8000,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    return HttpResponse.json({
      success: true,
      data: newProject
    });
  }),
  
  // 获取论文项目列表
  http.get('/api/v1/paper/projects', async () => {
    await delay(300);
    
    return HttpResponse.json({
      success: true,
      data: mockPaperData.projects
    });
  }),
  
  // 获取参考文献
  http.get('/api/v1/paper/references', async () => {
    await delay(400);
    
    return HttpResponse.json({
      success: true,
      data: mockPaperData.references
    });
  }),
  
  // ===== AI痕迹消除模块API =====
  
  // 上传文件检测
  http.post('/api/v1/trace/detect', async ({ request }) => {
    await delay(3000); // 模拟检测时间
    
    const formData = await request.formData();
    const file = formData.get('file') as File;
    
    const result = {
      id: generateId(),
      fileName: file?.name || 'unknown.docx',
      aiProbability: Math.random() * 0.4 + 0.4, // 0.4-0.8之间
      detectedSections: [
        {
          section: '引言部分',
          confidence: Math.random() * 0.3 + 0.6,
          suggestions: ['增加个人观点', '调整语言风格', '添加具体案例']
        },
        {
          section: '主体部分',
          confidence: Math.random() * 0.3 + 0.5,
          suggestions: ['重新组织逻辑', '增加原创性表述', '添加数据支撑']
        }
      ],
      modificationSuggestions: [
        '使用更多主观性表达',
        '增加个人经验和观点',
        '调整句式结构',
        '添加具体的数据和案例',
        '增强逻辑连贯性'
      ]
    };
    
    return HttpResponse.json({
      success: true,
      data: result
    });
  }),
  
  // 获取检测历史
  http.get('/api/v1/trace/history', async () => {
    await delay(300);
    
    return HttpResponse.json({
      success: true,
      data: mockTraceData.detectionResults
    });
  }),
  
  // ===== 通用API =====
  
  // 文件上传
  http.post('/api/v1/upload', async ({ request }) => {
    await delay(1000);
    
    const formData = await request.formData();
    const file = formData.get('file') as File;
    
    return HttpResponse.json({
      success: true,
      data: {
        url: `/uploads/${generateId()}_${file?.name}`,
        filename: file?.name,
        size: file?.size,
        type: file?.type
      }
    });
  }),
  
  // 健康检查
  http.get('/api/v1/health', async () => {
    return HttpResponse.json({
      status: 'OK',
      message: 'Mock API服务运行正常',
      timestamp: new Date().toISOString(),
      version: '1.0.0-mock'
    });
  })
]; 