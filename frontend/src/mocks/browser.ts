import { setupWorker } from "msw/browser";
import { handlers } from "./handlers";

// 创建MSW worker实例
export const worker = setupWorker(...handlers);

// 启动worker的配置
export const startWorker = async () => {
  if (typeof window !== 'undefined') {
    try {
      await worker.start({
        onUnhandledRequest: 'warn', // 对未处理的请求发出警告
        serviceWorker: {
          url: '/mockServiceWorker.js', // Service Worker文件路径
        },
      });
      console.log('🎭 MSW Mock服务已启动');
      console.log('📋 已加载的API处理器数量:', handlers.length);
      console.log('🔧 Mock环境配置完成，前端开发就绪！');
    } catch (error) {
      console.error('❌ MSW启动失败:', error);
    }
  }
};

// 停止worker
export const stopWorker = () => {
  worker.stop();
  console.log('🛑 MSW Mock服务已停止');
}; 