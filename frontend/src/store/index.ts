// ============================================================================
// 高校AI助手 - Zustand状态管理
// ============================================================================

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { 
  User, 
  UIState,
} from '../types';

// UI状态管理
interface UIStore extends UIState {
  setSidebarCollapsed: (collapsed: boolean) => void;
  setTheme: (theme: 'light' | 'dark') => void;
  setLoading: (loading: boolean) => void;
  addNotification: (notification: any) => void;
  removeNotification: (id: string) => void;
}

// 用户状态管理
interface UserStore {
  user: User | null;
  isAuthenticated: boolean;
  login: (user: User) => void;
  logout: () => void;
  updateUser: (updates: Partial<User>) => void;
}

// UI状态
export const useUI = create<UIStore>()(
  persist(
    (set) => ({
      sidebarCollapsed: false,
      theme: 'light',
      loading: false,
      notifications: [],
      
      setSidebarCollapsed: (collapsed) => set({ sidebarCollapsed: collapsed }),
      setTheme: (theme) => set({ theme }),
      setLoading: (loading) => set({ loading }),
      addNotification: (notification) => 
        set((state) => ({ 
          notifications: [...state.notifications, { ...notification, id: Date.now().toString() }] 
        })),
      removeNotification: (id) => 
        set((state) => ({ 
          notifications: state.notifications.filter(n => n.id !== id) 
        })),
    }),
    {
      name: 'ui-storage',
      partialize: (state) => ({ 
        sidebarCollapsed: state.sidebarCollapsed, 
        theme: state.theme 
      }),
    }
  )
);

// 用户状态
export const useUser = create<UserStore>()(
  persist(
    (set) => ({
      user: null,
      isAuthenticated: false,
      
      login: (user) => set({ user, isAuthenticated: true }),
      logout: () => set({ user: null, isAuthenticated: false }),
      updateUser: (updates) => 
        set((state) => ({ 
          user: state.user ? { ...state.user, ...updates } : null 
        })),
    }),
    {
      name: 'user-storage',
    }
  )
);

// 便捷的Hook函数
export const useUIActions = () => {
  const setSidebarCollapsed = useUI(state => state.setSidebarCollapsed);
  const setTheme = useUI(state => state.setTheme);
  const setLoading = useUI(state => state.setLoading);
  const addNotification = useUI(state => state.addNotification);
  const removeNotification = useUI(state => state.removeNotification);

  return {
    setSidebarCollapsed,
    setTheme,
    setLoading,
    addNotification,
    removeNotification,
  };
};

export const useUserActions = () => {
  const login = useUser(state => state.login);
  const logout = useUser(state => state.logout);
  const updateUser = useUser(state => state.updateUser);

  return {
    login,
    logout,
    updateUser,
  };
};

export default useUI; 