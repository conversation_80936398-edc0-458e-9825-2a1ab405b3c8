import React from 'react';
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import AppLayout from './components/layout/AppLayout';
import Dashboard from './pages/dashboard/Dashboard';
import Chat from './pages/chat/Chat';
import Homework from './pages/homework/Homework';
import Paper from './pages/paper/Paper';
import PPT from './pages/ppt/PPT';
import Trace from './pages/trace/Trace';
import ReduceAI from './pages/reduce-ai/ReduceAI';
import Settings from './pages/settings/Settings';
import Help from './pages/help/Help';
import Login from './pages/auth/Login';
import Register from './pages/auth/Register';
import NotFound from './pages/error/NotFound';

const App: React.FC = () => {
  return (
    <BrowserRouter>
      <div className="App">
        <Routes>
          {/* 认证页面 */}
          <Route path="/login" element={<Login />} />
          <Route path="/register" element={<Register />} />
          
          {/* 主应用路由 */}
          <Route path="/" element={<AppLayout />}>
            <Route index element={<Navigate to="/dashboard" replace />} />
            <Route path="dashboard" element={<Dashboard />} />
            <Route path="chat" element={<Chat />} />
            <Route path="homework" element={<Homework />} />
            <Route path="paper" element={<Paper />} />
            <Route path="ppt" element={<PPT />} />
            <Route path="reduce-ai" element={<ReduceAI />} />
            <Route path="trace" element={<Trace />} />
            <Route path="settings" element={<Settings />} />
            <Route path="help" element={<Help />} />
          </Route>
          
          {/* 404页面 */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </div>
    </BrowserRouter>
  );
};

export default App; 