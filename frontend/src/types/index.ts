// 搜题相关类型
export interface SearchQuestion {
  id: string;
  question: string;
  type: 'text' | 'image' | 'screenshot';
  subject: string;
  difficulty: 'easy' | 'medium' | 'hard';
  createdAt: Date;
}

export interface SearchResult {
  id: string;
  question: SearchQuestion;
  answer: string;
  explanation: string;
  steps: string[];
  confidence: number;
  relatedQuestions: SearchQuestion[];
  createdAt: Date;
}

// 用户相关类型
export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  role?: 'student' | 'teacher' | 'admin';
  preferences?: UserPreferences;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  language: 'zh-CN' | 'en-US';
  notifications: {
    email: boolean;
    push: boolean;
    homework: boolean;
    chat: boolean;
  };
}

// 学科类型
export type Subject = 
  | 'math' 
  | 'physics' 
  | 'chemistry' 
  | 'biology'
  | 'english'
  | 'chinese'
  | 'history'
  | 'geography'
  | 'politics'
  | 'programming'
  | 'other';

// API响应类型
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message: string;
  code: number;
}

// 组件Props类型
export interface SearchInputProps {
  onSearch: (query: string, type: 'text' | 'image' | 'screenshot') => void;
  loading?: boolean;
}

export interface QuestionExampleProps {
  examples: SearchQuestion[];
  onExampleClick: (question: SearchQuestion) => void;
}

// Store类型
export interface AppState {
  user: User | null;
  currentSearch: SearchResult | null;
  searchHistory: SearchResult[];
  loading: boolean;
  error: string | null;
}

export interface AppActions {
  setUser: (user: User | null) => void;
  setCurrentSearch: (result: SearchResult | null) => void;
  addToHistory: (result: SearchResult) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

// 应用设置类型
export interface AppSettings {
  theme: 'light' | 'dark' | 'auto';
  language: 'zh-CN' | 'en-US';
  autoSave: boolean;
  showTutorial: boolean;
}

// 聊天相关类型
export interface ChatMessage {
  id: string;
  content: string;
  type: 'user' | 'assistant';
  timestamp: Date;
  attachments?: ChatAttachment[];
}

export interface ChatAttachment {
  id: string;
  type: 'image' | 'file' | 'link';
  url: string;
  name: string;
  size?: number;
}

export interface ChatSession {
  id: string;
  title: string;
  messages: ChatMessage[];
  createdAt: Date;
  updatedAt: Date;
  status: 'active' | 'archived';
}

// 作业相关类型
export interface HomeworkSession {
  id: string;
  title: string;
  subject: string;
  difficulty: 'easy' | 'medium' | 'hard';
  type: string;
  content: string;
  solution?: string;
  status: 'pending' | 'solving' | 'completed';
  score?: number;
  timeSpent?: number;
  createdAt: Date;
  updatedAt: Date;
}

// 论文相关类型
export interface PaperProject {
  id: string;
  title: string;
  type: 'essay' | 'research' | 'report' | 'thesis';
  status: 'draft' | 'writing' | 'reviewing' | 'completed';
  content: string;
  outline?: string[];
  wordCount: number;
  targetWordCount?: number;
  createdAt: Date;
  updatedAt: Date;
}

// PPT相关类型
export interface PPTProject {
  id: string;
  title: string;
  theme: string;
  slideCount: number;
  status: 'draft' | 'generating' | 'completed';
  slides: PPTSlide[];
  createdAt: Date;
  updatedAt: Date;
}

export interface PPTSlide {
  id: string;
  title: string;
  content: string;
  layout: 'title' | 'content' | 'image' | 'chart';
  order: number;
}

// 学习轨迹相关类型
export interface LearningTrace {
  id: string;
  userId: string;
  activity: string;
  subject: string;
  duration: number;
  score?: number;
  details: Record<string, any>;
  timestamp: Date;
}

export interface LearningStats {
  totalTime: number;
  completedHomework: number;
  averageScore: number;
  subjectStats: Record<string, {
    time: number;
    count: number;
    averageScore: number;
  }>;
  weeklyProgress: number;
}

// UI相关类型
export interface UIState {
  sidebarCollapsed: boolean;
  theme: 'light' | 'dark';
  loading: boolean;
  notifications: Notification[];
}

export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
} 