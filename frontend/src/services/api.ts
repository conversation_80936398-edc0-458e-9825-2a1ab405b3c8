// API配置
const API_BASE_URL = import.meta.env.DEV 
  ? 'http://localhost:3001/api/v1' 
  : '/api/v1';

// 请求工具函数
async function request<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`;
  
  const defaultOptions: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  };

  try {
    const response = await fetch(url, defaultOptions);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('API请求失败:', error);
    throw error;
  }
}

// 文件上传工具函数
async function uploadFile(endpoint: string, file: File): Promise<any> {
  const url = `${API_BASE_URL}${endpoint}`;
  const formData = new FormData();
  formData.append('file', file);

  try {
    const response = await fetch(url, {
      method: 'POST',
      body: formData,
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('文件上传失败:', error);
    throw error;
  }
}

// API接口定义
export const api = {
  // AI对话接口
  chat: {
    sendMessage: async (data: {
      message: string;
      conversationId?: string;
      attachments?: any[];
    }) => {
      return request('/chat/send', {
        method: 'POST',
        body: JSON.stringify(data),
      });
    },

    getConversations: async () => {
      return request('/chat/conversations');
    },

    createConversation: async (title?: string) => {
      return request('/chat/conversations', {
        method: 'POST',
        body: JSON.stringify({ title }),
      });
    },
  },

  // 文件上传
  upload: {
    uploadFile: async (file: File) => {
      return uploadFile('/upload', file);
    },
  },

  // 系统接口
  system: {
    health: async () => {
      return request('/health');
    },
  },
};

// 类型定义
export interface Message {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  attachments?: {
    type: 'image' | 'chart' | 'table' | 'file';
    data: any;
  }[];
}

export interface Conversation {
  id: string;
  title: string;
  messages: Message[];
  createdAt: Date;
  updatedAt: Date;
}

export default api; 