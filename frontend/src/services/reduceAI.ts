const API_BASE = 'http://localhost:3001/api/v1';

export interface ProcessedDocument {
  id: string;
  originalFileName: string;
  processedFileName: string;
  originalAIScore: number;
  optimizedAIScore: number;
  reductionPercentage: number;
  fileType: 'pdf' | 'docx' | 'doc';
  fileSize: number;
  processedAt: Date;
  downloadUrl: string;
  status: 'completed' | 'processing' | 'failed';
}

export interface ReduceAIStats {
  totalProcessed: number;
  avgReduction: number;
  recentProcessed: number;
  totalSaved: number;
  successRate: number;
}

export interface APIResponse<T> {
  success: boolean;
  data: T;
  message: string;
  code: number;
}

class ReduceAIService {
  // 处理文档
  async processDocument(file: File): Promise<ProcessedDocument> {
    const formData = new FormData();
    formData.append('document', file);

    const response = await fetch(`${API_BASE}/reduce-ai/process`, {
      method: 'POST',
      body: formData,
    });

    const result: APIResponse<ProcessedDocument> = await response.json();
    
    if (!result.success) {
      throw new Error(result.message);
    }

    return {
      ...result.data,
      processedAt: new Date(result.data.processedAt)
    };
  }

  // 获取处理历史
  async getHistory(): Promise<ProcessedDocument[]> {
    const response = await fetch(`${API_BASE}/reduce-ai/history`);
    const result: APIResponse<ProcessedDocument[]> = await response.json();
    
    if (!result.success) {
      throw new Error(result.message);
    }

    return result.data.map(doc => ({
      ...doc,
      processedAt: new Date(doc.processedAt)
    }));
  }

  // 下载处理后的文档
  async downloadDocument(id: string, fileName: string): Promise<void> {
    const response = await fetch(`${API_BASE}/reduce-ai/download/${id}`);
    
    if (!response.ok) {
      throw new Error('下载失败');
    }

    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  }

  // 删除处理记录
  async deleteRecord(id: string): Promise<void> {
    const response = await fetch(`${API_BASE}/reduce-ai/history/${id}`, {
      method: 'DELETE',
    });

    const result: APIResponse<null> = await response.json();
    
    if (!result.success) {
      throw new Error(result.message);
    }
  }

  // 获取统计数据
  async getStats(): Promise<ReduceAIStats> {
    const response = await fetch(`${API_BASE}/reduce-ai/stats`);
    const result: APIResponse<ReduceAIStats> = await response.json();
    
    if (!result.success) {
      throw new Error(result.message);
    }

    return result.data;
  }
}

export const reduceAIService = new ReduceAIService(); 