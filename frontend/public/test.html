<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高校AI助手 - 测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            text-align: center;
            max-width: 600px;
        }
        h1 {
            color: #1890ff;
            margin-bottom: 20px;
        }
        .feature-list {
            text-align: left;
            margin: 30px 0;
        }
        .feature-item {
            background: #f0f2f5;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #1890ff;
        }
        .status {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            border-radius: 6px;
            padding: 15px;
            color: #52c41a;
            margin: 20px 0;
        }
        .action-buttons {
            margin-top: 30px;
        }
        .btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            margin: 0 10px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background: #40a9ff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎓 高校AI助手</h1>
        <p>College AI Assistant - 您的智能学习伙伴</p>
        
        <div class="status">
            ✅ 前端服务正常运行 | 后端服务已连接
        </div>
        
        <div class="feature-list">
            <div class="feature-item">
                <strong>💬 AI对话</strong> - 智能问答，支持数学公式和代码高亮
            </div>
            <div class="feature-item">
                <strong>📊 PPT生成</strong> - 一键生成精美演示文稿
            </div>
            <div class="feature-item">
                <strong>📝 论文助手</strong> - 协助撰写学术论文
            </div>
            <div class="feature-item">
                <strong>🧮 作业助手</strong> - 理科作业智能解答
            </div>
            <div class="feature-item">
                <strong>🔍 痕迹消除</strong> - AI文档检测与优化
            </div>
        </div>
        
        <div class="action-buttons">
            <a href="http://localhost:3001" class="btn">进入主应用</a>
            <a href="http://localhost:3002/health" class="btn">检查后端API</a>
        </div>
        
        <p style="color: #666; margin-top: 20px; font-size: 14px;">
            当前时间: <span id="current-time"></span><br>
            前端端口: 3001 | 后端端口: 3002
        </p>
    </div>

    <script>
        // 显示当前时间
        function updateTime() {
            document.getElementById('current-time').textContent = new Date().toLocaleString('zh-CN');
        }
        updateTime();
        setInterval(updateTime, 1000);
        
        // 测试后端连接
        fetch('http://localhost:3002/health')
            .then(response => response.json())
            .then(data => {
                console.log('后端服务状态:', data);
            })
            .catch(error => {
                console.error('后端连接失败:', error);
            });
    </script>
</body>
</html> 