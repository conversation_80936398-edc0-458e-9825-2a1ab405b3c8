# 高校AI助手 - 数据库设计文档

## 1. 数据库架构概览

### 1.1 多数据库策略
- **主数据库**: PostgreSQL 16 (关系型数据)
- **缓存层**: Redis 7.x (会话、缓存)
- **向量数据库**: Qdrant (AI知识库)
- **文件存储**: MinIO/S3 (文件和媒体)
- **搜索引擎**: Elasticsearch (全文搜索)

### 1.2 数据库连接配置
```typescript
interface DatabaseConfig {
  postgresql: {
    host: string;
    port: number;
    database: string;
    username: string;
    password: string;
    ssl: boolean;
    maxConnections: number;
  };
  redis: {
    host: string;
    port: number;
    password?: string;
    db: number;
    maxRetriesPerRequest: number;
  };
  qdrant: {
    url: string;
    apiKey?: string;
    collectionName: string;
    vectorSize: number;
  };
}
```

## 2. PostgreSQL 主数据库设计

### 2.1 用户管理表结构

#### 2.1.1 用户表 (users)
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role user_role_enum NOT NULL DEFAULT 'student',
    status user_status_enum NOT NULL DEFAULT 'active',
    email_verified BOOLEAN DEFAULT FALSE,
    phone VARCHAR(20),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login_at TIMESTAMP WITH TIME ZONE,
    deleted_at TIMESTAMP WITH TIME ZONE
);

CREATE TYPE user_role_enum AS ENUM ('student', 'teacher', 'admin', 'super_admin');
CREATE TYPE user_status_enum AS ENUM ('active', 'inactive', 'suspended', 'pending');

-- 索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_users_created_at ON users(created_at);
```

#### 2.1.2 用户配置表 (user_profiles)
```sql
CREATE TABLE user_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    display_name VARCHAR(200),
    avatar_url VARCHAR(500),
    university VARCHAR(200),
    major VARCHAR(200),
    academic_year INTEGER,
    student_id VARCHAR(50),
    bio TEXT,
    date_of_birth DATE,
    gender gender_enum,
    country VARCHAR(100),
    city VARCHAR(100),
    timezone VARCHAR(50) DEFAULT 'UTC',
    language VARCHAR(10) DEFAULT 'zh-CN',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TYPE gender_enum AS ENUM ('male', 'female', 'other', 'prefer_not_to_say');

ALTER TABLE user_profiles ADD CONSTRAINT unique_user_profile UNIQUE (user_id);
CREATE INDEX idx_user_profiles_university ON user_profiles(university);
CREATE INDEX idx_user_profiles_major ON user_profiles(major);
```

#### 2.1.3 用户偏好设置表 (user_preferences)
```sql
CREATE TABLE user_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    theme VARCHAR(20) DEFAULT 'light',
    language VARCHAR(10) DEFAULT 'zh-CN',
    timezone VARCHAR(50) DEFAULT 'UTC',
    notification_email BOOLEAN DEFAULT TRUE,
    notification_push BOOLEAN DEFAULT TRUE,
    ai_model_preference VARCHAR(50) DEFAULT 'gpt-4',
    auto_save BOOLEAN DEFAULT TRUE,
    privacy_level privacy_level_enum DEFAULT 'standard',
    data_retention_days INTEGER DEFAULT 365,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TYPE privacy_level_enum AS ENUM ('minimal', 'standard', 'enhanced');

ALTER TABLE user_preferences ADD CONSTRAINT unique_user_preferences UNIQUE (user_id);
```

### 2.2 AI对话系统表结构

#### 2.2.1 对话表 (conversations)
```sql
CREATE TABLE conversations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(500) NOT NULL,
    type conversation_type_enum NOT NULL,
    status conversation_status_enum DEFAULT 'active',
    model_used VARCHAR(100),
    total_tokens INTEGER DEFAULT 0,
    total_cost DECIMAL(10,4) DEFAULT 0,
    metadata JSONB,
    tags TEXT[],
    is_favorite BOOLEAN DEFAULT FALSE,
    is_shared BOOLEAN DEFAULT FALSE,
    share_token UUID,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

CREATE TYPE conversation_type_enum AS ENUM (
    'general_chat', 'study_help', 'homework_assistance', 
    'paper_writing', 'ppt_creation', 'image_analysis'
);

CREATE TYPE conversation_status_enum AS ENUM ('active', 'archived', 'deleted');

-- 索引
CREATE INDEX idx_conversations_user_id ON conversations(user_id);
CREATE INDEX idx_conversations_type ON conversations(type);
CREATE INDEX idx_conversations_status ON conversations(status);
CREATE INDEX idx_conversations_created_at ON conversations(created_at);
CREATE INDEX idx_conversations_tags ON conversations USING GIN(tags);
CREATE INDEX idx_conversations_metadata ON conversations USING GIN(metadata);
```

#### 2.2.2 消息表 (messages)
```sql
CREATE TABLE messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id UUID NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
    role message_role_enum NOT NULL,
    content TEXT NOT NULL,
    content_type content_type_enum DEFAULT 'text',
    attachments JSONB,
    metadata JSONB,
    tokens INTEGER,
    cost DECIMAL(10,4),
    processing_time INTEGER, -- 毫秒
    model_used VARCHAR(100),
    parent_message_id UUID REFERENCES messages(id),
    is_edited BOOLEAN DEFAULT FALSE,
    edit_history JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TYPE message_role_enum AS ENUM ('user', 'assistant', 'system');
CREATE TYPE content_type_enum AS ENUM ('text', 'image', 'file', 'code', 'math', 'chart');

-- 索引
CREATE INDEX idx_messages_conversation_id ON messages(conversation_id);
CREATE INDEX idx_messages_role ON messages(role);
CREATE INDEX idx_messages_created_at ON messages(created_at);
CREATE INDEX idx_messages_parent_id ON messages(parent_message_id);
```

#### 2.2.3 文件附件表 (attachments)
```sql
CREATE TABLE attachments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    message_id UUID REFERENCES messages(id) ON DELETE CASCADE,
    filename VARCHAR(500) NOT NULL,
    original_filename VARCHAR(500) NOT NULL,
    file_type VARCHAR(100) NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(200),
    storage_path VARCHAR(1000) NOT NULL,
    thumbnail_path VARCHAR(1000),
    is_processed BOOLEAN DEFAULT FALSE,
    processing_result JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 索引
CREATE INDEX idx_attachments_message_id ON attachments(message_id);
CREATE INDEX idx_attachments_file_type ON attachments(file_type);
CREATE INDEX idx_attachments_created_at ON attachments(created_at);
```

### 2.3 PPT生成系统表结构

#### 2.3.1 PPT项目表 (ppt_projects)
```sql
CREATE TABLE ppt_projects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(500) NOT NULL,
    description TEXT,
    template_id UUID REFERENCES ppt_templates(id),
    status project_status_enum DEFAULT 'draft',
    settings JSONB,
    total_slides INTEGER DEFAULT 0,
    last_exported_at TIMESTAMP WITH TIME ZONE,
    is_public BOOLEAN DEFAULT FALSE,
    share_token UUID,
    collaborators UUID[], -- 协作者用户ID数组
    tags TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

CREATE TYPE project_status_enum AS ENUM ('draft', 'in_progress', 'completed', 'exported', 'archived');

-- 索引
CREATE INDEX idx_ppt_projects_user_id ON ppt_projects(user_id);
CREATE INDEX idx_ppt_projects_status ON ppt_projects(status);
CREATE INDEX idx_ppt_projects_template_id ON ppt_projects(template_id);
CREATE INDEX idx_ppt_projects_tags ON ppt_projects USING GIN(tags);
```

#### 2.3.2 PPT模板表 (ppt_templates)
```sql
CREATE TABLE ppt_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(200) NOT NULL,
    description TEXT,
    category template_category_enum NOT NULL,
    thumbnail_url VARCHAR(500),
    preview_images TEXT[],
    template_data JSONB NOT NULL, -- 模板结构数据
    default_settings JSONB,
    is_premium BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    usage_count INTEGER DEFAULT 0,
    rating DECIMAL(3,2) DEFAULT 0,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TYPE template_category_enum AS ENUM (
    'academic', 'business', 'education', 'science', 
    'engineering', 'arts', 'medical', 'general'
);

-- 索引
CREATE INDEX idx_ppt_templates_category ON ppt_templates(category);
CREATE INDEX idx_ppt_templates_is_active ON ppt_templates(is_active);
CREATE INDEX idx_ppt_templates_rating ON ppt_templates(rating);
```

#### 2.3.3 PPT幻灯片表 (ppt_slides)
```sql
CREATE TABLE ppt_slides (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID NOT NULL REFERENCES ppt_projects(id) ON DELETE CASCADE,
    slide_number INTEGER NOT NULL,
    title VARCHAR(500),
    layout_type layout_type_enum NOT NULL,
    content JSONB NOT NULL, -- 幻灯片内容数据
    design_settings JSONB,
    animations JSONB,
    transitions JSONB,
    notes TEXT,
    thumbnail_url VARCHAR(500),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TYPE layout_type_enum AS ENUM (
    'title_slide', 'content_slide', 'two_column', 'image_slide',
    'chart_slide', 'table_slide', 'comparison', 'conclusion'
);

-- 唯一约束和索引
ALTER TABLE ppt_slides ADD CONSTRAINT unique_project_slide_number UNIQUE (project_id, slide_number);
CREATE INDEX idx_ppt_slides_project_id ON ppt_slides(project_id);
CREATE INDEX idx_ppt_slides_layout_type ON ppt_slides(layout_type);
```

### 2.4 论文助手系统表结构

#### 2.4.1 论文项目表 (paper_projects)
```sql
CREATE TABLE paper_projects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(1000) NOT NULL,
    abstract TEXT,
    subject_area VARCHAR(200),
    paper_type paper_type_enum NOT NULL,
    status paper_status_enum DEFAULT 'draft',
    outline JSONB,
    content JSONB,
    formatting_style VARCHAR(50) DEFAULT 'apa',
    target_length INTEGER, -- 目标字数
    current_length INTEGER DEFAULT 0,
    plagiarism_checked BOOLEAN DEFAULT FALSE,
    plagiarism_score DECIMAL(5,2),
    deadline DATE,
    tags TEXT[],
    is_collaborative BOOLEAN DEFAULT FALSE,
    collaborators UUID[],
    version INTEGER DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

CREATE TYPE paper_type_enum AS ENUM (
    'research_paper', 'thesis', 'dissertation', 'essay', 
    'report', 'review', 'case_study', 'proposal'
);

CREATE TYPE paper_status_enum AS ENUM (
    'draft', 'outline', 'writing', 'reviewing', 'completed', 'submitted'
);

-- 索引
CREATE INDEX idx_paper_projects_user_id ON paper_projects(user_id);
CREATE INDEX idx_paper_projects_status ON paper_projects(status);
CREATE INDEX idx_paper_projects_subject_area ON paper_projects(subject_area);
CREATE INDEX idx_paper_projects_deadline ON paper_projects(deadline);
```

#### 2.4.2 参考文献表 (references)
```sql
CREATE TABLE references (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    paper_project_id UUID REFERENCES paper_projects(id) ON DELETE CASCADE,
    title VARCHAR(1000) NOT NULL,
    authors TEXT[] NOT NULL,
    publication_year INTEGER,
    journal_name VARCHAR(500),
    volume VARCHAR(50),
    issue VARCHAR(50),
    pages VARCHAR(100),
    doi VARCHAR(200),
    url VARCHAR(1000),
    isbn VARCHAR(50),
    publisher VARCHAR(300),
    reference_type reference_type_enum NOT NULL,
    citation_style VARCHAR(50) DEFAULT 'apa',
    citation_text TEXT,
    abstract TEXT,
    keywords TEXT[],
    notes TEXT,
    is_verified BOOLEAN DEFAULT FALSE,
    added_method added_method_enum DEFAULT 'manual',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TYPE reference_type_enum AS ENUM (
    'journal_article', 'book', 'book_chapter', 'conference_paper',
    'thesis', 'website', 'report', 'patent', 'dataset'
);

CREATE TYPE added_method_enum AS ENUM ('manual', 'search', 'doi_lookup', 'import');

-- 索引
CREATE INDEX idx_references_paper_project_id ON references(paper_project_id);
CREATE INDEX idx_references_type ON references(reference_type);
CREATE INDEX idx_references_year ON references(publication_year);
CREATE INDEX idx_references_doi ON references(doi);
```

### 2.5 作业助手系统表结构

#### 2.5.1 作业项目表 (homework_projects)
```sql
CREATE TABLE homework_projects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(500) NOT NULL,
    subject_area subject_area_enum NOT NULL,
    difficulty_level difficulty_enum DEFAULT 'medium',
    problem_count INTEGER DEFAULT 0,
    status homework_status_enum DEFAULT 'in_progress',
    due_date TIMESTAMP WITH TIME ZONE,
    total_score DECIMAL(5,2),
    achieved_score DECIMAL(5,2),
    completion_percentage DECIMAL(5,2) DEFAULT 0,
    time_spent INTEGER DEFAULT 0, -- 分钟
    tags TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE
);

CREATE TYPE subject_area_enum AS ENUM (
    'mathematics', 'physics', 'chemistry', 'biology',
    'computer_science', 'engineering', 'statistics', 'economics'
);

CREATE TYPE difficulty_enum AS ENUM ('easy', 'medium', 'hard', 'expert');
CREATE TYPE homework_status_enum AS ENUM ('in_progress', 'completed', 'submitted', 'graded');

-- 索引
CREATE INDEX idx_homework_projects_user_id ON homework_projects(user_id);
CREATE INDEX idx_homework_projects_subject ON homework_projects(subject_area);
CREATE INDEX idx_homework_projects_status ON homework_projects(status);
CREATE INDEX idx_homework_projects_due_date ON homework_projects(due_date);
```

#### 2.5.2 问题表 (problems)
```sql
CREATE TABLE problems (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    homework_project_id UUID REFERENCES homework_projects(id) ON DELETE CASCADE,
    problem_number INTEGER NOT NULL,
    problem_text TEXT NOT NULL,
    problem_type problem_type_enum NOT NULL,
    subject_area subject_area_enum NOT NULL,
    difficulty_level difficulty_enum NOT NULL,
    original_file_url VARCHAR(1000),
    processed_images TEXT[],
    ocr_text TEXT,
    latex_representation TEXT,
    solution JSONB,
    explanation JSONB,
    user_answer JSONB,
    is_correct BOOLEAN,
    points_possible DECIMAL(5,2) DEFAULT 1,
    points_earned DECIMAL(5,2) DEFAULT 0,
    attempts_count INTEGER DEFAULT 0,
    max_attempts INTEGER DEFAULT 3,
    time_spent INTEGER DEFAULT 0, -- 秒
    hints_used TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    solved_at TIMESTAMP WITH TIME ZONE
);

CREATE TYPE problem_type_enum AS ENUM (
    'multiple_choice', 'true_false', 'short_answer', 'essay',
    'calculation', 'proof', 'coding', 'diagram'
);

-- 约束和索引
ALTER TABLE problems ADD CONSTRAINT unique_homework_problem_number 
    UNIQUE (homework_project_id, problem_number);
CREATE INDEX idx_problems_homework_id ON problems(homework_project_id);
CREATE INDEX idx_problems_type ON problems(problem_type);
CREATE INDEX idx_problems_subject ON problems(subject_area);
CREATE INDEX idx_problems_difficulty ON problems(difficulty_level);
```

### 2.6 AI痕迹消除系统表结构

#### 2.6.1 文本处理任务表 (text_processing_jobs)
```sql
CREATE TABLE text_processing_jobs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(500) NOT NULL,
    original_text TEXT NOT NULL,
    processed_text TEXT,
    processing_type processing_type_enum NOT NULL,
    status job_status_enum DEFAULT 'pending',
    ai_detection_score DECIMAL(5,2),
    optimization_level optimization_level_enum DEFAULT 'standard',
    processing_options JSONB,
    results JSONB,
    metrics JSONB,
    error_message TEXT,
    processing_time INTEGER, -- 毫秒
    model_used VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE
);

CREATE TYPE processing_type_enum AS ENUM (
    'ai_detection', 'text_optimization', 'style_transfer', 
    'humanization', 'plagiarism_check', 'combined'
);

CREATE TYPE job_status_enum AS ENUM ('pending', 'processing', 'completed', 'failed', 'cancelled');

CREATE TYPE optimization_level_enum AS ENUM ('light', 'standard', 'aggressive', 'custom');

-- 索引
CREATE INDEX idx_text_jobs_user_id ON text_processing_jobs(user_id);
CREATE INDEX idx_text_jobs_status ON text_processing_jobs(status);
CREATE INDEX idx_text_jobs_type ON text_processing_jobs(processing_type);
CREATE INDEX idx_text_jobs_created_at ON text_processing_jobs(created_at);
```

#### 2.6.2 AI检测结果表 (ai_detection_results)
```sql
CREATE TABLE ai_detection_results (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    text_processing_job_id UUID NOT NULL REFERENCES text_processing_jobs(id) ON DELETE CASCADE,
    overall_score DECIMAL(5,2) NOT NULL,
    confidence_level DECIMAL(5,2) NOT NULL,
    detected_patterns JSONB,
    sentence_scores JSONB,
    model_predictions JSONB,
    flagged_sections JSONB,
    risk_level risk_level_enum NOT NULL,
    recommendations TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TYPE risk_level_enum AS ENUM ('low', 'medium', 'high', 'very_high');

-- 索引
CREATE INDEX idx_ai_detection_job_id ON ai_detection_results(text_processing_job_id);
CREATE INDEX idx_ai_detection_score ON ai_detection_results(overall_score);
CREATE INDEX idx_ai_detection_risk ON ai_detection_results(risk_level);
```

### 2.7 系统管理表结构

#### 2.7.1 使用统计表 (usage_statistics)
```sql
CREATE TABLE usage_statistics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    feature_type feature_type_enum NOT NULL,
    action_type VARCHAR(100) NOT NULL,
    resource_id UUID,
    metadata JSONB,
    tokens_consumed INTEGER DEFAULT 0,
    cost DECIMAL(10,4) DEFAULT 0,
    processing_time INTEGER, -- 毫秒
    success BOOLEAN DEFAULT TRUE,
    error_code VARCHAR(50),
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TYPE feature_type_enum AS ENUM (
    'ai_chat', 'ppt_generation', 'paper_writing', 
    'homework_assistance', 'ai_trace_removal', 'file_processing'
);

-- 索引
CREATE INDEX idx_usage_stats_user_id ON usage_statistics(user_id);
CREATE INDEX idx_usage_stats_feature ON usage_statistics(feature_type);
CREATE INDEX idx_usage_stats_created_at ON usage_statistics(created_at);
CREATE INDEX idx_usage_stats_success ON usage_statistics(success);

-- 分区表（按月分区）
-- CREATE TABLE usage_statistics_y2025m01 PARTITION OF usage_statistics
-- FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');
```

#### 2.7.2 系统配置表 (system_configurations)
```sql
CREATE TABLE system_configurations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    config_key VARCHAR(200) UNIQUE NOT NULL,
    config_value JSONB NOT NULL,
    description TEXT,
    category VARCHAR(100),
    is_encrypted BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_by UUID REFERENCES users(id)
);

-- 索引
CREATE INDEX idx_system_config_key ON system_configurations(config_key);
CREATE INDEX idx_system_config_category ON system_configurations(category);
CREATE INDEX idx_system_config_active ON system_configurations(is_active);
```

## 3. 数据库性能优化策略

### 3.1 索引优化
```sql
-- 复合索引优化
CREATE INDEX idx_messages_conversation_created ON messages(conversation_id, created_at DESC);

-- 部分索引
CREATE INDEX idx_active_conversations ON conversations(user_id, updated_at) 
    WHERE status = 'active' AND deleted_at IS NULL;

-- 表达式索引
CREATE INDEX idx_users_email_lower ON users(LOWER(email));
```

### 3.2 Redis 缓存设计
```typescript
// 缓存键命名规范
interface CacheKeys {
  userSession: (userId: string) => `user:session:${userId}`;
  userProfile: (userId: string) => `user:profile:${userId}`;
  conversation: (conversationId: string) => `chat:conversation:${conversationId}`;
  aiResponse: (requestHash: string) => `ai:response:${requestHash}`;
}

// 缓存TTL配置
const CacheTTL = {
  shortTerm: 5 * 60,      // 5分钟
  mediumTerm: 60 * 60,    // 1小时
  longTerm: 24 * 60 * 60, // 24小时
  session: 7 * 24 * 60 * 60 // 7天
};
```

## 4. 数据安全和合规

### 4.1 数据加密
```sql
-- 敏感字段加密
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- 审计日志表
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    table_name VARCHAR(100) NOT NULL,
    operation VARCHAR(10) NOT NULL,
    row_id UUID NOT NULL,
    old_values JSONB,
    new_values JSONB,
    user_id UUID REFERENCES users(id),
    ip_address INET,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 4.2 备份策略
```bash
# 定期备份脚本
#!/bin/bash

# PostgreSQL备份
pg_dump -h localhost -U postgres -d college_ai > "backup_$(date +%Y%m%d_%H%M%S).sql"

# Redis备份
redis-cli --rdb redis_backup_$(date +%Y%m%d_%H%M%S).rdb
```

---

**总结：**
本数据库设计基于最佳实践，确保了数据的一致性、性能和安全性。后续将继续完善其他模块的表结构设计。 