# AI搜题助手 - 技术架构设计文档 v2.0

## 1. 架构概览

### 1.1 系统定位
AI搜题助手是一款专注于学生学习辅助的Web应用，采用现代化的前后端分离架构，以极简设计和高效搜题为核心特色。

### 1.2 架构原则
- **简约性**：去除冗余组件，专注核心搜题功能
- **高效性**：优化响应时间，提升用户体验
- **可扩展性**：模块化设计，支持功能扩展
- **稳定性**：确保服务高可用性

### 1.3 技术选型理念
- **现代化技术栈**：采用最新稳定版本的技术框架
- **轻量化部署**：减少依赖，简化部署流程
- **开发效率**：优选开发友好的技术工具

## 2. 整体架构

### 2.1 系统架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│                 │    │                 │    │                 │
│   前端应用      │    │   后端API      │    │   AI服务        │
│   React + TS    │◄──►│   Node.js       │◄──►│   GPT-4/Claude  │
│   TailwindCSS   │    │   Express       │    │   OCR API       │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
        │                       │                       │
        │                       │                       │
        ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│                 │    │                 │    │                 │
│   CDN分发       │    │   文件存储      │    │   监控日志      │
│   静态资源      │    │   图片处理      │    │   性能分析      │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2.2 核心模块
1. **前端展示层**：用户界面和交互逻辑
2. **API网关层**：请求路由和权限控制
3. **业务逻辑层**：搜题核心业务处理
4. **AI服务层**：图像识别和题目解析
5. **数据存储层**：搜题历史和用户数据

## 3. 前端架构设计

### 3.1 技术栈详情

#### 3.1.1 核心框架
- **React 18.3.1**：主框架，提供组件化开发
- **TypeScript 5.6.3**：类型安全，提升开发效率
- **Vite 5.4.19**：构建工具，热更新和快速编译

#### 3.1.2 UI框架
- **TailwindCSS 3.4.19**：原子化CSS，实现极简设计
- **Framer Motion 11.17.0**：动画库，提升交互体验
- **Lucide React 0.469.0**：图标库，现代简洁风格

#### 3.1.3 状态管理
- **Zustand 5.0.2**：轻量级状态管理，替代Redux
- **React Hot Toast 2.4.1**：消息提示组件

#### 3.1.4 工具库
- **React Dropzone 14.3.5**：文件拖拽上传
- **Axios 1.7.9**：HTTP请求库

### 3.2 目录结构设计

```
frontend/src/
├── components/          # 组件目录
│   ├── SearchPage.tsx  # 搜题主页组件
│   └── SearchResult.tsx # 搜题结果组件
├── services/            # 服务层
│   └── api.ts          # API请求服务
├── store/               # 状态管理
│   └── index.ts        # Zustand store
├── types/               # 类型定义
│   └── index.ts        # TypeScript类型
├── App.tsx             # 根组件
├── main.tsx            # 应用入口
└── index.css           # 全局样式
```

### 3.3 组件设计原则

#### 3.3.1 SearchPage组件
- **职责**：搜题主页展示和交互
- **功能**：三种搜题方式、学科导航、题目示例
- **设计**：极简布局，中心化排版

#### 3.3.2 SearchResult组件  
- **职责**：搜题结果展示
- **功能**：题目显示、答案解析、相关推荐
- **设计**：卡片式布局，结构化信息展示

### 3.4 状态管理设计

```typescript
interface AppState {
  // 用户状态
  user: User | null;
  
  // 搜题状态
  currentQuestion: SearchQuestion | null;
  searchResults: SearchResult[];
  searchHistory: SearchHistory[];
  
  // UI状态
  isLoading: boolean;
  error: string | null;
  
  // 操作方法
  searchByText: (query: string) => void;
  searchByImage: (file: File) => void;
  searchByScreenshot: () => void;
  clearError: () => void;
}
```

## 4. 后端架构设计

### 4.1 技术栈详情

#### 4.1.1 核心框架
- **Node.js v18+**：运行时环境
- **Express 4.21.2**：Web框架
- **TypeScript 5.4.5**：开发语言

#### 4.1.2 中间件
- **CORS 2.8.5**：跨域资源共享
- **Helmet 8.0.0**：安全防护中间件
- **Morgan 1.10.0**：HTTP请求日志
- **Multer 1.4.5-lts.1**：文件上传处理

#### 4.1.3 工具库
- **ts-node 10.9.2**：TypeScript运行环境
- **nodemon 3.1.10**：开发热重载

### 4.2 API设计规范

#### 4.2.1 RESTful API结构
```
GET    /health                    # 健康检查
POST   /api/v1/search/text       # 文字搜题
POST   /api/v1/search/image      # 图片搜题  
POST   /api/v1/search/screenshot # 截屏搜题
GET    /api/v1/search/history    # 搜题历史
GET    /api/v1/search/examples   # 题目示例
```

#### 4.2.2 响应格式标准
```typescript
interface ApiResponse<T> {
  success: boolean;
  message: string;
  data?: T;
  error?: {
    code: string;
    details: string;
  };
  timestamp: string;
}
```

### 4.3 业务逻辑模块

#### 4.3.1 搜题服务模块
```typescript
class SearchService {
  // 文字搜题
  async searchByText(query: string): Promise<SearchResult>
  
  // 图片搜题
  async searchByImage(imageFile: File): Promise<SearchResult>
  
  // 截屏搜题
  async searchByScreenshot(screenshotData: Buffer): Promise<SearchResult>
  
  // 获取相关题目
  async getRelatedQuestions(questionId: string): Promise<Question[]>
}
```

#### 4.3.2 AI服务集成
```typescript
class AIService {
  // OCR图像识别
  async recognizeText(image: Buffer): Promise<string>
  
  // 题目解析
  async analyzeQuestion(question: string): Promise<QuestionAnalysis>
  
  // 生成解答
  async generateSolution(question: string): Promise<Solution>
}
```

### 4.4 数据存储设计

#### 4.4.1 搜题历史存储
```typescript
interface SearchHistory {
  id: string;
  userId?: string;
  question: string;
  questionType: 'text' | 'image' | 'screenshot';
  subject: string;
  solution: Solution;
  timestamp: Date;
  confidence: number;
}
```

#### 4.4.2 题目示例存储
```typescript
interface QuestionExample {
  id: string;
  subject: string;
  difficulty: 'easy' | 'medium' | 'hard';
  question: string;
  tags: string[];
  isPopular: boolean;
  clickCount: number;
}
```

## 5. AI服务架构

### 5.1 AI能力集成

#### 5.1.1 图像识别服务
- **OCR供应商**：腾讯云OCR、百度OCR、阿里云OCR
- **识别能力**：手写体、印刷体、数学公式、几何图形
- **准确率要求**：>95%

#### 5.1.2 自然语言处理
- **模型选择**：GPT-4、Claude-3、ChatGLM等
- **处理能力**：题目理解、解题分析、答案生成
- **响应时间**：<5秒

### 5.2 AI服务设计模式

#### 5.2.1 多模型策略
```typescript
class AIModelManager {
  private models: AIModel[];
  
  // 选择最适合的模型
  selectBestModel(question: Question): AIModel
  
  // 模型回退机制
  fallbackToSecondary(primaryResult: any): Promise<any>
  
  // 结果质量评估
  evaluateResult(result: any): number
}
```

#### 5.2.2 缓存策略
- **Redis缓存**：常见题目结果缓存
- **内存缓存**：热点题目快速响应
- **CDN缓存**：静态资源分发

### 5.3 AI服务监控

#### 5.3.1 性能监控指标
- **响应时间**：平均响应时间、P95响应时间
- **准确率**：AI解答正确率统计
- **成功率**：API调用成功率
- **成本控制**：AI服务调用成本统计

## 6. 部署架构

### 6.1 本地开发环境

#### 6.1.1 开发工具配置
```json
// package.json 开发脚本
{
  "scripts": {
    "dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"",
    "dev:frontend": "cd frontend && npm run dev",
    "dev:backend": "cd backend && npm run dev"
  }
}
```

#### 6.1.2 环境变量配置
```env
# 前端环境变量
VITE_API_BASE_URL=http://localhost:3002
VITE_APP_NAME=AI搜题助手

# 后端环境变量  
PORT=3002
NODE_ENV=development
AI_SERVICE_API_KEY=your_api_key
```

### 6.2 生产环境部署

#### 6.2.1 Docker容器化
```dockerfile
# Frontend Dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
RUN npm run build
EXPOSE 3000

# Backend Dockerfile  
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
RUN npm run build
EXPOSE 3002
```

#### 6.2.2 Docker Compose编排
```yaml
version: '3.8'
services:
  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    depends_on:
      - backend
      
  backend:
    build: ./backend
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=production
```

### 6.3 云服务部署

#### 6.3.1 推荐云平台
- **前端部署**：Vercel、Netlify、阿里云OSS
- **后端部署**：阿里云ECS、腾讯云CVM、AWS EC2
- **CDN加速**：阿里云CDN、腾讯云CDN
- **监控告警**：阿里云云监控、腾讯云监控

## 7. 性能优化策略

### 7.1 前端性能优化

#### 7.1.1 代码层面优化
- **代码分割**：React.lazy实现路由级代码分割
- **组件优化**：React.memo避免不必要的重渲染
- **状态优化**：Zustand减少状态管理开销

#### 7.1.2 资源优化
- **图片优化**：WebP格式、压缩处理、懒加载
- **CSS优化**：TailwindCSS树摇、CSS压缩
- **JS优化**：Vite构建优化、gzip压缩

### 7.2 后端性能优化

#### 7.2.1 API优化
- **请求合并**：批量处理相关请求
- **响应压缩**：gzip压缩API响应
- **连接池**：数据库连接池优化

#### 7.2.2 缓存策略
- **Redis缓存**：热点数据缓存
- **本地缓存**：内存级别缓存
- **CDN缓存**：静态资源全球分发

### 7.3 AI服务优化

#### 7.3.1 模型优化
- **模型选择**：根据题目类型选择最适合的模型
- **提示优化**：优化Prompt提升解题准确率
- **结果缓存**：相同题目结果复用

## 8. 安全架构

### 8.1 数据安全

#### 8.1.1 传输安全
- **HTTPS**：全站HTTPS加密传输
- **API签名**：请求签名验证
- **跨域控制**：CORS严格配置

#### 8.1.2 存储安全
- **数据加密**：敏感数据加密存储
- **访问控制**：基于角色的权限控制
- **审计日志**：完整的操作日志记录

### 8.2 业务安全

#### 8.2.1 请求控制
- **频率限制**：API请求频率控制
- **参数验证**：严格的输入参数验证
- **文件安全**：上传文件类型和大小限制

#### 8.2.2 内容安全
- **内容过滤**：有害内容识别和过滤
- **版权保护**：避免版权侵权内容
- **学术诚信**：使用提示和规范指导

## 9. 监控与运维

### 9.1 应用监控

#### 9.1.1 性能监控
- **响应时间**：API响应时间监控
- **错误率**：错误请求比例统计
- **资源使用**：CPU、内存、磁盘使用率

#### 9.1.2 业务监控
- **搜题量**：日搜题次数统计
- **用户活跃**：DAU、MAU统计
- **功能使用**：各功能使用率分析

### 9.2 日志管理

#### 9.2.1 日志分类
- **访问日志**：HTTP请求访问记录
- **错误日志**：系统错误和异常记录
- **业务日志**：关键业务操作记录

### 9.3 告警机制

#### 9.3.1 告警规则
- **服务异常**：服务宕机、响应超时
- **性能异常**：响应时间过长、错误率过高
- **业务异常**：搜题量异常波动

## 10. 扩展性设计

### 10.1 水平扩展

#### 10.1.1 前端扩展
- **CDN分发**：多地域CDN节点部署
- **负载均衡**：多实例负载均衡

#### 10.1.2 后端扩展
- **微服务化**：按功能拆分微服务
- **容器编排**：Kubernetes集群管理
- **数据库分片**：按用户或地域分片

### 10.2 功能扩展

#### 10.2.1 新学科支持
- **插件化架构**：新学科解析插件
- **知识图谱**：学科知识关联扩展

#### 10.2.2 新功能模块
- **用户系统**：注册登录、个人中心
- **会员系统**：付费功能、权限管理
- **社区功能**：题目讨论、学习分享

---

*本文档基于AI搜题助手v2.0架构设计，将随系统演进持续更新* 