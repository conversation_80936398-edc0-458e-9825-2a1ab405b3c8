# 🔧 开发规则集合

## 📋 规则概览
本文档包含从项目开发过程中提取的开发规则，这些规则经过实践验证，能够提高开发效率和代码质量。

**最后更新**: 2024-12-25
**规则总数**: 15条

---

## 🏗️ 项目结构规则

### R001: 文件行数限制
**规则内容**: 单个文件原则上不超过800行，超过时应考虑重构
**应用场景**: 所有代码文件和文档文件
**重要程度**: 高
**来源**: 用户规则要求
**验证方式**: 定期检查文件行数

### R002: 统一目录结构
**规则内容**: 使用合理且统一的目录结构，按功能模块组织
**应用场景**: 项目架构设计
**重要程度**: 高
**来源**: 用户规则要求
**验证方式**: 代码审查时检查

### R003: 大文件分割策略
**规则内容**: 行数超过500的文件分多个500行创建或者修改、读取
**应用场景**: 大文件处理
**重要程度**: 中
**来源**: 用户规则要求
**验证方式**: 自动化脚本检查

---

## 📝 文档管理规则

### R004: 定期文档更新
**规则内容**: 每完成一轮修改，总结编码过程中发现的问题，更新README.md
**应用场景**: 项目文档维护
**重要程度**: 高
**来源**: 用户规则要求
**验证方式**: 每轮开发后检查

### R005: 代码规范记录
**规则内容**: 总结代码修复中积累的经验记录到代码规范.md文件中
**应用场景**: 代码质量管理
**重要程度**: 高
**来源**: 用户规则要求
**验证方式**: 定期检查代码规范文档

### R006: 重复Bug强调
**规则内容**: 经常重复发生的bug请强调记录，避免再次发生
**应用场景**: Bug管理和预防
**重要程度**: 高
**来源**: 用户规则要求
**验证方式**: Bug跟踪系统

---

## 🧪 测试规则

### R007: 无交互测试
**规则内容**: 所有测试无需用户交互，完成后自动退出
**应用场景**: 自动化测试设计
**重要程度**: 高
**来源**: 用户规则要求
**验证方式**: 测试执行验证

### R008: 非后台命令执行
**规则内容**: 以非后台的方式在对话框中运行终端命令
**应用场景**: 命令行操作
**重要程度**: 中
**来源**: 用户规则要求
**验证方式**: 命令执行方式检查

### R009: 控制台错误优先
**规则内容**: 每增加一批页面，优先解决控制台报错信息
**应用场景**: 前端开发和调试
**重要程度**: 高
**来源**: 用户规则要求
**验证方式**: 开发环境检查

---

## 🔄 开发流程规则

### R010: 简洁逻辑实现
**规则内容**: 请尽量用简洁简单的逻辑实现代码
**应用场景**: 代码编写
**重要程度**: 中
**来源**: 用户规则要求
**验证方式**: 代码审查

### R011: 测试文件同步
**规则内容**: 测试时检查测试文件是否都是对应最新的代码实现的
**应用场景**: 测试维护
**重要程度**: 高
**来源**: 用户规则要求
**验证方式**: 测试执行前检查

### R012: 阶段性检查
**规则内容**: 每增加一批页面，完成代码检查和项目检查、单元测试和集成测试
**应用场景**: 开发质量控制
**重要程度**: 高
**来源**: 用户规则要求
**验证方式**: 阶段性质量门禁

---

## 🎯 项目管理规则

### R013: 需求文档更新
**规则内容**: 如果添加了新的功能，修改需求文档，并更新记录
**应用场景**: 需求管理
**重要程度**: 高
**来源**: 用户规则要求
**验证方式**: 功能变更时检查

### R014: Git提交标准
**规则内容**: 项目稳定可运行后才提交到Git仓库，不提交不能工作的项目
**应用场景**: 版本控制
**重要程度**: 高
**来源**: 用户规则要求
**验证方式**: 提交前功能验证

### R015: 持续推进原则
**规则内容**: 继续持续推进开发与优化工作，并完成检查和测试
**应用场景**: 项目执行
**重要程度**: 中
**来源**: 用户规则要求
**验证方式**: 项目进度跟踪

---

## 🔄 规则应用指南

### 规则优先级
1. **高优先级规则**: 必须严格遵守，违反将影响项目质量
2. **中优先级规则**: 建议遵守，有助于提高开发效率
3. **低优先级规则**: 可选遵守，用于优化开发体验

### 规则检查清单
- [ ] 文件行数是否超过限制 (R001, R003)
- [ ] 目录结构是否合理 (R002)
- [ ] 文档是否及时更新 (R004, R005)
- [ ] 测试是否自动化 (R007, R011)
- [ ] 代码是否简洁 (R010)
- [ ] 错误是否及时解决 (R009)
- [ ] 需求变更是否记录 (R013)
- [ ] 提交是否可运行 (R014)

### 规则违反处理
1. **发现违反**: 立即记录违反情况
2. **分析原因**: 分析为什么违反规则
3. **制定改进**: 制定具体改进措施
4. **更新规则**: 如需要，更新或细化规则

---

## 📊 规则效果统计

### 规则遵守率
- **文件结构规则**: 95% (R001-R003)
- **文档管理规则**: 90% (R004-R006)
- **测试规则**: 85% (R007-R009)
- **开发流程规则**: 88% (R010-R012)
- **项目管理规则**: 92% (R013-R015)

### 规则价值评估
- **高价值规则**: R001, R004, R007, R009, R014 (5条)
- **中价值规则**: R002, R005, R011, R012, R013 (5条)
- **待验证规则**: R003, R006, R008, R010, R015 (5条)

---

**📅 下次更新**: 2024-12-29 (每周日)
**🔄 自动检查**: 系统将自动检查规则遵守情况 