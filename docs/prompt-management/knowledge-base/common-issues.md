# 🚨 常见问题解决手册

## 📋 文档概览
本文档整理项目开发过程中遇到的常见问题，提供快速解决方案和预防措施。

---

## 🛠️ 开发环境问题

### 问题1: npm命令无法找到
**问题现象**:
```
command not found: concurrently
command not found: vite
```

**问题原因**:
- 依赖包未安装或安装位置错误
- 全局安装与本地安装混淆
- package.json中scripts配置错误

**解决方案**:
```bash
# 1. 检查package.json中的依赖
npm list --depth=0

# 2. 重新安装依赖
npm install

# 3. 如果是开发依赖，使用--save-dev
npm install concurrently --save-dev

# 4. 清除缓存重新安装
npm cache clean --force
rm -rf node_modules package-lock.json
npm install
```

**预防措施**:
- 项目初始化时使用package-lock.json锁定版本
- 统一使用npm或yarn，不要混用
- 建立依赖检查清单

**相关工具**: npm, yarn, package.json
**频率**: 高
**影响程度**: 中

---

### 问题2: MSW Mock服务无法启动
**问题现象**:
```
Failed to register Service Worker
Mock API calls return 404 errors
Mock service not intercepting requests
```

**问题原因**:
- Service Worker注册路径错误
- MSW配置文件缺失或配置错误
- 浏览器Service Worker缓存问题

**解决方案**:
```javascript
// 1. 检查public目录下是否有mockServiceWorker.js
// 如果没有，运行：
npx msw init public/

// 2. 检查browser.ts配置
import { setupWorker } from 'msw/browser'
import { handlers } from './handlers'

export const worker = setupWorker(...handlers)

// 3. 检查main.tsx中的启动配置
if (import.meta.env.DEV) {
  import('./mocks/browser').then(({ worker }) => {
    worker.start({
      onUnhandledRequest: 'warn',
    })
  })
}

// 4. 清除浏览器Service Worker缓存
// 开发者工具 -> Application -> Service Workers -> Unregister
```

**预防措施**:
- 定期备份MSW配置文件
- 建立Mock服务健康检查脚本
- 文档化MSW配置步骤

**相关工具**: MSW, Service Worker, Vite
**频率**: 中
**影响程度**: 高

---

### 问题3: TypeScript类型错误
**问题现象**:
```
Property 'xxx' does not exist on type 'yyy'
Type 'unknown' is not assignable to type 'string'
Cannot find module 'xxx' or its corresponding type declarations
```

**问题原因**:
- 类型定义文件缺失
- 第三方库类型声明不完整
- tsconfig.json配置不当

**解决方案**:
```bash
# 1. 安装类型定义文件
npm install @types/node --save-dev
npm install @types/react --save-dev

# 2. 检查tsconfig.json配置
{
  "compilerOptions": {
    "strict": true,
    "moduleResolution": "node",
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true
  }
}

# 3. 创建类型声明文件
// types/index.ts
export interface ApiResponse<T> {
  data: T;
  message: string;
  success: boolean;
}

# 4. 使用类型断言（临时解决）
const data = response as ApiResponse<UserInfo>;
```

**预防措施**:
- 项目初始化时配置完整的TypeScript环境
- 建立项目专用的类型定义文件
- 使用严格模式，早期发现类型问题

**相关工具**: TypeScript, @types包
**频率**: 高
**影响程度**: 中

---

## 🎨 UI/组件问题

### 问题4: Ant Design样式不生效
**问题现象**:
```
Ant Design组件样式显示异常
自定义主题不生效
组件样式被全局样式覆盖
```

**问题原因**:
- CSS样式优先级问题
- ConfigProvider配置错误
- 样式文件导入顺序不当

**解决方案**:
```javascript
// 1. 检查ConfigProvider配置
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';

<ConfigProvider
  locale={zhCN}
  theme={{
    token: {
      colorPrimary: '#1890ff',
    },
  }}
>
  <App />
</ConfigProvider>

// 2. 调整CSS导入顺序
// main.tsx中的导入顺序
import 'antd/dist/reset.css';  // Ant Design样式
import './index.css';          // 全局样式
import './App.css';            // 组件样式

// 3. 使用CSS变量覆盖
:root {
  --ant-primary-color: #1890ff;
  --ant-border-radius-base: 6px;
}
```

**预防措施**:
- 建立统一的样式管理规范
- 使用CSS Modules或styled-components避免样式冲突
- 定期检查样式优先级和覆盖情况

**相关工具**: Ant Design, CSS, ConfigProvider
**频率**: 中
**影响程度**: 中

---

### 问题5: 响应式布局不正确
**问题现象**:
```
移动端布局错乱
组件在不同屏幕尺寸下显示异常
断点切换不平滑
```

**问题原因**:
- 断点配置不合理
- 缺少移动端适配
- 固定尺寸使用过多

**解决方案**:
```javascript
// 1. 使用Ant Design的Grid系统
import { Row, Col } from 'antd';

<Row gutter={[16, 16]}>
  <Col xs={24} sm={12} md={8} lg={6}>
    <Card>内容1</Card>
  </Col>
  <Col xs={24} sm={12} md={8} lg={6}>
    <Card>内容2</Card>
  </Col>
</Row>

// 2. 使用CSS媒体查询
@media (max-width: 768px) {
  .container {
    padding: 16px 8px;
  }
}

// 3. 使用相对单位
.card {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
}
```

**预防措施**:
- 建立标准的响应式设计规范
- 使用相对单位而非固定像素
- 定期在不同设备上测试

**相关工具**: Ant Design Grid, CSS媒体查询
**频率**: 中
**影响程度**: 中

---

## 🔧 功能开发问题

### 问题6: 状态管理数据不更新
**问题现象**:
```
组件状态更新但界面不刷新
Zustand store数据更新异常
状态持久化不工作
```

**问题原因**:
- 状态更新方式不正确
- 组件未正确订阅状态变化
- 持久化配置错误

**解决方案**:
```javascript
// 1. 正确的状态更新方式
const useStore = create((set, get) => ({
  count: 0,
  increment: () => set((state) => ({ count: state.count + 1 })),
  // 错误：直接修改状态
  // increment: () => { get().count++ }
}));

// 2. 正确的组件订阅
const MyComponent = () => {
  const { count, increment } = useStore();
  // 或选择性订阅
  const count = useStore((state) => state.count);
  
  return <div onClick={increment}>{count}</div>;
};

// 3. 检查持久化配置
const useStore = create(
  persist(
    (set) => ({
      user: null,
      setUser: (user) => set({ user }),
    }),
    {
      name: 'user-storage',
      storage: createJSONStorage(() => localStorage),
    }
  )
);
```

**预防措施**:
- 遵循不可变数据更新原则
- 使用TypeScript确保状态类型安全
- 建立状态管理最佳实践文档

**相关工具**: Zustand, React, TypeScript
**频率**: 中
**影响程度**: 中

---

### 问题7: API请求失败或数据异常
**问题现象**:
```
API请求返回404错误
数据格式与预期不符
请求超时或网络错误
```

**问题原因**:
- Mock API配置错误
- 请求URL或参数错误
- 网络配置问题

**解决方案**:
```javascript
// 1. 检查Mock API配置
// handlers.ts
import { http, HttpResponse } from 'msw';

export const handlers = [
  http.get('/api/users', () => {
    return HttpResponse.json({
      data: [{ id: 1, name: 'John' }],
      success: true
    });
  }),
];

// 2. 添加错误处理
const fetchUsers = async () => {
  try {
    const response = await fetch('/api/users');
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Failed to fetch users:', error);
    throw error;
  }
};

// 3. 使用axios配置拦截器
axios.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error);
    return Promise.reject(error);
  }
);
```

**预防措施**:
- 建立统一的API请求封装
- 添加完善的错误处理机制
- 定期验证Mock数据与实际API的一致性

**相关工具**: MSW, Axios, Fetch API
**频率**: 高
**影响程度**: 中

---

## ⚡ 性能问题

### 问题8: 应用加载速度慢
**问题现象**:
```
首屏加载时间过长
打包文件体积过大
页面渲染性能差
```

**问题原因**:
- 未进行代码分割
- 第三方库体积过大
- 图片资源未优化

**解决方案**:
```javascript
// 1. 实现路由级代码分割
import { lazy, Suspense } from 'react';

const Dashboard = lazy(() => import('./pages/Dashboard'));
const Profile = lazy(() => import('./pages/Profile'));

function App() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <Routes>
        <Route path="/dashboard" element={<Dashboard />} />
        <Route path="/profile" element={<Profile />} />
      </Routes>
    </Suspense>
  );
}

// 2. 按需导入第三方库
// 错误：全量导入
import * as antd from 'antd';

// 正确：按需导入
import { Button, Card } from 'antd';

// 3. 使用Vite的预构建优化
// vite.config.ts
export default defineConfig({
  optimizeDeps: {
    include: ['react', 'react-dom', 'antd']
  }
});
```

**预防措施**:
- 定期分析打包体积
- 使用工具监控性能指标
- 建立性能预算和检查机制

**相关工具**: Vite, Bundle Analyzer, Lighthouse
**频率**: 低
**影响程度**: 高

---

## 🐛 调试问题

### 问题9: 开发者工具不显示正确信息
**问题现象**:
```
React DevTools无法检测组件
Console日志信息不完整
Source Map映射错误
```

**问题原因**:
- 开发环境配置不当
- Source Map生成配置错误
- 浏览器插件冲突

**解决方案**:
```javascript
// 1. 检查Vite开发配置
// vite.config.ts
export default defineConfig({
  build: {
    sourcemap: true,
  },
  define: {
    __DEV__: JSON.stringify(true),
  }
});

// 2. 添加调试信息
console.log('Debug info:', {
  env: import.meta.env.MODE,
  timestamp: new Date().toISOString(),
  data: someData
});

// 3. 使用React StrictMode
function App() {
  return (
    <React.StrictMode>
      <Router>
        <Routes>
          {/* routes */}
        </Routes>
      </Router>
    </React.StrictMode>
  );
}
```

**预防措施**:
- 配置完整的开发环境
- 使用标准的调试工具和方法
- 定期清理浏览器缓存和插件

**相关工具**: React DevTools, Browser DevTools, Vite
**频率**: 低
**影响程度**: 低

---

## 📋 快速诊断清单

### 环境问题诊断
- [ ] 检查Node.js版本 (`node --version`)
- [ ] 检查npm/yarn版本 (`npm --version`)
- [ ] 检查package.json依赖
- [ ] 清除node_modules重新安装
- [ ] 检查环境变量配置

### Mock服务诊断
- [ ] 确认mockServiceWorker.js存在
- [ ] 检查handlers.ts配置
- [ ] 检查browser.ts初始化
- [ ] 清除Service Worker缓存
- [ ] 检查网络请求面板

### 样式问题诊断
- [ ] 检查CSS导入顺序
- [ ] 确认ConfigProvider配置
- [ ] 检查样式优先级
- [ ] 验证响应式断点
- [ ] 清除浏览器缓存

### 状态管理诊断
- [ ] 检查状态更新方式
- [ ] 确认组件正确订阅
- [ ] 验证持久化配置
- [ ] 检查类型定义
- [ ] 使用React DevTools检查

---

## 🔧 应急解决方案

### 快速修复命令
```bash
# 重置开发环境
rm -rf node_modules package-lock.json
npm cache clean --force
npm install

# 重置Git状态
git stash
git reset --hard HEAD
git clean -fd

# 重启开发服务器
npm run dev --force

# 清除浏览器缓存
# Ctrl+Shift+R (硬刷新)
# 开发者工具 -> Network -> Disable cache
```

### 回退策略
1. **代码回退**: 使用Git回退到最后一个可工作的提交
2. **依赖回退**: 回退到之前工作的package-lock.json版本
3. **配置回退**: 恢复之前的配置文件版本
4. **缓存清除**: 清除所有本地缓存和临时文件

---

**📝 更新记录**:
- 2024-12-27: 初版建立，包含9个常见问题分类
- 下次更新: 2024-12-30 (计划添加部署和测试相关问题)

**🎯 使用建议**: 遇到问题时，先查看对应分类的解决方案，如果问题未覆盖，请按照诊断清单逐步排查 