# 代码贡献规范 (Contributing Guidelines)

## 文档信息
- **文档版本**: v1.0
- **创建日期**: 2025-01-15
- **更新日期**: 2025-01-15
- **维护人**: 项目组
- **审核人**: 技术负责人

## 概述

感谢您对高校AI产品项目的贡献兴趣！本文档基于业界最佳实践，详细说明了如何参与项目开发、提交代码和进行代码审查的规范流程。

## 行为准则 (Code of Conduct)

### 我们的承诺

为了营造一个开放和友好的环境，我们作为贡献者和维护者承诺：
- 尊重不同观点和经验
- 接受建设性的反馈
- 专注于对社区最有利的事情
- 对其他社区成员表示同理心

### 不可接受的行为

- 使用性化的语言或图像
- 人身攻击或政治攻击
- 公开或私下骚扰
- 发布他人的私人信息
- 其他在专业环境中不当的行为

## 开始贡献

### 环境准备

1. **开发环境要求**
   ```bash
   # Node.js版本要求
   node >= 18.0.0
   npm >= 9.0.0
   
   # 必需的全局工具
   npm install -g @commitlint/cli husky lint-staged
   ```

2. **项目设置**
   ```bash
   # 1. Fork 项目到你的 GitHub 账户
   # 2. 克隆你的 fork
   git clone https://github.com/YOUR-USERNAME/college.git
   cd college
   
   # 3. 添加上游仓库
   git remote add upstream https://github.com/ORIGINAL-OWNER/college.git
   
   # 4. 安装依赖
   npm install
   
   # 5. 安装 Git hooks
   npm run prepare
   ```

3. **验证环境**
   ```bash
   # 运行测试确保环境正常
   npm test
   npm run lint
   npm run type-check
   ```

## 工作流程 (Workflow)

### 1. 创建功能分支

```bash
# 更新主分支
git checkout main
git pull upstream main

# 创建新的功能分支
git checkout -b feature/your-feature-name

# 分支命名规范:
# feature/功能名称  - 新功能开发
# bugfix/问题描述  - Bug修复
# hotfix/紧急修复  - 紧急修复
# docs/文档类型   - 文档更新
# refactor/重构内容 - 代码重构
# test/测试内容   - 测试相关
```

### 2. 开发阶段

#### 2.1 编码规范

**TypeScript/JavaScript规范**
```typescript
// ✅ 推荐的代码风格
interface UserProfile {
  id: string;
  username: string;
  email: string;
  createdAt: Date;
}

class UserService {
  async getUserById(id: string): Promise<UserProfile | null> {
    try {
      const user = await this.userRepository.findById(id);
      return user || null;
    } catch (error) {
      this.logger.error('Failed to get user', { id, error });
      throw error;
    }
  }
}

// ❌ 避免的代码风格
var userData; // 避免使用 var
function getUser(id) { // 应使用 TypeScript 类型
  return db.query('SELECT * FROM users WHERE id = ' + id); // SQL注入风险
}
```

**React组件规范**
```tsx
// ✅ 推荐的组件写法
interface UserCardProps {
  user: UserProfile;
  onEdit?: (user: UserProfile) => void;
  className?: string;
}

export const UserCard: React.FC<UserCardProps> = ({ 
  user, 
  onEdit, 
  className = '' 
}) => {
  const handleEditClick = useCallback(() => {
    onEdit?.(user);
  }, [user, onEdit]);

  return (
    <div className={`user-card ${className}`} data-testid="user-card">
      <h3>{user.username}</h3>
      <p>{user.email}</p>
      {onEdit && (
        <button onClick={handleEditClick} type="button">
          编辑
        </button>
      )}
    </div>
  );
};
```

#### 2.2 提交规范

我们使用 [Conventional Commits](https://www.conventionalcommits.org/zh-hans/) 规范：

```bash
# 提交消息格式
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

**提交类型 (type)**
- `feat`: 新功能
- `fix`: Bug修复
- `docs`: 文档更新
- `style`: 代码格式调整（不影响功能）
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具变动
- `perf`: 性能优化
- `ci`: CI配置文件和脚本变动

**示例提交消息**
```bash
# 新功能
git commit -m "feat(auth): add JWT token refresh mechanism"

# Bug修复
git commit -m "fix(api): resolve user profile update validation error"

# 文档更新
git commit -m "docs(readme): update installation instructions"

# 重构
git commit -m "refactor(database): extract query builder to separate module"

# 破坏性变更
git commit -m "feat(api)!: change user authentication endpoint

BREAKING CHANGE: The /auth/login endpoint now requires email instead of username"
```

#### 2.3 代码质量检查

在每次提交前，系统会自动运行：

```bash
# Husky pre-commit hook 会执行：
npm run lint        # ESLint代码检查
npm run type-check  # TypeScript类型检查
npm run test:unit   # 单元测试
npm run format      # Prettier代码格式化
```

### 3. 测试要求

#### 3.1 测试覆盖率要求

- **单元测试覆盖率**: ≥ 80%
- **集成测试覆盖率**: ≥ 60%
- **E2E测试覆盖率**: 核心用户流程100%

#### 3.2 测试命名规范

```typescript
// ✅ 推荐的测试结构
describe('UserService', () => {
  describe('getUserById', () => {
    it('should return user when valid ID is provided', async () => {
      // Arrange
      const userId = 'user123';
      const expectedUser = { id: userId, username: 'testuser' };
      
      // Act
      const result = await userService.getUserById(userId);
      
      // Assert
      expect(result).toEqual(expectedUser);
    });

    it('should return null when user does not exist', async () => {
      // Arrange
      const nonExistentId = 'nonexistent';
      
      // Act
      const result = await userService.getUserById(nonExistentId);
      
      // Assert
      expect(result).toBeNull();
    });

    it('should throw error when database connection fails', async () => {
      // Arrange
      jest.spyOn(userRepository, 'findById')
        .mockRejectedValue(new Error('Database error'));
      
      // Act & Assert
      await expect(userService.getUserById('user123'))
        .rejects.toThrow('Database error');
    });
  });
});
```

#### 3.3 E2E测试示例

```typescript
// tests/e2e/user-authentication.spec.ts
import { test, expect } from '@playwright/test';

test.describe('用户认证流程', () => {
  test('用户可以成功登录和退出', async ({ page }) => {
    // 访问登录页面
    await page.goto('/login');
    
    // 填写登录表单
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'password123');
    
    // 点击登录按钮
    await page.click('[data-testid="login-button"]');
    
    // 验证登录成功
    await expect(page).toHaveURL('/dashboard');
    await expect(page.locator('[data-testid="user-menu"]')).toBeVisible();
    
    // 退出登录
    await page.click('[data-testid="user-menu"]');
    await page.click('[data-testid="logout-button"]');
    
    // 验证退出成功
    await expect(page).toHaveURL('/login');
  });
});
```

### 4. Pull Request 规范

#### 4.1 创建 Pull Request

```bash
# 推送功能分支
git push origin feature/your-feature-name

# 在 GitHub 上创建 Pull Request
```

#### 4.2 PR 标题和描述

**标题格式**
```
[类型] 简洁的功能描述
```

**描述模板**
```markdown
## 📝 变更描述
简要描述本次变更的内容和目的

## 🎯 变更类型
- [ ] 新功能 (feature)
- [ ] Bug修复 (bugfix)
- [ ] 文档更新 (docs)
- [ ] 代码重构 (refactor)
- [ ] 性能优化 (perf)
- [ ] 测试相关 (test)

## 🔍 变更清单
- [ ] 添加了用户认证功能
- [ ] 实现了JWT token管理
- [ ] 更新了相关文档
- [ ] 添加了单元测试

## 🧪 测试情况
- [ ] 单元测试通过
- [ ] 集成测试通过
- [ ] E2E测试通过
- [ ] 手动测试完成

## 📷 截图/演示
（如果是UI变更，请提供截图或GIF演示）

## 🔗 相关Issue
关闭 #123

## 📋 检查清单
- [ ] 代码符合项目规范
- [ ] 添加了适当的测试
- [ ] 更新了相关文档
- [ ] 通过了所有CI检查
- [ ] 没有破坏性变更（或已在描述中说明）

## 🤔 备注
其他需要说明的内容
```

#### 4.3 PR 审查标准

**自动检查**
- ✅ 所有CI/CD检查通过
- ✅ 代码覆盖率满足要求
- ✅ 没有安全漏洞
- ✅ 性能测试通过

**人工审查**
- ✅ 代码逻辑正确
- ✅ 符合架构设计
- ✅ 代码可读性良好
- ✅ 错误处理完善
- ✅ 文档完整

## 代码审查 (Code Review)

### 审查者指南

#### 1. 审查重点

**功能性审查**
```typescript
// ✅ 检查业务逻辑正确性
if (user.role === 'admin' && user.permissions.includes('user_management')) {
  // 正确的权限检查
}

// ❌ 避免的逻辑错误
if (user.role = 'admin') { // 赋值而非比较
  // 错误的权限检查
}
```

**安全性审查**
```typescript
// ✅ 安全的数据处理
const sanitizedInput = DOMPurify.sanitize(userInput);
const hashedPassword = await bcrypt.hash(password, 10);

// ❌ 不安全的做法
const query = `SELECT * FROM users WHERE id = ${userId}`; // SQL注入风险
localStorage.setItem('password', password); // 明文存储密码
```

**性能审查**
```typescript
// ✅ 性能优化的代码
const users = useMemo(() => 
  filteredUsers.slice(page * pageSize, (page + 1) * pageSize), 
  [filteredUsers, page, pageSize]
);

// ❌ 性能问题
users.forEach(user => {
  // 在渲染循环中进行昂贵操作
  const processedData = expensiveOperation(user);
});
```

#### 2. 审查反馈规范

**建设性反馈示例**
```markdown
## 💡 建议改进
这里的错误处理可以更具体一些：

```diff
- } catch (error) {
-   throw error;
- }
+ } catch (error) {
+   this.logger.error('Failed to create user', { error, userData });
+   throw new Error('用户创建失败，请稍后重试');
+ }
```

## 🎯 优化建议
考虑使用 React.memo 来优化这个组件的渲染性能

## ❓ 疑问
这个API调用是否需要添加超时处理？
```

### 被审查者指南

#### 1. 响应反馈

```markdown
## 👍 已修复
感谢指出错误处理的问题，已按建议修改

## 💭 解释说明
这里没有使用React.memo是因为props变化频繁，memo优化效果有限

## 🤝 讨论
关于超时处理，我觉得可以在更上层统一处理，你觉得呢？
```

#### 2. 持续改进

- 认真对待每个反馈
- 学习审查者的建议
- 主动解释设计决策
- 感谢审查者的时间

## 发布流程

### 版本管理

我们使用 [Semantic Versioning](https://semver.org/) 进行版本管理：

```
MAJOR.MINOR.PATCH

1.0.0 → 1.0.1  (patch: bug fixes)
1.0.1 → 1.1.0  (minor: new features)
1.1.0 → 2.0.0  (major: breaking changes)
```

### 发布检查清单

- [ ] 所有测试通过
- [ ] 文档已更新
- [ ] CHANGELOG.md已更新
- [ ] 版本号已更新
- [ ] 安全扫描通过
- [ ] 性能测试通过

## 社区参与

### 讨论渠道

- **GitHub Issues**: 报告Bug、功能请求
- **GitHub Discussions**: 技术讨论、问答
- **Slack频道**: 日常沟通（内部团队）

### 贡献认可

我们会在以下地方认可贡献者：
- README.md中的贡献者列表
- 发布说明中的感谢名单
- 年度贡献者奖励

## 常见问题 (FAQ)

### Q: 如何选择合适的Issue开始贡献？

A: 
1. 查找标有 `good first issue` 的Issue
2. 选择你感兴趣的功能领域
3. 确认Issue还没有被认领
4. 在Issue中评论表明你想要处理

### Q: 我的PR多久会被审查？

A: 
- 小的修复: 1-2个工作日
- 新功能: 3-5个工作日
- 大的重构: 1-2周

### Q: 如何处理合并冲突？

A:
```bash
# 获取最新的upstream变更
git fetch upstream
git checkout main
git merge upstream/main

# 切换到你的分支并rebase
git checkout your-feature-branch
git rebase main

# 解决冲突后
git add .
git rebase --continue
git push --force-with-lease origin your-feature-branch
```

### Q: 我应该创建一个大的PR还是多个小的PR？

A: 倾向于创建多个小的、专注的PR：
- 更容易审查
- 更快获得反馈
- 降低合并风险
- 更容易回滚

## 联系我们

如果您有任何问题或建议，请通过以下方式联系：

- **项目维护者**: @project-maintainers
- **技术负责人**: @tech-lead
- **邮件**: <EMAIL>
- **GitHub**: [创建Issue](https://github.com/project/college/issues/new)

---

**感谢您的贡献！** 🙏

每一个贡献，无论大小，都让这个项目变得更好。我们期待与您一起构建优秀的高校AI产品！ 