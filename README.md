# 🎓 高校AI助手 - College AI Assistant

一个专为高校师生设计的全能AI学习平台，集成对话问答、PPT生成、论文写作、作业辅导和AI痕迹优化等功能。

## ✨ 核心功能

### 🤖 AI对话助手
- **通用知识问答**：支持多学科领域，专门优化高数、物理等理科科目
- **快速操作按钮**：数学解题、生成图表、饼图分析、函数图像等一键操作
- **多模态交互**：文字、图片、语音输入，智能识别数学公式、几何图、电路图、化学结构式
- **实时图表生成**：支持柱状图、折线图、饼图、散点图等可视化图表
- **对话历史**：保存对话记录，支持搜索和导出

### 📝 AI论文助手 ⭐ **全新功能**
#### 🎯 学术论文生成
- **AI智能生成**：基于用户输入的标题、主题和关键词，自动生成符合学术标准的完整论文
- **学术格式规范**：严格按照学术论文格式要求，包含摘要、引言、文献综述、研究方法、结果分析、讨论、结论等完整结构
- **多类型模板**：支持学术研究论文、学位论文（本科/研究生）、文献综述、实验报告等多种类型

#### 🔍 真实文献搜索
- **联网文献检索**：集成CrossRef、Google Scholar等学术数据库API
- **真实文献验证**：确保所有参考文献都是真实存在的学术资源，避免虚构引用
- **自动格式化**：支持APA、MLA、Chicago、GB/T等多种引用格式
- **DOI链接**：自动获取DOI链接，便于读者查阅原文

#### 📊 数据可视化
- **智能图表生成**：自动生成柱状图、折线图、饼图、散点图等学术图表
- **数据表格**：创建标准的学术数据表格，支持自定义标题和注释
- **图表编辑**：在线编辑图表样式、数据和标题
- **导入导出**：支持图表数据的导入和导出

#### 💾 多格式导出
- **Word格式**：导出为标准的.docx文件，兼容Microsoft Word
- **LaTeX格式**：导出为.tex文件，支持专业学术排版
- **在线LaTeX编辑**：集成LaTeX编辑器，支持实时预览和代码修改
- **格式切换**：在Markdown和LaTeX编辑模式之间自由切换

#### 🛠️ 智能写作工具
- **AI写作助手**：语言表达优化、学术规范检查、大纲生成等智能功能
- **实时字数统计**：跟踪写作进度，显示字数和完成度
- **自动保存**：实时保存编辑内容，防止数据丢失
- **版本管理**：支持多版本论文管理和比较

### 📊 AI PPT生成器
- **学术模板库**：20+预设学术PPT模板（理工科、文科、商科）
- **智能生成**：基于文档内容自动生成PPT结构和内容
- **VBA代码支持**：提供PowerPoint自动化脚本和动画效果
- **单页编辑**：支持对生成PPT进行单页修改和重新生成
- **多格式导出**：支持PPTX、PDF格式

### 🔬 AI理科作业助手
- **多格式识别**：支持图片(JPG/PNG)、Word、PDF等格式文件上传
- **专业优化**：针对高等数学、物理、化学等理科科目深度优化
- **解题详解**：提供完整解题思路和分步骤详细过程
- **智能识别**：准确识别手写、印刷体数学公式和几何图形
- **作业生成**：支持Word、PDF格式的作业文档输出

### 🎭 AI痕迹优化
- **智能检测**：识别AI生成文本的特征模式和痕迹
- **内容优化**：保持原意的前提下进行自然化改写
- **多格式支持**：支持PDF、DOC/DOCX等多种文档格式
- **批量处理**：支持大量文档的批量优化处理

## 🛠️ 技术架构

### 前端技术栈
- **React 18** + **TypeScript** - 现代化前端框架
- **Vite** - 快速构建工具
- **TailwindCSS** - 极简设计系统
- **Framer Motion** - 流畅动画效果
- **Zustand** - 轻量化状态管理
- **React Router** - 单页应用路由
- **ECharts** - 专业图表库

### 后端技术栈
- **Node.js** + **Express** - 服务端框架
- **TypeScript** - 类型安全开发
- **RESTful API** - 标准化接口设计
- **Multer** - 文件上传处理
- **CORS** - 跨域资源共享

### AI服务集成
- **多模型支持**：集成GPT-4、Claude等主流AI模型
- **OCR服务**：图像文字识别和公式解析
- **文献数据库**：CrossRef、Google Scholar、百度学术等学术资源
- **图表生成**：ECharts、D3.js等可视化库

## 🚀 快速开始

### 环境要求
- Node.js 18+
- npm 或 yarn
- 现代浏览器（Chrome、Firefox、Safari、Edge）

### 安装依赖
```bash
# 克隆项目
git clone [repository-url]
cd college

# 安装依赖
npm install

# 启动开发服务
npm run dev
```

### 访问地址
- 前端应用：http://localhost:3001
- 后端API：http://localhost:3002
- API文档：http://localhost:3002/api/docs

## 📁 项目结构

```
college/
├── frontend/           # React前端应用
│   ├── src/
│   │   ├── components/     # 可复用组件
│   │   │   ├── ui/         # UI组件库
│   │   │   │   ├── Chart.tsx       # 图表组件
│   │   │   │   ├── Button.tsx      # 按钮组件
│   │   │   │   └── Card.tsx        # 卡片组件
│   │   │   └── layout/     # 布局组件
│   │   ├── pages/          # 页面组件
│   │   │   ├── chat/       # AI对话模块
│   │   │   ├── paper/      # 论文写作模块 ⭐
│   │   │   ├── ppt/        # PPT生成模块
│   │   │   ├── homework/   # 作业辅导模块
│   │   │   └── trace/      # 痕迹优化模块
│   │   ├── store/          # 状态管理
│   │   ├── services/       # API服务
│   │   └── types/          # TypeScript类型
├── backend/            # Node.js后端服务
│   ├── src/
│   │   ├── controllers/    # 控制器
│   │   ├── services/       # 业务逻辑
│   │   ├── models/         # 数据模型
│   │   └── routes/         # 路由定义
├── docs/               # 项目文档
└── README.md          # 项目说明
```

## 🎯 开发进度

### 已完成功能 ✅
- [x] 项目基础架构搭建
- [x] 用户认证系统
- [x] 主布局和导航
- [x] **AI对话模块** - 完整实现包括快速操作按钮和图表生成
- [x] **论文写作模块** - ⭐ 完整实现所有功能
  - [x] 学术论文自动生成
  - [x] 真实文献搜索和管理
  - [x] 数据图表和表格生成
  - [x] Word和LaTeX格式导出
  - [x] 在线LaTeX编辑器
  - [x] 智能写作工具
- [x] 作业辅导页面框架
- [x] PPT生成页面框架
- [x] 痕迹优化页面框架

### 开发中功能 🔄
- [ ] PPT模板系统和VBA支持
- [ ] 理科作业解题优化
- [ ] AI痕迹检测和优化算法

### 计划功能 📋
- [ ] 多语言支持
- [ ] 移动端适配
- [ ] 用户数据分析
- [ ] 性能监控
- [ ] 部署上线

## 🎉 最新更新

### v2.1.0 - 论文写作模块完整实现 (2024-12-27)

#### ✨ 新功能
- **🎓 完整论文写作系统**：从创建到导出的全链路学术写作支持
- **🔍 真实文献搜索**：集成学术数据库，搜索真实存在的参考文献
- **📊 学术图表生成**：支持柱状图、折线图、饼图、数据表格等可视化元素
- **💾 多格式导出**：Word和LaTeX格式导出，支持在线LaTeX编辑
- **🛠️ 智能写作工具**：AI写作助手、语言优化、学术规范检查

#### 🚀 性能优化
- 优化了图表组件的渲染性能
- 改进了用户界面的交互体验
- 修复了AI对话模块的快速操作按钮问题

#### 🧪 测试覆盖
- 完整的Playwright自动化测试
- 验证了所有核心功能的正常运行
- 确保了界面交互的流畅性

## 🤝 贡献指南

我们欢迎任何形式的贡献！

1. Fork 本项目
2. 创建特性分支：`git checkout -b feature/amazing-feature`
3. 提交改动：`git commit -m 'Add amazing feature'`
4. 推送分支：`git push origin feature/amazing-feature`
5. 提交Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系我们

- 项目地址：[GitHub Repository]
- 问题反馈：[Issues]
- 邮箱：[<EMAIL>]

---

⭐ 如果这个项目对你有帮助，请给我们一个星标！ 
### 前端技术栈
```json
{
  "框架": "React 18.3.1 + TypeScript 5.6.3",
  "构建工具": "Vite 5.4.19",
  "样式方案": "TailwindCSS 3.4.19",
  "动画库": "Framer Motion 11.17.0",
  "状态管理": "Zustand 5.0.2",
  "图标库": "Lucide React 0.469.0",
  "HTTP客户端": "Axios 1.7.9"
}
```

### 后端技术栈
```json
{
  "运行环境": "Node.js + Express 4.21.2",
  "开发语言": "TypeScript 5.4.5",
  "中间件": "CORS, Helmet, Morgan, Multer",
  "文件处理": "Multer 1.4.5-lts.1",
  "开发工具": "ts-node, nodemon"
}
```

### AI服务集成
- **图像识别**：OCR API（腾讯云、百度云等）
- **题目解析**：GPT-4、Claude-3等大语言模型
- **知识图谱**：学科知识关联系统

## 📁 项目结构

```
college/
├── frontend/                   # 前端应用
│   ├── src/
│   │   ├── components/        # React组件
│   │   │   ├── SearchPage.tsx # 搜题主页
│   │   │   └── SearchResult.tsx # 搜题结果
│   │   ├── services/          # API服务
│   │   ├── store/             # 状态管理
│   │   ├── types/             # 类型定义
│   │   └── App.tsx            # 根组件
│   ├── package.json           # 前端依赖
│   └── tailwind.config.js     # TailwindCSS配置
├── backend/                    # 后端API
│   ├── src/
│   │   └── index.ts           # 服务入口
│   ├── package.json           # 后端依赖
│   └── tsconfig.json          # TypeScript配置
├── docs/                       # 项目文档
│   ├── 需求文档.md            # 产品需求
│   ├── 技术架构设计.md        # 技术架构
│   └── 代码规范.md            # 开发规范
├── package.json               # 项目根配置
└── README.md                  # 项目说明
```

## 🔌 API接口

### 搜题相关接口
```
POST /api/v1/search/text       # 文字搜题
POST /api/v1/search/image      # 图片搜题
POST /api/v1/search/screenshot # 截屏搜题
GET  /api/v1/search/history    # 搜题历史
GET  /api/v1/search/examples   # 题目示例
```

### 系统接口
```
GET  /health                   # 健康检查
```

### 响应格式
```typescript
interface ApiResponse<T> {
  success: boolean;
  message: string;
  data?: T;
  error?: {
    code: string;
    details: string;
  };
  timestamp: string;
}
```

## 🎨 界面设计

### 主页设计理念
- **极简至上**：去除冗余元素，专注搜题核心功能
- **中心布局**：主要功能居中展示，符合用户注意力习惯
- **现代美感**：圆角卡片、柔和阴影、渐变色彩
- **直观交互**：明确的操作指引，流畅的动画效果

### 界面预览
```
┌─────────────────────────────────────┐
│              AI搜题                 │
│     一键截屏搜题｜AI精准解析｜分步讲解   │
│                                     │
│    [截屏搜题]      [图片搜题]        │
│                                     │
│      [输入题目进行AI搜题]            │
│                                     │
│              热门学科                │
│   [数学] [物理] [化学] [编程] [英语]  │
│                                     │
│              AI题目示例              │
│        [示例1] [示例2] [示例3]       │
│                                     │
│  累计解答：1,000,000+ ｜ 准确率：98%  │
└─────────────────────────────────────┘
```

## 🛠️ 开发指南

### 开发环境设置
```bash
# 1. 安装依赖
npm install

# 2. 环境变量配置
cp env.example .env

# 3. 启动开发服务
npm run dev

# 4. 代码格式化
npm run format

# 5. 类型检查
npm run type-check
```

### 代码规范
- **TypeScript**：严格类型检查，避免any类型
- **组件设计**：单一职责，props类型定义完整
- **命名规范**：文件名PascalCase，变量名camelCase
- **代码组织**：按功能模块划分，保持目录结构清晰

### Git工作流
```bash
# 1. 功能开发
git checkout -b feature/search-optimization

# 2. 提交代码
git add .
git commit -m "feat: 优化搜题识别准确率"

# 3. 推送分支
git push origin feature/search-optimization

# 4. 创建Pull Request
# 在GitHub/GitLab中创建PR，等待代码审查
```

## 📊 性能指标

### 技术指标
- **搜题准确率**：>95%
- **响应时间**：文字搜题<2s，图片搜题<5s，截屏搜题<3s
- **并发支持**：1000+用户同时在线
- **服务可用性**：99.5%+

### 用户体验指标
- **界面加载**：首页加载<1s
- **操作流畅度**：60fps动画帧率
- **移动端适配**：完美支持iOS和Android
- **浏览器兼容**：支持Chrome、Firefox、Safari、Edge最新版本

## 🚀 部署指南

### Docker部署
```bash
# 1. 构建镜像
docker-compose build

# 2. 启动服务
docker-compose up -d

# 3. 查看状态
docker-compose ps

# 4. 查看日志
docker-compose logs -f
```

### 生产环境部署
```bash
# 1. 构建前端
cd frontend && npm run build

# 2. 构建后端
cd backend && npm run build

# 3. 启动服务
npm run start:prod
```

### 环境变量配置
```env
# 前端环境变量
VITE_API_BASE_URL=https://api.yourdomain.com
VITE_APP_NAME=AI搜题助手

# 后端环境变量
PORT=3002
NODE_ENV=production
AI_SERVICE_API_KEY=your_api_key
OCR_SERVICE_URL=https://ocr.api.com
```

## 🤝 贡献指南

### 参与贡献
我们欢迎各种形式的贡献：

1. **🐛 Bug反馈**：在Issues中报告问题
2. **💡 功能建议**：提出新功能想法
3. **📝 文档改进**：完善项目文档
4. **🔧 代码贡献**：提交Pull Request

### 开发流程
1. Fork项目到个人账户
2. 创建功能分支：`git checkout -b feature/amazing-feature`
3. 提交更改：`git commit -m 'Add some amazing feature'`
4. 推送分支：`git push origin feature/amazing-feature`
5. 创建Pull Request

### 代码审查
- 确保代码通过所有测试
- 遵循项目代码规范
- 添加必要的文档说明
- 保持提交历史清晰

## 📄 许可证

本项目采用 [MIT License](LICENSE) 开源协议。

## 🔗 相关链接

- **产品需求文档**：[docs/需求文档.md](docs/需求文档.md)
- **技术架构设计**：[docs/技术架构设计.md](docs/技术架构设计.md)
- **开发规范**：[代码规范.md](代码规范.md)
- **项目管理**：[agile-tracking/](agile-tracking/)

## 📞 联系我们

- **项目维护者**：开发团队
- **问题反馈**：GitHub Issues
- **技术交流**：欢迎提交PR和Issue

---

<div align="center">
  <p>🌟 如果这个项目对你有帮助，请给我们一个Star！🌟</p>
  <p>Made with ❤️ by College AI Team</p>
</div> 