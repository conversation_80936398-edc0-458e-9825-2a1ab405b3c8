{"name": "college-ai-backend", "version": "1.0.0", "description": "高校AI助手后端服务 - 智能教育助手API服务", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["nodejs", "express", "typescript", "AI", "education", "api", "backend"], "author": "College AI Team", "license": "MIT", "dependencies": {"axios": "^1.7.2", "bcrypt": "^5.1.1", "cors": "^2.8.5", "dayjs": "^1.11.11", "dotenv": "^16.4.5", "express": "^4.18.2", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "socket.io": "^4.7.5"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/jsonwebtoken": "^9.0.6", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.14.8", "@types/supertest": "^6.0.2", "jest": "^29.7.0", "nodemon": "^3.1.4", "supertest": "^7.0.0", "ts-jest": "^29.1.5", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.5.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/src"], "testMatch": ["**/__tests__/**/*.ts", "**/*.test.ts"], "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts", "!src/index.ts"]}}