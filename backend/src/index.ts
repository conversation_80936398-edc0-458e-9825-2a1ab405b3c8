import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import multer from 'multer';
import path from 'path';
import { config } from 'dotenv';
import { createServer } from 'http';
import { Server, Socket } from 'socket.io';

// 加载环境变量
config();

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

const PORT = process.env.PORT || 3002;

// 中间件
app.use(helmet());
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:3002', 'http://localhost:3003'],
  credentials: true
}));
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 配置multer用于文件上传
const storage = multer.memoryStorage();
const upload = multer({
  storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
  },
  fileFilter: (req, file, cb) => {
    // 只允许图片文件
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('只支持图片文件'));
    }
  },
});

// 生成AI聊天响应
const generateAIResponse = (message: string): string => {
  const lowerMessage = message.toLowerCase();
  
  // 数学相关
  if (lowerMessage.includes('积分') || lowerMessage.includes('导数') || lowerMessage.includes('微积分')) {
    return `## 📚 高等数学解题\n\n**问题分析：**\n这是一个${lowerMessage.includes('积分') ? '积分' : '导数'}计算问题。\n\n**解答步骤：**\n1. 识别函数类型和积分区间\n2. 应用相应的积分公式\n3. 进行具体计算\n4. 验证结果的合理性\n\n详细的解题过程需要具体的题目内容，请提供完整的数学表达式。`;
  }
  
  // 图表生成
  if (lowerMessage.includes('图表') || lowerMessage.includes('生成图表')) {
    return `## 📊 数据可视化与图表分析\n\n**图表类型选择：**\n不同的数据需要不同类型的图表来最好地展示信息。\n\n**示例数据可视化：**\n下面展示了一个综合性的数据分析，包含图表和表格。`;
  }
  
  // 饼图分析
  if (lowerMessage.includes('饼图')) {
    return `## 🥧 饼图分析与数据分布\n\n**饼图特点：**\n饼图是最直观的数据分布展示方式，能清晰地显示各部分占整体的比例关系。\n\n**示例饼图分析：**\n以下是学生成绩等级分布的饼图分析，帮助您理解数据构成。`;
  }
  
  // 函数图像
  if (lowerMessage.includes('函数') || lowerMessage.includes('图像')) {
    return `## 📈 函数与图像分析\n\n**常见函数类型：**\n\n### 1. 基本函数\n- **线性函数：** y = kx + b\n- **二次函数：** y = ax² + bx + c\n\n**函数图像展示：**\n下面展示了常见函数的图像和对应的数据表格。`;
  }
  
  // 默认响应
  return `## 🤖 AI智能助手\n\n感谢您的提问！我是专为高校学生设计的AI学习助手。\n\n**我可以帮您解决：**\n\n### 📚 学科问题\n- **数学：** 高等数学、线性代数、概率统计\n- **物理：** 理论物理、实验物理、工程物理\n- **化学：** 有机化学、无机化学、分析化学\n\n请告诉我您的具体问题，我会提供专业的学术解答！`;
};

// 生成响应附件（图表、表格等）
const generateResponseAttachments = (message: string): any[] => {
  const lowerMessage = message.toLowerCase();
  const attachments: any[] = [];
  
  // 图表生成相关
  if (lowerMessage.includes('图表') || lowerMessage.includes('生成图表')) {
    attachments.push(
      {
        type: 'chart',
        data: {
          title: '学生成绩分析',
          type: 'bar',
          data: [
            { name: '数学', value: 85 },
            { name: '物理', value: 78 },
            { name: '化学', value: 92 },
            { name: '英语', value: 88 },
            { name: '计算机', value: 95 }
          ]
        }
      },
      {
        type: 'chart',
        data: {
          title: '成绩分布',
          type: 'pie',
          data: [
            { name: '优秀(90+)', value: 2 },
            { name: '良好(80-89)', value: 2 },
            { name: '中等(70-79)', value: 1 }
          ]
        }
      },
      {
        type: 'table',
        data: {
          title: '成绩统计表',
          headers: ['科目', '成绩', '等级', '评价'],
          rows: [
            ['数学', '85', '良好', '基础扎实'],
            ['物理', '78', '中等', '需要加强'],
            ['化学', '92', '优秀', '表现出色'],
            ['英语', '88', '良好', '稳步提升'],
            ['计算机', '95', '优秀', '能力突出']
          ]
        }
      }
    );
  }
  
  // 饼图分析
  if (lowerMessage.includes('饼图')) {
    attachments.push(
      {
        type: 'chart',
        data: {
          title: '学生成绩等级分布分析',
          type: 'pie',
          data: [
            { name: '优秀(90-100分)', value: 25 },
            { name: '良好(80-89分)', value: 35 },
            { name: '中等(70-79分)', value: 30 },
            { name: '及格(60-69分)', value: 10 }
          ]
        }
      },
      {
        type: 'table',
        data: {
          title: '成绩分布统计表',
          headers: ['等级', '分数范围', '人数', '百分比', '分析'],
          rows: [
            ['优秀', '90-100分', '25人', '25%', '成绩突出，学习效果优良'],
            ['良好', '80-89分', '35人', '35%', '成绩良好，占比最大'],
            ['中等', '70-79分', '30人', '30%', '成绩中等，有提升空间'],
            ['及格', '60-69分', '10人', '10%', '刚达及格线，需加强学习']
          ]
        }
      }
    );
  }
  
  // 函数图像
  if (lowerMessage.includes('函数') || lowerMessage.includes('图像')) {
    attachments.push(
      {
        type: 'chart',
        data: {
          title: '二次函数图像',
          type: 'line',
          data: Array.from({length: 21}, (_, i) => {
            const x = i - 10;
            return { x, y: x * x };
          })
        }
      },
      {
        type: 'table',
        data: {
          title: '常见函数特性对比表',
          headers: ['函数类型', '表达式', '定义域', '值域', '主要特点'],
          rows: [
            ['线性函数', 'y = kx + b', 'R', 'R', '直线，斜率k'],
            ['二次函数', 'y = ax² + bx + c', 'R', 'a>0时: [f(顶点),+∞)', '抛物线'],
            ['指数函数', 'y = aˣ (a>0,a≠1)', 'R', '(0,+∞)', '单调，过点(0,1)'],
            ['对数函数', 'y = log_a(x)', '(0,+∞)', 'R', '单调，过点(1,0)']
          ]
        }
      }
    );
  }
  
  return attachments;
};

// 模拟AI搜题结果生成
const generateMockResult = (question: string, type: 'text' | 'image' | 'screenshot') => {
  const mockResults = {
    '已知函数f(x)=x²+2x-3，求f(x)的最小值': {
      answer: 'f(x)的最小值为-4',
      explanation: '这是一个二次函数求最值的问题。对于二次函数f(x)=ax²+bx+c（a>0），其最小值在顶点处取得。',
      steps: [
        '将函数f(x)=x²+2x-3配方',
        'f(x) = (x+1)² - 1 - 3 = (x+1)² - 4',
        '由于(x+1)²≥0，所以f(x)≥-4',
        '当x=-1时，f(x)取得最小值-4'
      ],
      confidence: 0.98
    }
  };

  const defaultResult = {
    answer: '通过AI分析，这道题的答案是...',
    explanation: '这是一道典型的数学题目，需要运用相关的数学知识点来解决。',
    steps: [
      '分析题目条件和要求',
      '选择合适的解题方法',
      '进行具体的计算推导',
      '得出最终答案'
    ],
    confidence: 0.85
  };

  const result = mockResults[question as keyof typeof mockResults] || defaultResult;

  return {
    id: `search_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    question: {
      id: `q_${Date.now()}`,
      question: question,
      type: type,
      subject: 'math',
      difficulty: 'medium' as const,
      createdAt: new Date(),
    },
    answer: result.answer,
    explanation: result.explanation,
    steps: result.steps,
    confidence: result.confidence,
    relatedQuestions: [
      {
        id: 'related_1',
        question: '已知函数g(x)=2x²-4x+1，求g(x)的最小值',
        type: 'text' as const,
        subject: 'math',
        difficulty: 'medium' as const,
        createdAt: new Date(),
      },
      {
        id: 'related_2',
        question: '求函数h(x)=-x²+6x-5的最大值',
        type: 'text' as const,
        subject: 'math',
        difficulty: 'medium' as const,
        createdAt: new Date(),
      }
    ],
    createdAt: new Date(),
  };
};

// API路由

// 健康检查
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    message: '高校AI助手后端服务运行正常',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// 文字搜题
app.post('/api/v1/search/text', (req, res) => {
  try {
    const { question } = req.body;
    
    if (!question || typeof question !== 'string') {
      return res.status(400).json({
        success: false,
        message: '请提供有效的题目内容',
        code: 400
      });
    }

    // 模拟AI处理延迟
    setTimeout(() => {
      const result = generateMockResult(question.trim(), 'text');
      
      res.json({
        success: true,
        data: result,
        message: '搜题成功',
        code: 200
      });
    }, 1500); // 1.5秒延迟模拟AI处理

  } catch (error) {
    console.error('Text search error:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      code: 500
    });
  }
});

// 图片搜题
app.post('/api/v1/search/image', upload.single('image'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '请上传图片文件',
        code: 400
      });
    }

    // 模拟OCR识别
    const mockQuestions = [
      '计算定积分∫₀¹ x²dx的值',
      '求解方程组：x + y = 5, 2x - y = 1',
      '在三角形ABC中，∠A=60°，b=4，c=3，求边a的长度'
    ];
    
    const randomQuestion = mockQuestions[Math.floor(Math.random() * mockQuestions.length)];

    // 模拟AI处理延迟
    setTimeout(() => {
      const result = generateMockResult(randomQuestion, 'image');
      
      res.json({
        success: true,
        data: result,
        message: '图片搜题成功',
        code: 200
      });
    }, 2000); // 2秒延迟模拟图片处理

  } catch (error) {
    console.error('Image search error:', error);
    res.status(500).json({
      success: false,
      message: '图片搜题失败',
      code: 500
    });
  }
});

// 截屏搜题
app.post('/api/v1/search/screenshot', (req, res) => {
  try {
    const { imageData } = req.body;
    
    if (!imageData) {
      return res.status(400).json({
        success: false,
        message: '请提供截屏数据',
        code: 400
      });
    }

    // 模拟截屏处理
    setTimeout(() => {
      const result = generateMockResult('截屏识别的数学题目', 'screenshot');
      
      res.json({
        success: true,
        data: result,
        message: '截屏搜题成功',
        code: 200
      });
    }, 1800); // 1.8秒延迟

  } catch (error) {
    console.error('Screenshot search error:', error);
    res.status(500).json({
      success: false,
      message: '截屏搜题失败',
      code: 500
    });
  }
});

// 获取搜题历史（模拟）
app.get('/api/v1/search/history', (req, res) => {
  res.json({
    success: true,
    data: [],
    message: '搜题历史获取成功',
    code: 200
  });
});

// 获取题目示例
app.get('/api/v1/search/examples', (req, res) => {
  const examples = [
    {
      id: 'example_1',
      question: '已知函数f(x)=x²+2x-3，求f(x)的最小值',
      type: 'text',
      subject: 'math',
      difficulty: 'medium',
      createdAt: new Date(),
    },
    {
      id: 'example_2',
      question: '在三角形ABC中，∠A=60°，b=4，c=3，求边a的长度',
      type: 'text',
      subject: 'math',
      difficulty: 'medium',
      createdAt: new Date(),
    },
    {
      id: 'example_3',
      question: '计算定积分∫₀¹ x²dx的值',
      type: 'text',
      subject: 'math',
      difficulty: 'hard',
      createdAt: new Date(),
    }
  ];

  res.json({
    success: true,
    data: examples,
    message: '题目示例获取成功',
    code: 200
  });
});

// AI聊天相关API
app.post('/api/v1/chat/send', (req, res) => {
  try {
    const { message, conversationId, attachments } = req.body;
    
    if (!message || typeof message !== 'string') {
      return res.status(400).json({
        success: false,
        message: '请提供有效的消息内容',
        code: 400
      });
    }

    // 模拟AI响应生成
    setTimeout(() => {
      const aiResponse = {
        id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        content: generateAIResponse(message.trim()),
        timestamp: new Date().toISOString(),
        attachments: generateResponseAttachments(message.trim())
      };
      
      res.json({
        success: true,
        data: aiResponse,
        message: '消息发送成功',
        code: 200
      });
    }, 1000); // 1秒延迟模拟AI思考

  } catch (error) {
    console.error('Chat send error:', error);
    res.status(500).json({
      success: false,
      message: '聊天服务异常',
      code: 500
    });
  }
});

// 获取对话列表
app.get('/api/v1/chat/conversations', (req, res) => {
  const mockConversations = [
    {
      id: 'conv_1',
      title: '数学解题助手',
      lastMessage: '帮我解一道微积分题目...',
      lastActivity: new Date(),
      messageCount: 5,
      createdAt: new Date(Date.now() - 86400000), // 1天前
    },
    {
      id: 'conv_2',
      title: '编程学习',
      lastMessage: '如何学习React和TypeScript...',
      lastActivity: new Date(Date.now() - 3600000), // 1小时前
      messageCount: 12,
      createdAt: new Date(Date.now() - 172800000), // 2天前
    }
  ];

  res.json({
    success: true,
    data: mockConversations,
    message: '对话列表获取成功',
    code: 200
  });
});

// 创建新对话
app.post('/api/v1/chat/conversations', (req, res) => {
  try {
    const { title } = req.body;
    
    const newConversation = {
      id: `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      title: title || '新对话',
      lastMessage: '',
      lastActivity: new Date(),
      messageCount: 0,
      createdAt: new Date(),
    };

    res.json({
      success: true,
      data: newConversation,
      message: '对话创建成功',
      code: 200
    });

  } catch (error) {
    console.error('Create conversation error:', error);
    res.status(500).json({
      success: false,
      message: '创建对话失败',
      code: 500
    });
  }
});

// 文件上传API
app.post('/api/v1/upload', upload.single('file'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '请选择要上传的文件',
        code: 400
      });
    }

    // 模拟文件处理
    const fileInfo = {
      id: `file_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: req.file.originalname,
      size: req.file.size,
      type: req.file.mimetype,
      url: `/uploads/${req.file.originalname}`, // 模拟URL
      uploadedAt: new Date(),
    };

    res.json({
      success: true,
      data: fileInfo,
      message: '文件上传成功',
      code: 200
    });

  } catch (error) {
    console.error('Upload error:', error);
    res.status(500).json({
      success: false,
      message: '文件上传失败',
      code: 500
    });
  }
});

// 降低AI功能相关API

// 配置multer用于文档上传
const documentStorage = multer.memoryStorage();
const documentUpload = multer({
  storage: documentStorage,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB
  },
  fileFilter: (req, file, cb) => {
    // 支持PDF、DOC、DOCX格式
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];
    
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('仅支持PDF、DOC、DOCX格式文件'));
    }
  },
});

// 文档AI检测和优化
app.post('/api/v1/reduce-ai/process', documentUpload.single('document'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '请上传文档文件',
        code: 400
      });
    }

    const { originalname, mimetype, size } = req.file;
    
    // 模拟AI检测和优化过程
    setTimeout(() => {
      const originalAIScore = Math.floor(Math.random() * 30) + 70; // 70-100
      const optimizedAIScore = Math.floor(Math.random() * 20) + 15; // 15-35
      const reductionPercentage = Math.round(((originalAIScore - optimizedAIScore) / originalAIScore) * 100);
      
      const fileExtension = originalname.split('.').pop()?.toLowerCase();
      const processedFileName = originalname.replace(/\.(pdf|docx?|txt)$/i, '_降低AI版.$1');
      
      const result = {
        id: `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        originalFileName: originalname,
        processedFileName: processedFileName,
        originalAIScore: originalAIScore,
        optimizedAIScore: optimizedAIScore,
        reductionPercentage: reductionPercentage,
        fileType: fileExtension,
        fileSize: size,
        processedAt: new Date(),
        downloadUrl: `/api/v1/reduce-ai/download/${Date.now()}`, // 模拟下载链接
        status: 'completed'
      };
      
      res.json({
        success: true,
        data: result,
        message: 'AI痕迹降低处理完成',
        code: 200
      });
    }, 5000); // 5秒延迟模拟处理时间

  } catch (error) {
    console.error('Document processing error:', error);
    res.status(500).json({
      success: false,
      message: '文档处理失败',
      code: 500
    });
  }
});

// 获取处理历史
app.get('/api/v1/reduce-ai/history', (req, res) => {
  const mockHistory = [
    {
      id: 'doc_1',
      originalFileName: '学术论文草稿.docx',
      processedFileName: '学术论文草稿_降低AI版.docx',
      originalAIScore: 85,
      optimizedAIScore: 28,
      reductionPercentage: 67,
      fileType: 'docx',
      processedAt: new Date(Date.now() - 1800000), // 30分钟前
      downloadUrl: '/api/v1/reduce-ai/download/doc_1',
      status: 'completed'
    },
    {
      id: 'doc_2',
      originalFileName: '研究报告.pdf',
      processedFileName: '研究报告_降低AI版.pdf',
      originalAIScore: 92,
      optimizedAIScore: 35,
      reductionPercentage: 62,
      fileType: 'pdf',
      processedAt: new Date(Date.now() - 3600000), // 1小时前
      downloadUrl: '/api/v1/reduce-ai/download/doc_2',
      status: 'completed'
    }
  ];

  res.json({
    success: true,
    data: mockHistory,
    message: '处理历史获取成功',
    code: 200
  });
});

// 下载处理后的文档
app.get('/api/v1/reduce-ai/download/:id', (req, res) => {
  try {
    const { id } = req.params;
    
    // 模拟文档内容生成
    const mockContent = `降低AI处理完成
文档ID: ${id}
处理时间: ${new Date().toLocaleString()}
AI痕迹已成功降低
原始AI痕迹: 85%
优化后痕迹: 28%
降低程度: 67%

这是一个模拟的处理后文档内容。在实际应用中，这里会返回真正的优化后文档。`;

    res.setHeader('Content-Type', 'application/octet-stream');
    res.setHeader('Content-Disposition', `attachment; filename="processed_document_${id}.txt"`);
    res.send(Buffer.from(mockContent, 'utf8'));

  } catch (error) {
    console.error('Download error:', error);
    res.status(500).json({
      success: false,
      message: '文档下载失败',
      code: 500
    });
  }
});

// 删除处理记录
app.delete('/api/v1/reduce-ai/history/:id', (req, res) => {
  try {
    const { id } = req.params;
    
    res.json({
      success: true,
      message: `记录 ${id} 删除成功`,
      code: 200
    });

  } catch (error) {
    console.error('Delete error:', error);
    res.status(500).json({
      success: false,
      message: '删除失败',
      code: 500
    });
  }
});

// 获取AI检测统计
app.get('/api/v1/reduce-ai/stats', (req, res) => {
  const mockStats = {
    totalProcessed: 15,
    avgReduction: 64,
    recentProcessed: 3,
    totalSaved: 47, // 总计降低的AI痕迹百分比
    successRate: 98
  };

  res.json({
    success: true,
    data: mockStats,
    message: '统计数据获取成功',
    code: 200
  });
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: '接口不存在',
    code: 404
  });
});

// 错误处理中间件
app.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Server Error:', error);
  res.status(500).json({
    success: false,
    message: '服务器内部错误',
    code: 500
  });
});

// WebSocket 连接处理
io.on('connection', (socket: Socket) => {
  console.log(`用户连接: ${socket.id}`);
  
  socket.on('join-chat', (userId: string) => {
    socket.join(`user-${userId}`);
    console.log(`用户 ${userId} 加入聊天室`);
  });
  
  socket.on('send-message', (data: any) => {
    // 广播消息给所有用户
    io.emit('receive-message', {
      ...data,
      timestamp: new Date().toISOString()
    });
  });
  
  socket.on('disconnect', () => {
    console.log(`用户断开连接: ${socket.id}`);
  });
});

// 端口自动检测和启动
async function findAvailablePort(startPort: number): Promise<number> {
  const net = await import('net');
  
  return new Promise((resolve) => {
    const server = net.createServer();
    server.listen(startPort, () => {
      const port = (server.address() as any)?.port;
      server.close(() => resolve(port));
    });
    server.on('error', () => {
      resolve(findAvailablePort(startPort + 1));
    });
  });
}

// 启动服务器
async function startServer() {
  try {
    const preferredPort = parseInt(process.env.PORT || '3001', 10);
    const port = await findAvailablePort(preferredPort);
    
    server.listen(port, () => {
      console.log('🚀 高校AI助手后端服务启动成功');
      console.log(`📍 服务地址: http://localhost:${port}`);
      console.log(`🏥 健康检查: http://localhost:${port}/health`);
      console.log(`🛠️  测试接口: http://localhost:${port}/api/v1/test`);
      console.log('🎯 支持功能:');
      console.log('   - 智能聊天对话 (WebSocket + REST)');
      console.log('   - 作业辅导分析');
      console.log('   - 论文写作助手');
      console.log('   - PPT自动生成');
      console.log('   - 学习轨迹追踪');
      console.log('   - 用户认证系统');
    });
  } catch (error) {
    console.error('❌ 服务器启动失败:', error);
    process.exit(1);
  }
}

startServer();

export default app; 