import request from 'supertest'
import express from 'express'

// 创建简单的测试应用
const app = express()
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    service: '高校AI助手后端服务'
  })
})

describe('Health Check API', () => {
  it('should return 200 status for health check', async () => {
    const response = await request(app)
      .get('/health')
      .expect(200)

    expect(response.body).toHaveProperty('status', 'ok')
    expect(response.body).toHaveProperty('service', '高校AI助手后端服务')
    expect(response.body).toHaveProperty('timestamp')
  })

  it('should return proper JSON format', async () => {
    const response = await request(app)
      .get('/health')
      .expect('Content-Type', /json/)

    expect(typeof response.body).toBe('object')
    expect(response.body.status).toBeDefined()
  })
}) 