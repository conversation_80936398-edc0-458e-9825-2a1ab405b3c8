---
description: 
globs: 
alwaysApply: false
---
# 文档指导规则

## 文档结构
项目文档遵循标准化结构，所有文档位于 `docs/` 目录下。

## 核心文档说明

### 产品文档
- **[docs/PRD.md](mdc:docs/PRD.md)**: 产品需求文档，包含完整的产品规划、功能定义、用户体验设计
- **[docs/需求文档.md](mdc:docs/需求文档.md)**: 详细需求分析，用户分析、功能需求、技术约束、验收标准

### 技术文档  
- **[docs/技术架构设计.md](mdc:docs/技术架构设计.md)**: 系统架构、模块设计、数据库设计、API设计
- **[docs/开发指南.md](mdc:docs/开发指南.md)**: 环境搭建、开发流程、部署指南、故障排除

### 项目管理文档
- **[README.md](mdc:README.md)**: 项目概览、快速开始、核心功能介绍
- **[代码规范.md](mdc:代码规范.md)**: 编码标准、最佳实践、规范要求
- **[docs/项目总结.md](mdc:docs/项目总结.md)**: 项目进展、已完成工作、技术亮点

## 文档更新原则
1. 每完成一轮开发，更新相关文档
2. 新增功能时，同步更新需求文档
3. 发现问题时，记录到代码规范文档
4. 定期更新项目总结

## 文档撰写规范
- 使用中文撰写
- 结构清晰，层次分明
- 包含具体的技术细节
- 提供实际的代码示例
- 保持文档与代码同步

## 文档引用
在代码中引用文档时，使用以下格式：
- 参考文档：`详见 docs/文档名.md`
- 相关规范：`遵循 代码规范.md 中的要求`
- 实现细节：`按照 docs/技术架构设计.md 中的设计`

## 特殊文档说明

### README.md
- 项目首页文档，提供项目概览
- 包含安装、使用、贡献指南
- 定期更新项目状态

### 代码规范.md
- 编码标准和最佳实践
- 积累开发中的经验和教训
- 记录常见问题和解决方案
- 强调重复出现的bug

### 环境配置
- **[env.example](mdc:env.example)**: 环境变量配置模板
- **[docker-compose.yml](mdc:docker-compose.yml)**: 容器化部署配置

## 文档维护职责
- 开发人员负责技术文档的更新
- 产品经理负责需求文档的维护  
- 项目经理负责项目管理文档的更新
- 所有团队成员共同维护代码规范文档
