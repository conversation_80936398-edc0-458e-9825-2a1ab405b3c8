---
description: 
globs: 
alwaysApply: false
---
# AI助手特殊规则

## 交流规范
- 与后滩萧亚轩交流时使用中文
- 保持专业友好的语调
- 提供详细的技术解释和建议

## 工具使用原则
- 优先使用并行工具调用，提高效率
- 文件读取遵循分批原则（500行限制）
- 终端命令使用非后台方式执行
- 避免创建不必要的临时文件

## 代码编辑策略
- 单文件不超过800行，超过需要重构
- 使用简洁清晰的逻辑实现
- 严格遵循TypeScript类型检查
- 优先编辑现有文件而非创建新文件

## 项目理解重点
基于以下文档理解项目：
- [docs/PRD.md](mdc:docs/PRD.md) - 产品整体规划
- [docs/需求文档.md](mdc:docs/需求文档.md) - 详细功能需求
- [docs/技术架构设计.md](mdc:docs/技术架构设计.md) - 技术实现方案
- [代码规范.md](mdc:代码规范.md) - 编码标准

## 功能实现优先级
1. AI对话助手（核心功能）
2. AI PPT生成器
3. AI论文助手
4. AI理科作业助手
5. AI痕迹消除

## 质量保证流程
1. 代码检查和lint修复
2. 单元测试编写和执行
3. 集成测试覆盖
4. E2E测试（Playwright）
5. 控制台错误优先解决

## 文档同步要求
- 新功能开发时更新需求文档
- 遇到问题时更新代码规范
- 完成阶段性开发时更新README
- 积累经验时更新项目总结

## Git管理原则
- 仅在项目稳定可运行时提交
- 使用清晰的commit信息
- 不提交临时文件和调试代码
- 保持代码库干净整洁

## 错误处理策略
- 重复出现的bug要特别强调
- 记录问题到代码规范文档
- 提供具体的解决方案
- 建立错误预防机制

## AI模型集成注意事项
- OpenAI API作为主要选择
- 准备本地模型备选方案
- 考虑成本控制和性能优化
- 实现多模态功能支持

## 用户体验标准
- 界面美观，符合高校场景
- 响应快速，交互流畅
- 支持多设备访问
- 遵循无障碍设计原则

## 安全考虑
- 用户数据加密存储
- API访问权限控制
- 输入验证和防护
- 隐私保护机制
