---
description: 
globs: 
alwaysApply: false
---
# 开发工作流规则

## 开发流程
遵循 [docs/开发指南.md](mdc:docs/开发指南.md) 中的完整开发流程。

## 中文交流
- 所有与后滩萧亚轩的交流使用中文
- 代码注释使用中文
- 文档撰写使用中文

## 开发环境
参考 [docs/开发指南.md](mdc:docs/开发指南.md) 进行环境搭建：
- Node.js 18+
- npm 8+
- Docker 和 Docker Compose
- MongoDB 和 Redis

## 文件读取原则
- 行数超过500的文件分多次创建或修改、读取
- 每次读取不超过250行
- 确保获取完整上下文

## 代码实现原则
- 使用简洁简单的逻辑实现代码
- 一个文件原则上不超过800行
- 超过800行需要重构拆分

## 测试流程
1. 单元测试：每个组件和服务
2. 集成测试：API 接口
3. E2E 测试：用户场景（使用 Playwright）
4. 每增加一批页面，完成代码检查和项目检查
5. 优先解决控制台报错信息

## 终端命令执行
- 以非后台方式在对话框中运行终端命令
- 使用 `| cat` 避免分页显示
- 长运行命令设置 background: true

## 项目状态检查
每完成一轮修改：
1. 更新 [README.md](mdc:README.md)
2. 总结编码过程中发现的问题
3. 更新 [代码规范.md](mdc:代码规范.md)
4. 记录重复出现的bug

## Git 提交规则
- 确保项目稳定可运行后再提交
- 使用 `git add .` 和 `git commit` 提交
- 如果没有 git 仓库先初始化
- 不提交不能工作的项目

## 需求文档维护
- 添加新功能时修改 [docs/需求文档.md](mdc:docs/需求文档.md)
- 更新功能记录
- 按照需求文档完成项目构建

## 近期目标
1. 解决测试中发现的问题
2. 按照需求文档完成项目构建
3. 持续推进开发与优化工作
4. 完成检查和测试
