---
description: 
globs: 
alwaysApply: false
---
# 高校AI助手项目结构规则

## 项目概述
这是一个面向高校师生的综合性AI学习助手平台，包含AI对话、PPT生成、论文写作、理科作业辅导和AI痕迹消除等功能。

## 核心文档
- 产品需求文档: [docs/PRD.md](mdc:docs/PRD.md)
- 详细需求文档: [docs/需求文档.md](mdc:docs/需求文档.md)
- 技术架构设计: [docs/技术架构设计.md](mdc:docs/技术架构设计.md)
- 开发指南: [docs/开发指南.md](mdc:docs/开发指南.md)
- 项目总结: [docs/项目总结.md](mdc:docs/项目总结.md)
- 代码规范: [代码规范.md](mdc:代码规范.md)

## 项目结构
```
college/
├── docs/                 # 项目文档
├── frontend/            # React前端应用
├── backend/             # Node.js后端应用
├── shared/              # 共享代码（待创建）
├── tests/               # 测试文件（待创建）
├── scripts/             # 构建脚本（待创建）
├── docker/              # Docker配置（待创建）
└── nginx/               # Nginx配置（待创建）
```

## 技术栈
- **前端**: React 18 + TypeScript + Ant Design + Redux Toolkit
- **后端**: Node.js + Express + TypeScript + MongoDB + Redis
- **部署**: Docker + Docker Compose + Nginx

## 配置文件
- 根目录配置: [package.json](mdc:package.json)
- 前端配置: [frontend/package.json](mdc:frontend/package.json) 和 [frontend/tsconfig.json](mdc:frontend/tsconfig.json)
- 后端配置: [backend/package.json](mdc:backend/package.json) 和 [backend/tsconfig.json](mdc:backend/tsconfig.json)
- Docker配置: [docker-compose.yml](mdc:docker-compose.yml)
- 环境变量示例: [env.example](mdc:env.example)

## 开发命令
- `npm run setup`: 安装所有依赖
- `npm run dev`: 启动开发环境（前后端同时）
- `npm run build`: 构建生产版本
- `npm run test`: 运行所有测试
- `npm run lint`: 代码检查和格式化
