---
description: 
globs: 
alwaysApply: false
---
# 代码规范规则

## 总体原则
遵循 [代码规范.md](mdc:代码规范.md) 中定义的所有规范。重点包括：

## 文件结构规范
- 单个文件不超过800行，超过需要重构
- 使用合理且统一的目录结构
- 每个功能模块独立目录

## TypeScript 规范
- 严格使用 TypeScript，禁用 `any` 类型
- 优先使用接口（interface）而非类型别名（type）
- 所有函数必须有明确的返回类型
- 使用严格的 null 检查

## React 组件规范
- 使用函数组件和 Hooks
- 组件名使用 PascalCase
- 优先使用 TypeScript + React.FC
- 组件文件结构：
  ```
  ComponentName/
  ├── index.tsx
  ├── ComponentName.tsx
  ├── ComponentName.styles.ts
  └── ComponentName.types.ts
  ```

## API 服务规范
- 使用 RESTful API 设计
- 统一的响应格式
- 完整的错误处理
- 输入验证和类型检查

## 样式规范
- 使用 Ant Design 组件库
- CSS-in-JS 方案（styled-components）
- 响应式设计原则
- 统一的颜色和字体规范

## 性能优化规范
- React.memo 优化组件渲染
- useMemo 和 useCallback 优化计算
- 图片懒加载和压缩
- API 请求缓存

## 测试规范
- 单元测试覆盖率 > 80%
- 集成测试覆盖关键流程
- E2E 测试覆盖主要用户场景
- Mock 数据规范

## 用户提醒
在代码修改过程中，如果发现不规范的代码，应该：
1. 立即修正
2. 记录问题到代码规范文档
3. 在后续开发中避免重复错误
4. 经常重复的bug需要特别强调
