---
description: 
globs: 
alwaysApply: false
---
# 功能需求规则

## 项目目标
基于 [docs/PRD.md](mdc:docs/PRD.md) 和 [docs/需求文档.md](mdc:docs/需求文档.md)，构建面向高校师生的AI学习助手平台。

## 核心功能模块

### 1. AI对话助手
- **通用问答**: 支持各类学术和生活问题
- **理科科目优化**: 数学、物理、化学等专业优化
- **图片识别**: 支持图片上传和内容识别
- **图表生成**: 根据数据生成各类图表

### 2. AI PPT生成器
- **预设模板**: 提供多种高校场景模板
- **文档生成**: 自动生成PPT结构和内容
- **单页编辑**: 支持逐页精细编辑
- **VBA支持**: 高级功能脚本支持

### 3. AI论文助手
- **学术写作**: 论文结构、语言优化
- **真实文献搜索**: 集成学术数据库
- **格式支持**: Word和LaTeX双格式
- **引用管理**: 自动引用格式化

### 4. AI理科作业助手
- **多格式识别**: 支持PDF、图片、手写等
- **解题思路**: 提供详细解题步骤
- **标准输出**: 规范化答案格式

### 5. AI痕迹消除
- **检测AI生成内容**: 识别AI写作痕迹
- **内容优化**: 使内容更自然
- **格式保持**: 保持原有格式

## 技术要求
参考 [docs/技术架构设计.md](mdc:docs/技术架构设计.md)：

### 前端要求
- React 18 + TypeScript
- Ant Design UI库
- Redux状态管理
- 响应式设计

### 后端要求
- Node.js + Express
- MongoDB数据库
- Redis缓存
- JWT认证

### AI集成
- OpenAI API集成
- 本地模型备选方案
- 多模态支持
- 实时响应

## 用户体验要求
- 界面美观大方，符合高校场景
- 响应速度快，用户体验流畅
- 支持多设备访问
- 无障碍设计

## 安全和隐私
- 用户数据加密存储
- 访问权限控制
- 数据备份和恢复
- 隐私保护机制

## 性能指标
- 页面加载时间 < 3秒
- API响应时间 < 2秒
- 支持1000+并发用户
- 99.9%可用性
