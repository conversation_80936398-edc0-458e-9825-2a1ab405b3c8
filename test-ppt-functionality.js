const { chromium } = require('playwright');

async function testPPTModule() {
  console.log('🚀 开始测试PPT模块功能...');
  
  const browser = await chromium.launch({ 
    headless: false,
    slowMo: 1000 
  });
  
  const page = await browser.newPage();
  
  try {
    // 1. 导航到PPT页面
    console.log('📍 导航到PPT页面...');
    await page.goto('http://localhost:3001/ppt');
    await page.waitForTimeout(2000);
    
    // 2. 检查页面是否正确加载
    console.log('🔍 检查页面元素...');
    const title = await page.textContent('h1');
    console.log(`页面标题: ${title}`);
    
    // 3. 检查模板是否显示
    console.log('📋 检查模板列表...');
    const templates = await page.locator('.grid .group').count();
    console.log(`找到 ${templates} 个模板`);
    
    if (templates > 0) {
      // 4. 点击第一个模板的"使用模板"按钮
      console.log('✨ 点击第一个模板的"使用模板"按钮...');
      const firstTemplate = page.locator('.grid .group').first();
      const useTemplateButton = firstTemplate.locator('button:has-text("使用模板")');
      
      await useTemplateButton.click();
      await page.waitForTimeout(2000);
      
      // 5. 检查是否切换到编辑模式
      console.log('🔄 检查是否切换到编辑模式...');
      const editTitle = await page.locator('h2:has-text("编辑PPT")').isVisible();
      
      if (editTitle) {
        console.log('✅ 成功切换到编辑模式');
        
        // 6. 检查幻灯片列表
        console.log('📄 检查幻灯片列表...');
        const slides = await page.locator('.grid .motion-div').count();
        console.log(`生成了 ${slides} 张幻灯片`);
        
        // 7. 点击第一张幻灯片
        if (slides > 0) {
          console.log('👆 点击第一张幻灯片...');
          await page.locator('.grid .motion-div').first().click();
          await page.waitForTimeout(1000);
          
          // 8. 检查是否显示编辑表单
          const titleInput = await page.locator('input[placeholder*="幻灯片标题"]').isVisible();
          const contentTextarea = await page.locator('textarea[placeholder*="幻灯片内容"]').isVisible();
          
          if (titleInput && contentTextarea) {
            console.log('✅ 幻灯片编辑表单正常显示');
            
            // 9. 测试编辑功能
            console.log('✏️ 测试编辑功能...');
            await page.fill('input[placeholder*="幻灯片标题"]', '测试标题');
            await page.fill('textarea[placeholder*="幻灯片内容"]', '这是测试内容');
            await page.waitForTimeout(1000);
            
            // 10. 测试导出功能
            console.log('💾 测试导出功能...');
            const exportButton = page.locator('button:has-text("导出PPT")');
            if (await exportButton.isVisible()) {
              await exportButton.click();
              console.log('✅ 导出功能触发成功');
            }
            
            console.log('🎉 所有功能测试完成！');
          } else {
            console.log('❌ 编辑表单未正确显示');
          }
        } else {
          console.log('❌ 没有生成幻灯片');
        }
      } else {
        console.log('❌ 未能切换到编辑模式');
      }
    } else {
      console.log('❌ 没有找到模板');
    }
    
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
  } finally {
    console.log('🔚 测试完成，保持浏览器打开以供查看');
    // 不关闭浏览器，让用户可以查看结果
    // await browser.close();
  }
}

// 运行测试
testPPTModule().catch(console.error); 