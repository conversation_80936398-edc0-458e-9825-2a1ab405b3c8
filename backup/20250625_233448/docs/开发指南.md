# 高校AI助手 - 开发指南

## 分阶段开发策略

### 阶段1: 前端Mock开发（当前阶段 - 4-6周）

**目标**: 使用Mock数据完成前端UI/UX开发，定义完整API规范

**技术栈**:
- React 18 + Vite + TypeScript
- Ant Design 5.x + CSS Modules
- MSW (Mock Service Worker)
- Zustand状态管理

**开发步骤**:
1. **项目初始化**
   ```bash
   cd frontend
   npm create vite@latest . -- --template react-ts
   npm install antd @ant-design/icons
   npm install zustand axios react-router-dom
   npm install --save-dev msw @types/node
   ```

2. **Mock数据配置**
   ```bash
   npx msw init public/ --save
   ```

3. **核心功能开发顺序**:
   - 用户认证界面 (登录/注册)
   - 主导航和布局组件
   - AI对话界面和交互
   - PPT生成器界面
   - 论文助手界面
   - 作业助手界面
   - 痕迹消除工具界面

4. **Mock API设计**:
   - 定义完整的API接口规范
   - 创建真实的Mock响应数据
   - 模拟各种业务场景

**交付物**:
- 完整的前端应用
- 详细的API接口文档
- 用户体验测试报告

### 阶段2: 后端开发（6-8周）

**目标**: 基于前端需求开发真实API，渐进式替换Mock数据

**推荐技术栈**: FastAPI + PostgreSQL + Redis + Qdrant

**开发步骤**:
1. **环境搭建**
2. **数据库设计和迁移**
3. **核心API开发**
4. **AI服务集成**
5. **认证和权限系统**

### 阶段3: 集成测试与优化（2-3周）

**目标**: 前后端联调，性能优化，部署上线

**工作内容**:
- 移除Mock数据，连接真实API
- 性能优化和缓存策略
- 安全性测试和优化
- 部署和监控

## 1. 开发环境搭建

### 1.1 环境要求
- Node.js 18.0 或更高版本
- npm 8.0 或更高版本
- Docker 和 Docker Compose
- Git
- VS Code (推荐)

### 1.2 快速开始

```bash
# 克隆项目
git clone <repository-url>
cd college-ai-assistant

# 安装依赖
npm run setup

# 复制环境变量文件
cp env.example .env

# 启动开发环境
npm run dev
```

### 1.3 环境变量配置

创建 `.env` 文件并配置以下变量：

```bash
# 复制 env.example 到 .env
cp env.example .env

# 编辑环境变量
# 主要配置项：
# - OPENAI_API_KEY: OpenAI API密钥
# - MONGODB_URI: MongoDB连接字符串
# - JWT_SECRET: JWT密钥
```

## 2. 项目结构详解

```
college/
├── docs/                    # 项目文档
│   ├── PRD.md              # 产品需求文档
│   ├── 需求文档.md          # 详细需求文档
│   ├── 技术架构设计.md      # 技术架构设计
│   └── 开发指南.md          # 开发指南
├── frontend/               # 前端应用
│   ├── public/            # 静态资源
│   ├── src/               # 源代码
│   │   ├── components/    # 组件
│   │   │   ├── ui/        # 基础UI组件
│   │   │   ├── layout/    # 布局组件
│   │   │   └── business/  # 业务组件
│   │   ├── pages/         # 页面组件
│   │   ├── hooks/         # 自定义Hook
│   │   ├── services/      # API服务
│   │   ├── utils/         # 工具函数
│   │   ├── types/         # TypeScript类型
│   │   ├── constants/     # 常量定义
│   │   └── assets/        # 静态资源
│   ├── package.json
│   └── tsconfig.json
├── backend/               # 后端应用
│   ├── src/               # 源代码
│   │   ├── controllers/   # 控制器
│   │   ├── services/      # 业务服务
│   │   ├── models/        # 数据模型
│   │   ├── middleware/    # 中间件
│   │   ├── routes/        # 路由定义
│   │   ├── utils/         # 工具函数
│   │   ├── types/         # TypeScript类型
│   │   └── config/        # 配置文件
│   ├── package.json
│   └── tsconfig.json
├── shared/                # 共享代码
│   ├── types/             # 共享类型定义
│   └── utils/             # 共享工具函数
├── tests/                 # 测试文件
│   ├── unit/              # 单元测试
│   ├── integration/       # 集成测试
│   └── e2e/               # 端到端测试
├── scripts/               # 构建脚本
├── docker/                # Docker配置
├── nginx/                 # Nginx配置
├── package.json           # 根package.json
├── docker-compose.yml     # Docker Compose配置
└── README.md              # 项目说明
```

## 3. 开发工作流

### 3.1 功能开发流程

1. **创建功能分支**
```bash
git checkout -b feature/功能名称
```

2. **开发功能**
- 前端：在 `frontend/src` 下开发
- 后端：在 `backend/src` 下开发
- 遵循代码规范和命名规范

3. **编写测试**
- 单元测试：测试单个函数或组件
- 集成测试：测试API接口
- E2E测试：测试完整用户流程

4. **提交代码**
```bash
git add .
git commit -m "feat(scope): 功能描述"
```

5. **合并主分支**
```bash
git checkout main
git merge feature/功能名称
```

### 3.2 代码审查要点

- [ ] 代码符合规范
- [ ] 功能实现完整
- [ ] 测试覆盖充分
- [ ] 文档更新及时
- [ ] 性能影响评估
- [ ] 安全性检查

## 4. 前端开发指南

### 4.1 技术栈
- React 18 + TypeScript
- Ant Design 5.x
- Redux Toolkit
- React Router v6
- Axios

### 4.2 组件开发规范

#### 4.2.1 函数组件模板
```typescript
import React from 'react';
import { Button } from 'antd';
import './ComponentName.less';

interface IComponentNameProps {
  title: string;
  onClick?: () => void;
}

const ComponentName: React.FC<IComponentNameProps> = ({
  title,
  onClick
}) => {
  return (
    <div className="component-name">
      <h3>{title}</h3>
      <Button onClick={onClick}>点击</Button>
    </div>
  );
};

export default ComponentName;
```

#### 4.2.2 Hook使用规范
```typescript
import { useState, useEffect, useCallback } from 'react';

const useCustomHook = (initialValue: string) => {
  const [value, setValue] = useState(initialValue);
  
  const updateValue = useCallback((newValue: string) => {
    setValue(newValue);
  }, []);
  
  useEffect(() => {
    // 副作用逻辑
  }, [value]);
  
  return { value, updateValue };
};
```

### 4.3 状态管理

#### 4.3.1 Redux Store配置
```typescript
// store/index.ts
import { configureStore } from '@reduxjs/toolkit';
import authSlice from './slices/authSlice';
import chatSlice from './slices/chatSlice';

export const store = configureStore({
  reducer: {
    auth: authSlice,
    chat: chatSlice,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
```

#### 4.3.2 Slice示例
```typescript
// store/slices/authSlice.ts
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface IAuthState {
  user: IUser | null;
  token: string | null;
  isLoading: boolean;
}

const initialState: IAuthState = {
  user: null,
  token: null,
  isLoading: false,
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setUser: (state, action: PayloadAction<IUser>) => {
      state.user = action.payload;
    },
    clearAuth: (state) => {
      state.user = null;
      state.token = null;
    },
  },
});

export const { setUser, clearAuth } = authSlice.actions;
export default authSlice.reducer;
```

### 4.4 API服务

#### 4.4.1 API客户端配置
```typescript
// services/api.ts
import axios from 'axios';

const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL,
  timeout: 10000,
});

// 请求拦截器
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// 响应拦截器
api.interceptors.response.use(
  (response) => response.data,
  (error) => {
    if (error.response?.status === 401) {
      // 处理认证失败
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export default api;
```

#### 4.4.2 服务层示例
```typescript
// services/chatService.ts
import api from './api';

export interface ISendMessageRequest {
  conversationId?: string;
  content: string;
  attachments?: File[];
}

export interface ISendMessageResponse {
  message: IMessage;
  conversation: IConversation;
}

class ChatService {
  async sendMessage(data: ISendMessageRequest): Promise<ISendMessageResponse> {
    const formData = new FormData();
    formData.append('content', data.content);
    
    if (data.conversationId) {
      formData.append('conversationId', data.conversationId);
    }
    
    if (data.attachments) {
      data.attachments.forEach((file) => {
        formData.append('attachments', file);
      });
    }
    
    return api.post('/conversations/message', formData);
  }
  
  async getConversations(): Promise<IConversation[]> {
    return api.get('/conversations');
  }
}

export const chatService = new ChatService();
```

## 5. 后端开发指南

### 5.1 技术栈
- Node.js + Express + TypeScript
- MongoDB + Mongoose
- Redis
- JWT认证

### 5.2 项目结构

#### 5.2.1 控制器示例
```typescript
// controllers/ChatController.ts
import { Request, Response } from 'express';
import { ChatService } from '@/services/ChatService';
import { validateSendMessage } from '@/validators/chatValidators';

export class ChatController {
  private chatService = new ChatService();
  
  async sendMessage(req: Request, res: Response) {
    try {
      const { error, value } = validateSendMessage(req.body);
      if (error) {
        return res.status(400).json({
          success: false,
          error: { message: error.details[0].message }
        });
      }
      
      const result = await this.chatService.sendMessage(
        req.user.id,
        value
      );
      
      res.json({
        success: true,
        data: result
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: { message: '发送消息失败' }
      });
    }
  }
}
```

#### 5.2.2 服务层示例
```typescript
// services/ChatService.ts
import { ConversationModel } from '@/models/Conversation';
import { AIService } from './AIService';

export class ChatService {
  private aiService = new AIService();
  
  async sendMessage(userId: string, data: ISendMessageData) {
    // 1. 查找或创建对话
    let conversation = await ConversationModel.findById(data.conversationId);
    if (!conversation) {
      conversation = new ConversationModel({
        userId,
        title: '新对话',
        messages: []
      });
    }
    
    // 2. 添加用户消息
    const userMessage = {
      role: 'user',
      content: data.content,
      timestamp: new Date()
    };
    conversation.messages.push(userMessage);
    
    // 3. 调用AI服务
    const aiResponse = await this.aiService.generateResponse(
      conversation.messages
    );
    
    // 4. 添加AI回复
    const aiMessage = {
      role: 'assistant',
      content: aiResponse.content,
      timestamp: new Date()
    };
    conversation.messages.push(aiMessage);
    
    // 5. 保存对话
    await conversation.save();
    
    return {
      conversation,
      userMessage,
      aiMessage
    };
  }
}
```

#### 5.2.3 数据模型示例
```typescript
// models/Conversation.ts
import mongoose, { Schema, Document } from 'mongoose';

interface IMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  attachments?: string[];
  timestamp: Date;
}

interface IConversation extends Document {
  userId: string;
  title: string;
  messages: IMessage[];
  category: string;
  createdAt: Date;
  updatedAt: Date;
}

const MessageSchema = new Schema({
  role: { type: String, required: true },
  content: { type: String, required: true },
  attachments: [String],
  timestamp: { type: Date, default: Date.now }
});

const ConversationSchema = new Schema({
  userId: { type: String, required: true },
  title: { type: String, required: true },
  messages: [MessageSchema],
  category: { type: String, default: 'general' }
}, {
  timestamps: true
});

export const ConversationModel = mongoose.model<IConversation>('Conversation', ConversationSchema);
```

### 5.3 中间件

#### 5.3.1 认证中间件
```typescript
// middleware/auth.ts
import jwt from 'jsonwebtoken';
import { Request, Response, NextFunction } from 'express';

declare global {
  namespace Express {
    interface Request {
      user: { id: string; email: string; role: string };
    }
  }
}

export const authMiddleware = (req: Request, res: Response, next: NextFunction) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({
        success: false,
        error: { message: '未提供认证令牌' }
      });
    }
    
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;
    req.user = decoded;
    next();
  } catch (error) {
    res.status(401).json({
      success: false,
      error: { message: '无效的认证令牌' }
    });
  }
};
```

#### 5.3.2 错误处理中间件
```typescript
// middleware/errorHandler.ts
import { Request, Response, NextFunction } from 'express';
import { logger } from '@/utils/logger';

export const errorHandler = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  logger.error('API Error:', error);
  
  // 开发环境返回详细错误信息
  if (process.env.NODE_ENV === 'development') {
    return res.status(500).json({
      success: false,
      error: {
        message: error.message,
        stack: error.stack
      }
    });
  }
  
  // 生产环境返回通用错误信息
  res.status(500).json({
    success: false,
    error: { message: '服务器内部错误' }
  });
};
```

## 6. 测试指南

### 6.1 单元测试

#### 6.1.1 前端组件测试
```typescript
// components/__tests__/Button.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import Button from '../Button';

describe('Button组件', () => {
  test('应该正确渲染文本', () => {
    render(<Button>点击我</Button>);
    expect(screen.getByText('点击我')).toBeInTheDocument();
  });
  
  test('点击时应该调用onClick回调', () => {
    const mockClick = jest.fn();
    render(<Button onClick={mockClick}>点击我</Button>);
    
    fireEvent.click(screen.getByText('点击我'));
    expect(mockClick).toHaveBeenCalledTimes(1);
  });
});
```

#### 6.1.2 后端服务测试
```typescript
// services/__tests__/ChatService.test.ts
import { ChatService } from '../ChatService';
import { ConversationModel } from '@/models/Conversation';

jest.mock('@/models/Conversation');

describe('ChatService', () => {
  let chatService: ChatService;
  
  beforeEach(() => {
    chatService = new ChatService();
  });
  
  test('应该成功发送消息', async () => {
    const mockConversation = {
      _id: 'conv_id',
      messages: [],
      save: jest.fn()
    };
    
    (ConversationModel.findById as jest.Mock).mockResolvedValue(mockConversation);
    
    const result = await chatService.sendMessage('user_id', {
      content: '测试消息'
    });
    
    expect(result).toBeDefined();
    expect(mockConversation.save).toHaveBeenCalled();
  });
});
```

### 6.2 集成测试

```typescript
// tests/integration/chat.test.ts
import request from 'supertest';
import app from '@/app';

describe('Chat API', () => {
  test('POST /api/conversations/message - 发送消息', async () => {
    const response = await request(app)
      .post('/api/conversations/message')
      .set('Authorization', 'Bearer valid_token')
      .send({
        content: '测试消息'
      });
    
    expect(response.status).toBe(200);
    expect(response.body.success).toBe(true);
    expect(response.body.data).toBeDefined();
  });
});
```

### 6.3 E2E测试

```typescript
// tests/e2e/chat.spec.ts
import { test, expect } from '@playwright/test';

test('用户可以发送消息并收到回复', async ({ page }) => {
  // 1. 登录
  await page.goto('/login');
  await page.fill('[data-testid=email]', '<EMAIL>');
  await page.fill('[data-testid=password]', 'password');
  await page.click('[data-testid=login-button]');
  
  // 2. 进入聊天页面
  await page.goto('/chat');
  
  // 3. 发送消息
  await page.fill('[data-testid=message-input]', '你好');
  await page.click('[data-testid=send-button]');
  
  // 4. 验证消息显示
  await expect(page.locator('[data-testid=user-message]')).toContainText('你好');
  
  // 5. 等待AI回复
  await expect(page.locator('[data-testid=ai-message]')).toBeVisible();
});
```

## 7. 部署指南

### 7.1 开发环境部署

```bash
# 使用Docker Compose启动完整环境
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f backend
```

### 7.2 生产环境部署

```bash
# 构建生产镜像
docker-compose -f docker-compose.prod.yml build

# 启动生产环境
docker-compose -f docker-compose.prod.yml up -d

# 数据库迁移
npm run db:migrate

# 监控服务状态
docker-compose -f docker-compose.prod.yml ps
```

### 7.3 CI/CD流程

```yaml
# .github/workflows/deploy.yml
name: Deploy

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm test
      
  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Deploy to production
        run: |
          # 部署脚本
```

## 8. 故障排除

### 8.1 常见问题

#### 问题1：前端无法连接后端
```bash
# 检查后端服务状态
docker-compose logs backend

# 检查网络连接
curl http://localhost:3001/api/health
```

#### 问题2：数据库连接失败
```bash
# 检查MongoDB状态
docker-compose logs mongodb

# 检查连接字符串
echo $MONGODB_URI
```

#### 问题3：AI API调用失败
```bash
# 检查API密钥
echo $OPENAI_API_KEY

# 检查网络连接
curl -H "Authorization: Bearer $OPENAI_API_KEY" https://api.openai.com/v1/models
```

### 8.2 日志分析

```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
tail -f logs/error.log

# 查看访问日志
tail -f logs/access.log
```

### 8.3 性能监控

```bash
# 检查内存使用
docker stats

# 检查数据库性能
mongostat --host localhost:27017

# 检查Redis状态
redis-cli info
```

## 9. 开发工具配置

### 9.1 VS Code配置

推荐安装的扩展：
- TypeScript and JavaScript Language Features
- ES7+ React/Redux/React-Native snippets
- Prettier - Code formatter
- ESLint
- Auto Rename Tag
- GitLens

### 9.2 调试配置

```json
// .vscode/launch.json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug Frontend",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/frontend/node_modules/.bin/react-scripts",
      "args": ["start"],
      "env": {
        "BROWSER": "none"
      }
    },
    {
      "name": "Debug Backend",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/backend/src/index.ts",
      "outFiles": ["${workspaceFolder}/backend/dist/**/*.js"],
      "runtimeArgs": ["-r", "ts-node/register"]
    }
  ]
}
```

这个开发指南提供了完整的开发环境搭建、代码规范、测试指南和部署流程，帮助团队成员快速上手项目开发。