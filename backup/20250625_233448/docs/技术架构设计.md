# 高校AI助手 - 技术架构设计

## 1. 系统架构概览

### 1.1 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                        用户层                                │
├─────────────────────────────────────────────────────────────┤
│  Web浏览器  │  移动端APP  │  微信小程序  │  桌面客户端        │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                        网关层                                │
├─────────────────────────────────────────────────────────────┤
│           Nginx负载均衡 + SSL终止 + 静态资源              │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                       前端应用层                             │
├─────────────────────────────────────────────────────────────┤
│  React SPA │ TypeScript │ Ant Design │ Redux Toolkit     │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼ HTTP/HTTPS
┌─────────────────────────────────────────────────────────────┐
│                       API网关层                              │
├─────────────────────────────────────────────────────────────┤
│   路由转发  │  认证鉴权  │  限流控制  │  API版本管理      │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                      后端服务层                              │
├─────────────────────────────────────────────────────────────┤
│  用户服务  │  对话服务  │  PPT服务  │  论文服务  │文档服务   │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                      AI服务层                                │
├─────────────────────────────────────────────────────────────┤
│  OpenAI API  │  本地模型  │  图像识别  │  文档处理        │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                      数据存储层                              │
├─────────────────────────────────────────────────────────────┤
│   MongoDB    │    Redis     │   文件存储   │    日志系统     │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 技术栈选型

#### 前端技术栈（当前阶段：Mock开发）
- **框架**: React 18 + Vite + TypeScript
- **UI库**: Ant Design 5.x
- **状态管理**: Zustand / Redux Toolkit
- **路由**: React Router v6
- **HTTP客户端**: Axios
- **Mock数据**: MSW (Mock Service Worker)
- **样式**: CSS Modules / Styled Components
- **数学公式**: KaTeX + react-katex
- **图表**: ECharts / Recharts
- **文件上传**: react-dropzone
- **代码高亮**: Prism.js + react-syntax-highlighter
- **Markdown**: react-markdown
- **测试**: Vitest + Testing Library + Playwright

#### 后端技术栈（推荐方案）

**方案一：Python生态（AI功能优化）**
- **框架**: FastAPI + Python 3.11+
- **数据库**: PostgreSQL + Redis + Qdrant(向量数据库)
- **ORM**: SQLAlchemy + Alembic
- **AI集成**: OpenAI API + LangChain + Transformers
- **认证**: JWT + OAuth2
- **文档**: 自动生成OpenAPI文档
- **测试**: pytest + FastAPI TestClient

**方案二：Node.js生态（前后端统一语言）**
- **框架**: Nest.js + TypeScript
- **数据库**: PostgreSQL + Redis + Prisma ORM
- **AI集成**: OpenAI SDK + LangChain.js
- **认证**: Passport.js + JWT
- **测试**: Jest + Supertest
- **进程管理**: PM2

**方案三：现代全栈（快速部署）**
- **框架**: Next.js 14 (App Router)
- **后端**: Supabase (数据库+认证+存储)
- **部署**: Vercel (一键部署)
- **AI**: OpenAI API

#### DevOps & 部署
- **容器化**: Docker + Docker Compose
- **反向代理**: Nginx
- **CI/CD**: GitHub Actions
- **监控**: Prometheus + Grafana
- **日志收集**: ELK Stack
- **错误追踪**: Sentry

## 2. 核心模块设计

### 2.1 用户管理模块

```typescript
// 用户数据模型
interface IUser {
  id: string;
  email: string;
  username: string;
  passwordHash: string;
  avatar?: string;
  role: 'student' | 'teacher' | 'admin';
  profile: {
    school: string;
    major: string;
    grade: string;
    studentId?: string;
  };
  preferences: {
    theme: 'light' | 'dark';
    language: 'zh' | 'en';
    notifications: boolean;
  };
  subscription: {
    plan: 'free' | 'student' | 'pro';
    expiresAt?: Date;
    features: string[];
  };
  usage: {
    chatCount: number;
    pptCount: number;
    paperCount: number;
    lastResetAt: Date;
  };
  createdAt: Date;
  updatedAt: Date;
}
```

#### 核心功能
- 用户注册/登录/登出
- JWT令牌管理和自动刷新
- 用户信息管理和偏好设置
- 订阅计划和使用量统计
- 权限控制和角色管理

### 2.2 AI对话模块

```typescript
// 对话数据模型
interface IConversation {
  id: string;
  userId: string;
  title: string;
  category: 'general' | 'math' | 'physics' | 'chemistry' | 'programming';
  messages: IMessage[];
  metadata: {
    model: string;
    totalTokens: number;
    lastActiveAt: Date;
  };
  createdAt: Date;
  updatedAt: Date;
}

interface IMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  attachments?: IAttachment[];
  metadata: {
    tokens: number;
    processingTime: number;
    model: string;
  };
  timestamp: Date;
}

interface IAttachment {
  id: string;
  type: 'image' | 'document' | 'audio';
  filename: string;
  url: string;
  size: number;
  mimeType: string;
}
```

#### 核心功能
- 多轮对话管理
- 图像识别和OCR
- 数学公式解析和渲染
- 代码执行和高亮
- 对话历史存储和搜索

### 2.3 PPT生成模块

```typescript
// PPT项目数据模型
interface IPPTProject {
  id: string;
  userId: string;
  title: string;
  description: string;
  template: {
    id: string;
    name: string;
    category: 'academic' | 'business' | 'creative';
    colorScheme: string[];
  };
  slides: ISlide[];
  settings: {
    aspectRatio: '16:9' | '4:3';
    fontSize: number;
    fontFamily: string;
  };
  status: 'draft' | 'generating' | 'completed' | 'error';
  createdAt: Date;
  updatedAt: Date;
}

interface ISlide {
  id: string;
  order: number;
  type: 'title' | 'content' | 'image' | 'chart' | 'code';
  title: string;
  content: any; // 根据类型变化
  layout: string;
  animations: string[];
}
```

#### 核心功能
- 模板库管理
- 文档解析和内容提取
- 幻灯片自动生成
- 单页编辑和批量操作
- 多格式导出（PPTX、PDF、图片）

### 2.4 论文助手模块

```typescript
// 论文项目数据模型
interface IPaperProject {
  id: string;
  userId: string;
  title: string;
  abstract: string;
  type: 'bachelor' | 'master' | 'doctor' | 'research';
  subject: string;
  outline: IChapter[];
  references: IReference[];
  content: {
    format: 'word' | 'latex';
    data: any;
  };
  metadata: {
    wordCount: number;
    pageCount: number;
    citationCount: number;
  };
  status: 'planning' | 'writing' | 'reviewing' | 'completed';
  createdAt: Date;
  updatedAt: Date;
}

interface IChapter {
  id: string;
  level: number;
  title: string;
  content: string;
  children?: IChapter[];
}

interface IReference {
  id: string;
  type: 'article' | 'book' | 'conference' | 'website';
  title: string;
  authors: string[];
  year: number;
  source: string;
  doi?: string;
  url?: string;
  citationStyle: 'APA' | 'MLA' | 'Chicago' | 'GB/T';
}
```

#### 核心功能
- 论文大纲生成
- 文献搜索和管理
- 内容生成和优化
- 引用格式化
- Word和LaTeX格式支持
- 查重检测

### 2.5 作业助手模块

```typescript
// 作业数据模型
interface IHomework {
  id: string;
  userId: string;
  title: string;
  subject: 'math' | 'physics' | 'chemistry' | 'programming';
  problems: IProblem[];
  solutions: ISolution[];
  originalFile?: {
    filename: string;
    url: string;
    type: 'image' | 'pdf' | 'word';
  };
  status: 'uploaded' | 'processing' | 'solved' | 'error';
  createdAt: Date;
  updatedAt: Date;
}

interface IProblem {
  id: string;
  order: number;
  content: string;
  type: 'calculation' | 'proof' | 'analysis' | 'multiple_choice';
  difficulty: 'easy' | 'medium' | 'hard';
  tags: string[];
  images?: string[];
}

interface ISolution {
  id: string;
  problemId: string;
  steps: IStep[];
  answer: string;
  explanation: string;
  confidence: number;
}

interface IStep {
  order: number;
  description: string;
  formula?: string;
  calculation?: string;
  reasoning: string;
}
```

#### 核心功能
- 文件识别和解析
- 题目自动分割
- 知识库匹配
- 解题步骤生成
- 答案验证
- 解答文档生成

## 3. 数据库设计

### 3.1 MongoDB集合设计

#### 用户相关集合
```javascript
// users 集合
{
  _id: ObjectId,
  email: String (unique),
  username: String (unique),
  passwordHash: String,
  profile: Object,
  preferences: Object,
  subscription: Object,
  usage: Object,
  createdAt: Date,
  updatedAt: Date
}

// sessions 集合
{
  _id: ObjectId,
  userId: ObjectId,
  tokenHash: String,
  expiresAt: Date,
  createdAt: Date
}
```

#### 对话相关集合
```javascript
// conversations 集合
{
  _id: ObjectId,
  userId: ObjectId,
  title: String,
  category: String,
  messages: Array,
  metadata: Object,
  createdAt: Date,
  updatedAt: Date
}

// messages 集合（大对话分离存储）
{
  _id: ObjectId,
  conversationId: ObjectId,
  role: String,
  content: String,
  attachments: Array,
  metadata: Object,
  timestamp: Date
}
```

#### 项目相关集合
```javascript
// ppt_projects 集合
{
  _id: ObjectId,
  userId: ObjectId,
  title: String,
  template: Object,
  slides: Array,
  settings: Object,
  status: String,
  createdAt: Date,
  updatedAt: Date
}

// paper_projects 集合
{
  _id: ObjectId,
  userId: ObjectId,
  title: String,
  type: String,
  outline: Array,
  references: Array,
  content: Object,
  metadata: Object,
  status: String,
  createdAt: Date,
  updatedAt: Date
}

// homework 集合
{
  _id: ObjectId,
  userId: ObjectId,
  title: String,
  subject: String,
  problems: Array,
  solutions: Array,
  originalFile: Object,
  status: String,
  createdAt: Date,
  updatedAt: Date
}
```

### 3.2 Redis缓存设计

#### 缓存策略
```javascript
// 用户会话缓存
"session:{tokenHash}" = {
  userId: string,
  expiresAt: timestamp
}

// 对话历史缓存
"conversation:{conversationId}" = {
  // 对话数据
}

// 用户使用量缓存
"usage:{userId}:{date}" = {
  chatCount: number,
  pptCount: number,
  paperCount: number
}

// 热门模板缓存
"templates:popular" = [
  // 模板列表
]

// API响应缓存
"api:{endpoint}:{params_hash}" = {
  // 响应数据
}
```

## 4. API设计

### 4.1 RESTful API规范

#### 基础路径
- 基础URL: `https://api.college-ai.com/v1`
- 认证: Bearer Token (JWT)
- 内容类型: application/json

#### 响应格式
```typescript
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}
```

### 4.2 核心API端点

#### 用户认证
```
POST /auth/register          # 用户注册
POST /auth/login             # 用户登录
POST /auth/logout            # 用户登出
POST /auth/refresh           # 刷新令牌
GET  /auth/me                # 获取用户信息
PUT  /auth/profile           # 更新用户信息
```

#### 对话管理
```
GET    /conversations        # 获取对话列表
POST   /conversations        # 创建新对话
GET    /conversations/:id    # 获取对话详情
PUT    /conversations/:id    # 更新对话信息
DELETE /conversations/:id    # 删除对话
POST   /conversations/:id/messages  # 发送消息
```

#### PPT生成
```
GET    /ppt/templates        # 获取模板列表
GET    /ppt/projects         # 获取项目列表
POST   /ppt/projects         # 创建新项目
GET    /ppt/projects/:id     # 获取项目详情
PUT    /ppt/projects/:id     # 更新项目
DELETE /ppt/projects/:id     # 删除项目
POST   /ppt/projects/:id/generate  # 生成PPT
POST   /ppt/projects/:id/export    # 导出PPT
```

#### 论文助手
```
GET    /papers               # 获取论文列表
POST   /papers               # 创建新论文
GET    /papers/:id           # 获取论文详情
PUT    /papers/:id           # 更新论文
DELETE /papers/:id           # 删除论文
POST   /papers/:id/generate  # 生成内容
POST   /papers/references/search  # 搜索文献
```

#### 作业助手
```
GET    /homework             # 获取作业列表
POST   /homework             # 上传作业
GET    /homework/:id         # 获取作业详情
POST   /homework/:id/solve   # 解答作业
POST   /homework/:id/export  # 导出解答
```

### 4.3 WebSocket API

#### 实时通信
```javascript
// 连接认证
socket.emit('auth', { token: 'jwt_token' });

// 对话消息
socket.emit('chat:message', { 
  conversationId: 'id',
  content: 'message'
});

socket.on('chat:response', { 
  messageId: 'id',
  content: 'response'
});

// 生成进度
socket.on('generation:progress', {
  projectId: 'id',
  progress: 45,
  status: 'generating'
});
```

## 5. 安全设计

### 5.1 认证授权
- JWT令牌认证，支持令牌刷新
- 基于角色的权限控制(RBAC)
- API访问频率限制
- 跨域资源共享(CORS)配置

### 5.2 数据安全
- 密码哈希存储(bcrypt)
- 敏感数据加密传输(HTTPS)
- 用户上传文件病毒扫描
- SQL注入和XSS防护

### 5.3 隐私保护
- 用户数据最小化收集
- 数据匿名化处理
- GDPR合规性
- 数据删除和导出功能

## 6. 性能优化

### 6.1 前端优化
- 代码分割和懒加载
- 组件级缓存和记忆化
- 虚拟滚动处理大列表
- CDN静态资源加速

### 6.2 后端优化
- 数据库索引优化
- Redis缓存策略
- API响应压缩
- 连接池管理

### 6.3 系统优化
- 负载均衡配置
- 数据库读写分离
- 微服务架构演进
- 监控和自动扩缩容

## 7. 监控运维

### 7.1 系统监控
- 服务器资源监控(CPU、内存、磁盘)
- 应用性能监控(响应时间、错误率)
- 数据库性能监控
- 网络流量监控

### 7.2 日志管理
- 结构化日志记录
- 日志聚合和分析
- 错误日志告警
- 审计日志跟踪

### 7.3 备份恢复
- 数据库定期备份
- 增量备份策略
- 灾难恢复预案
- 数据一致性检查

## 8. 部署架构

### 8.1 开发环境
```yaml
# docker-compose.dev.yml
version: '3.8'
services:
  frontend:
    build: ./frontend
    ports: ["3000:3000"]
    volumes: ["./frontend:/app"]
  
  backend:
    build: ./backend
    ports: ["3001:3001"]
    volumes: ["./backend:/app"]
    
  mongodb:
    image: mongo:7.0
    ports: ["27017:27017"]
    
  redis:
    image: redis:alpine
    ports: ["6379:6379"]
```

### 8.2 生产环境
```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  nginx:
    image: nginx:alpine
    ports: ["80:80", "443:443"]
    
  frontend:
    build: 
      context: ./frontend
      dockerfile: Dockerfile.prod
      
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    deploy:
      replicas: 3
      
  mongodb:
    image: mongo:7.0
    deploy:
      replicas: 3
      
  redis:
    image: redis:alpine
    deploy:
      replicas: 2
```

这个技术架构设计文档为高校AI助手项目提供了完整的技术指导，涵盖了从前端到后端、从开发到部署的各个方面。架构设计注重可扩展性、安全性和性能，为项目的成功实施奠定了坚实的技术基础。 