# 数据字典

## 文档信息
- **文档版本**: v1.0
- **创建日期**: 2025-01-15
- **更新日期**: 2025-01-15
- **维护人**: 项目组
- **审核人**: 技术架构师

## 概述

本数据字典定义了高校AI产品系统的所有数据表结构、字段约束、业务规则和关系映射。系统采用PostgreSQL作为主数据库，Redis作为缓存层，Qdrant作为向量数据库。

## 数据库设计原则

### 1. 命名规范
- **表名**: 小写字母+下划线，复数形式
- **字段名**: 小写字母+下划线，语义明确
- **索引名**: idx_表名_字段名
- **外键名**: fk_表名_字段名

### 2. 数据类型规范
- **主键**: bigint，自增
- **字符串**: varchar(n)，明确长度限制
- **文本**: text，大容量文本
- **时间**: timestamp with time zone
- **布尔**: boolean
- **JSON**: jsonb，提升查询性能

### 3. 约束规范
- **必填字段**: NOT NULL
- **唯一约束**: UNIQUE
- **外键约束**: FOREIGN KEY
- **检查约束**: CHECK
- **默认值**: DEFAULT

## 核心数据表

### 1. 用户管理模块

#### 1.1 users (用户表)

**表描述**: 存储系统用户的基本信息和认证数据

| 字段名 | 数据类型 | 长度 | 是否必填 | 默认值 | 约束条件 | 业务说明 |
|--------|----------|------|----------|--------|----------|----------|
| id | bigint | - | 是 | 自增 | PRIMARY KEY | 用户主键ID |
| user_id | varchar | 32 | 是 | UUID | UNIQUE | 用户唯一标识 |
| username | varchar | 50 | 是 | - | UNIQUE | 用户名，3-50字符 |
| email | varchar | 100 | 是 | - | UNIQUE | 邮箱地址 |
| password_hash | varchar | 255 | 是 | - | - | 密码哈希值 |
| avatar_url | varchar | 500 | 否 | NULL | - | 头像URL地址 |
| school_name | varchar | 100 | 否 | NULL | - | 所在学校名称 |
| major | varchar | 50 | 否 | NULL | - | 专业 |
| grade | varchar | 20 | 否 | NULL | - | 年级 |
| phone | varchar | 20 | 否 | NULL | - | 手机号码 |
| status | integer | - | 是 | 1 | CHECK (status IN (0,1,2)) | 账户状态：0禁用，1正常，2待激活 |
| created_at | timestamp | - | 是 | CURRENT_TIMESTAMP | - | 创建时间 |
| updated_at | timestamp | - | 是 | CURRENT_TIMESTAMP | - | 更新时间 |
| last_login_at | timestamp | - | 否 | NULL | - | 最后登录时间 |

**索引设置**:
- `PRIMARY KEY (id)`
- `UNIQUE INDEX idx_users_user_id (user_id)`
- `UNIQUE INDEX idx_users_username (username)`
- `UNIQUE INDEX idx_users_email (email)`
- `INDEX idx_users_status (status)`
- `INDEX idx_users_created_at (created_at)`

#### 1.2 user_sessions (用户会话表)

**表描述**: 存储用户登录会话信息，支持多设备登录

| 字段名 | 数据类型 | 长度 | 是否必填 | 默认值 | 约束条件 | 业务说明 |
|--------|----------|------|----------|--------|----------|----------|
| id | bigint | - | 是 | 自增 | PRIMARY KEY | 会话主键ID |
| user_id | bigint | - | 是 | - | FOREIGN KEY | 用户ID |
| session_token | varchar | 255 | 是 | - | UNIQUE | 会话令牌 |
| device_info | varchar | 500 | 否 | NULL | - | 设备信息 |
| ip_address | inet | - | 否 | NULL | - | IP地址 |
| expires_at | timestamp | - | 是 | - | - | 过期时间 |
| created_at | timestamp | - | 是 | CURRENT_TIMESTAMP | - | 创建时间 |
| last_activity_at | timestamp | - | 是 | CURRENT_TIMESTAMP | - | 最后活动时间 |

**索引设置**:
- `PRIMARY KEY (id)`
- `UNIQUE INDEX idx_sessions_token (session_token)`
- `INDEX idx_sessions_user_id (user_id)`
- `INDEX idx_sessions_expires_at (expires_at)`

### 2. AI对话模块

#### 2.1 ai_conversations (AI对话表)

**表描述**: 存储AI对话的基本信息和元数据

| 字段名 | 数据类型 | 长度 | 是否必填 | 默认值 | 约束条件 | 业务说明 |
|--------|----------|------|----------|--------|----------|----------|
| id | bigint | - | 是 | 自增 | PRIMARY KEY | 对话主键ID |
| user_id | bigint | - | 是 | - | FOREIGN KEY | 用户ID |
| conversation_id | varchar | 32 | 是 | UUID | UNIQUE | 对话唯一标识 |
| title | varchar | 200 | 是 | - | - | 对话标题 |
| type | varchar | 50 | 是 | 'general' | CHECK | 对话类型：general/academic/creative |
| metadata | jsonb | - | 否 | '{}' | - | 对话元数据 |
| message_count | integer | - | 是 | 0 | CHECK (message_count >= 0) | 消息数量 |
| created_at | timestamp | - | 是 | CURRENT_TIMESTAMP | - | 创建时间 |
| updated_at | timestamp | - | 是 | CURRENT_TIMESTAMP | - | 更新时间 |

**索引设置**:
- `PRIMARY KEY (id)`
- `UNIQUE INDEX idx_conversations_conversation_id (conversation_id)`
- `INDEX idx_conversations_user_id (user_id)`
- `INDEX idx_conversations_type (type)`
- `INDEX idx_conversations_created_at (created_at)`

#### 2.2 conversation_messages (对话消息表)

**表描述**: 存储对话中的具体消息内容

| 字段名 | 数据类型 | 长度 | 是否必填 | 默认值 | 约束条件 | 业务说明 |
|--------|----------|------|----------|--------|----------|----------|
| id | bigint | - | 是 | 自增 | PRIMARY KEY | 消息主键ID |
| conversation_id | bigint | - | 是 | - | FOREIGN KEY | 对话ID |
| role | varchar | 20 | 是 | - | CHECK (role IN ('user','assistant','system')) | 消息角色 |
| content | text | - | 是 | - | - | 消息内容 |
| attachments | jsonb | - | 否 | '[]' | - | 附件信息 |
| tokens_used | integer | - | 否 | 0 | CHECK (tokens_used >= 0) | 使用的Token数 |
| created_at | timestamp | - | 是 | CURRENT_TIMESTAMP | - | 创建时间 |

**索引设置**:
- `PRIMARY KEY (id)`
- `INDEX idx_messages_conversation_id (conversation_id)`
- `INDEX idx_messages_role (role)`
- `INDEX idx_messages_created_at (created_at)`

#### 2.3 conversation_templates (对话模板表)

**表描述**: 存储预定义的对话模板

| 字段名 | 数据类型 | 长度 | 是否必填 | 默认值 | 约束条件 | 业务说明 |
|--------|----------|------|----------|--------|----------|----------|
| id | bigint | - | 是 | 自增 | PRIMARY KEY | 模板主键ID |
| name | varchar | 100 | 是 | - | - | 模板名称 |
| description | text | - | 否 | NULL | - | 模板描述 |
| category | varchar | 50 | 是 | - | - | 模板分类 |
| template_data | jsonb | - | 是 | - | - | 模板数据 |
| is_active | boolean | - | 是 | true | - | 是否激活 |
| created_at | timestamp | - | 是 | CURRENT_TIMESTAMP | - | 创建时间 |
| updated_at | timestamp | - | 是 | CURRENT_TIMESTAMP | - | 更新时间 |

### 3. PPT生成模块

#### 3.1 ppt_projects (PPT项目表)

**表描述**: 存储PPT项目的基本信息

| 字段名 | 数据类型 | 长度 | 是否必填 | 默认值 | 约束条件 | 业务说明 |
|--------|----------|------|----------|--------|----------|----------|
| id | bigint | - | 是 | 自增 | PRIMARY KEY | PPT项目主键ID |
| user_id | bigint | - | 是 | - | FOREIGN KEY | 用户ID |
| project_id | varchar | 32 | 是 | UUID | UNIQUE | 项目唯一标识 |
| title | varchar | 200 | 是 | - | - | 项目标题 |
| theme | varchar | 50 | 是 | 'default' | - | 主题模板 |
| settings | jsonb | - | 否 | '{}' | - | 项目设置 |
| slide_count | integer | - | 是 | 0 | CHECK (slide_count >= 0) | 幻灯片数量 |
| status | integer | - | 是 | 0 | CHECK (status IN (0,1,2,3)) | 项目状态：0草稿，1生成中，2已完成，3已发布 |
| created_at | timestamp | - | 是 | CURRENT_TIMESTAMP | - | 创建时间 |
| updated_at | timestamp | - | 是 | CURRENT_TIMESTAMP | - | 更新时间 |

#### 3.2 ppt_slides (PPT幻灯片表)

**表描述**: 存储PPT中每一页幻灯片的信息

| 字段名 | 数据类型 | 长度 | 是否必填 | 默认值 | 约束条件 | 业务说明 |
|--------|----------|------|----------|--------|----------|----------|
| id | bigint | - | 是 | 自增 | PRIMARY KEY | 幻灯片主键ID |
| project_id | bigint | - | 是 | - | FOREIGN KEY | 项目ID |
| slide_index | integer | - | 是 | - | CHECK (slide_index >= 0) | 幻灯片索引 |
| slide_type | varchar | 50 | 是 | 'content' | - | 幻灯片类型 |
| title | varchar | 200 | 否 | NULL | - | 幻灯片标题 |
| layout_data | jsonb | - | 否 | '{}' | - | 布局数据 |
| created_at | timestamp | - | 是 | CURRENT_TIMESTAMP | - | 创建时间 |
| updated_at | timestamp | - | 是 | CURRENT_TIMESTAMP | - | 更新时间 |

#### 3.3 slide_elements (幻灯片元素表)

**表描述**: 存储幻灯片中的各种元素（文本、图片、图表等）

| 字段名 | 数据类型 | 长度 | 是否必填 | 默认值 | 约束条件 | 业务说明 |
|--------|----------|------|----------|--------|----------|----------|
| id | bigint | - | 是 | 自增 | PRIMARY KEY | 元素主键ID |
| slide_id | bigint | - | 是 | - | FOREIGN KEY | 幻灯片ID |
| element_type | varchar | 50 | 是 | - | CHECK | 元素类型：text/image/chart/shape |
| element_data | jsonb | - | 是 | - | - | 元素数据 |
| position_data | jsonb | - | 是 | - | - | 位置数据 |
| style_data | jsonb | - | 否 | '{}' | - | 样式数据 |
| z_index | integer | - | 是 | 0 | - | 层级顺序 |
| created_at | timestamp | - | 是 | CURRENT_TIMESTAMP | - | 创建时间 |
| updated_at | timestamp | - | 是 | CURRENT_TIMESTAMP | - | 更新时间 |

### 4. 论文助手模块

#### 4.1 paper_projects (论文项目表)

**表描述**: 存储论文写作项目的基本信息

| 字段名 | 数据类型 | 长度 | 是否必填 | 默认值 | 约束条件 | 业务说明 |
|--------|----------|------|----------|--------|----------|----------|
| id | bigint | - | 是 | 自增 | PRIMARY KEY | 论文项目主键ID |
| user_id | bigint | - | 是 | - | FOREIGN KEY | 用户ID |
| project_id | varchar | 32 | 是 | UUID | UNIQUE | 项目唯一标识 |
| title | varchar | 500 | 是 | - | - | 论文标题 |
| subject | varchar | 100 | 是 | - | - | 学科领域 |
| paper_type | varchar | 50 | 是 | - | CHECK | 论文类型：thesis/dissertation/journal/conference |
| abstract | text | - | 否 | NULL | - | 摘要 |
| outline | jsonb | - | 否 | '{}' | - | 大纲结构 |
| word_count | integer | - | 是 | 0 | CHECK (word_count >= 0) | 字数统计 |
| status | integer | - | 是 | 0 | CHECK (status IN (0,1,2,3)) | 项目状态：0草稿，1写作中，2审稿中，3已完成 |
| created_at | timestamp | - | 是 | CURRENT_TIMESTAMP | - | 创建时间 |
| updated_at | timestamp | - | 是 | CURRENT_TIMESTAMP | - | 更新时间 |

#### 4.2 paper_sections (论文章节表)

**表描述**: 存储论文的各个章节内容

| 字段名 | 数据类型 | 长度 | 是否必填 | 默认值 | 约束条件 | 业务说明 |
|--------|----------|------|----------|--------|----------|----------|
| id | bigint | - | 是 | 自增 | PRIMARY KEY | 论文章节主键ID |
| project_id | bigint | - | 是 | - | FOREIGN KEY | 项目ID |
| section_type | varchar | 50 | 是 | - | CHECK | 章节类型：abstract/introduction/literature/methodology/results/discussion/conclusion |
| title | varchar | 200 | 是 | - | - | 章节标题 |
| content | text | - | 否 | NULL | - | 章节内容 |
| section_order | integer | - | 是 | - | CHECK (section_order > 0) | 章节顺序 |
| word_count | integer | - | 是 | 0 | CHECK (word_count >= 0) | 字数统计 |
| created_at | timestamp | - | 是 | CURRENT_TIMESTAMP | - | 创建时间 |
| updated_at | timestamp | - | 是 | CURRENT_TIMESTAMP | - | 更新时间 |

#### 4.3 reference_sources (参考文献表)

**表描述**: 存储论文的参考文献信息

| 字段名 | 数据类型 | 长度 | 是否必填 | 默认值 | 约束条件 | 业务说明 |
|--------|----------|------|----------|--------|----------|----------|
| id | bigint | - | 是 | 自增 | PRIMARY KEY | 参考文献主键ID |
| project_id | bigint | - | 是 | - | FOREIGN KEY | 项目ID |
| citation_style | varchar | 20 | 是 | 'APA' | CHECK | 引用格式：APA/MLA/Chicago/IEEE |
| source_type | varchar | 50 | 是 | - | CHECK | 文献类型：journal/book/conference/website/thesis |
| source_data | jsonb | - | 是 | - | - | 文献数据（作者、标题、出版信息等） |
| doi | varchar | 100 | 否 | NULL | - | DOI标识 |
| created_at | timestamp | - | 是 | CURRENT_TIMESTAMP | - | 创建时间 |

### 5. 作业助手模块

#### 5.1 homework_projects (作业项目表)

**表描述**: 存储作业项目的基本信息

| 字段名 | 数据类型 | 长度 | 是否必填 | 默认值 | 约束条件 | 业务说明 |
|--------|----------|------|----------|--------|----------|----------|
| id | bigint | - | 是 | 自增 | PRIMARY KEY | 作业项目主键ID |
| user_id | bigint | - | 是 | - | FOREIGN KEY | 用户ID |
| project_id | varchar | 32 | 是 | UUID | UNIQUE | 项目唯一标识 |
| subject | varchar | 50 | 是 | - | - | 学科 |
| homework_type | varchar | 50 | 是 | - | CHECK | 作业类型：math/physics/chemistry/programming/essay |
| title | varchar | 200 | 是 | - | - | 作业标题 |
| description | text | - | 否 | NULL | - | 作业描述 |
| difficulty_level | integer | - | 是 | 1 | CHECK (difficulty_level BETWEEN 1 AND 5) | 难度等级1-5 |
| total_questions | integer | - | 是 | 0 | CHECK (total_questions >= 0) | 题目总数 |
| completed_questions | integer | - | 是 | 0 | CHECK (completed_questions >= 0) | 已完成题目数 |
| status | integer | - | 是 | 0 | CHECK (status IN (0,1,2)) | 项目状态：0进行中，1已完成，2已提交 |
| created_at | timestamp | - | 是 | CURRENT_TIMESTAMP | - | 创建时间 |
| updated_at | timestamp | - | 是 | CURRENT_TIMESTAMP | - | 更新时间 |

#### 5.2 homework_questions (作业题目表)

**表描述**: 存储作业中的具体题目

| 字段名 | 数据类型 | 长度 | 是否必填 | 默认值 | 约束条件 | 业务说明 |
|--------|----------|------|----------|--------|----------|----------|
| id | bigint | - | 是 | 自增 | PRIMARY KEY | 题目主键ID |
| project_id | bigint | - | 是 | - | FOREIGN KEY | 项目ID |
| question_type | varchar | 50 | 是 | - | CHECK | 题目类型：multiple_choice/fill_blank/essay/calculation/coding |
| question_content | text | - | 是 | - | - | 题目内容 |
| question_data | jsonb | - | 否 | '{}' | - | 题目数据（选项、图片等） |
| answer | text | - | 否 | NULL | - | 标准答案 |
| explanation | text | - | 否 | NULL | - | 解答解释 |
| difficulty | integer | - | 是 | 1 | CHECK (difficulty BETWEEN 1 AND 5) | 难度系数1-5 |
| created_at | timestamp | - | 是 | CURRENT_TIMESTAMP | - | 创建时间 |

#### 5.3 question_solutions (题目解答表)

**表描述**: 存储用户的题目解答和AI分析

| 字段名 | 数据类型 | 长度 | 是否必填 | 默认值 | 约束条件 | 业务说明 |
|--------|----------|------|----------|--------|----------|----------|
| id | bigint | - | 是 | 自增 | PRIMARY KEY | 解答主键ID |
| question_id | bigint | - | 是 | - | FOREIGN KEY | 题目ID |
| user_answer | text | - | 否 | NULL | - | 用户答案 |
| ai_solution | text | - | 否 | NULL | - | AI解答 |
| step_by_step | text | - | 否 | NULL | - | 分步解答 |
| analysis_data | jsonb | - | 否 | '{}' | - | 分析数据 |
| correctness_score | integer | - | 否 | NULL | CHECK (correctness_score BETWEEN 0 AND 100) | 正确性评分0-100 |
| created_at | timestamp | - | 是 | CURRENT_TIMESTAMP | - | 创建时间 |

### 6. AI痕迹消除模块

#### 6.1 trace_removal_records (痕迹消除记录表)

**表描述**: 存储AI痕迹消除的处理记录

| 字段名 | 数据类型 | 长度 | 是否必填 | 默认值 | 约束条件 | 业务说明 |
|--------|----------|------|----------|--------|----------|----------|
| id | bigint | - | 是 | 自增 | PRIMARY KEY | 痕迹消除记录主键ID |
| user_id | bigint | - | 是 | - | FOREIGN KEY | 用户ID |
| record_id | varchar | 32 | 是 | UUID | UNIQUE | 记录唯一标识 |
| content_type | varchar | 50 | 是 | - | CHECK | 内容类型：text/document/code/presentation |
| original_content | text | - | 是 | - | - | 原始内容 |
| processed_content | text | - | 否 | NULL | - | 处理后内容 |
| removal_config | jsonb | - | 否 | '{}' | - | 消除配置 |
| similarity_score | decimal | 5,2 | 否 | NULL | CHECK (similarity_score BETWEEN 0 AND 100) | 相似度评分 |
| status | integer | - | 是 | 0 | CHECK (status IN (0,1,2,3)) | 处理状态：0排队中，1处理中，2已完成，3处理失败 |
| created_at | timestamp | - | 是 | CURRENT_TIMESTAMP | - | 创建时间 |
| completed_at | timestamp | - | 否 | NULL | - | 完成时间 |

#### 6.2 trace_operations (痕迹操作表)

**表描述**: 存储痕迹消除的具体操作步骤

| 字段名 | 数据类型 | 长度 | 是否必填 | 默认值 | 约束条件 | 业务说明 |
|--------|----------|------|----------|--------|----------|----------|
| id | bigint | - | 是 | 自增 | PRIMARY KEY | 操作主键ID |
| record_id | bigint | - | 是 | - | FOREIGN KEY | 记录ID |
| operation_type | varchar | 50 | 是 | - | CHECK | 操作类型：paraphrase/structure_change/style_change/word_replace |
| operation_params | jsonb | - | 否 | '{}' | - | 操作参数 |
| result_data | text | - | 否 | NULL | - | 操作结果 |
| created_at | timestamp | - | 是 | CURRENT_TIMESTAMP | - | 创建时间 |

### 7. 订阅管理模块

#### 7.1 subscription_records (订阅记录表)

**表描述**: 存储用户的订阅信息和支付记录

| 字段名 | 数据类型 | 长度 | 是否必填 | 默认值 | 约束条件 | 业务说明 |
|--------|----------|------|----------|--------|----------|----------|
| id | bigint | - | 是 | 自增 | PRIMARY KEY | 订阅记录主键ID |
| user_id | bigint | - | 是 | - | FOREIGN KEY | 用户ID |
| plan_type | varchar | 50 | 是 | - | CHECK | 订阅类型：free/basic/premium/enterprise |
| price | decimal | 10,2 | 是 | 0.00 | CHECK (price >= 0) | 价格 |
| payment_method | varchar | 50 | 否 | NULL | CHECK | 支付方式：alipay/wechat/credit_card/bank_transfer |
| transaction_id | varchar | 100 | 否 | NULL | - | 交易ID |
| start_date | timestamp | - | 是 | - | - | 开始日期 |
| end_date | timestamp | - | 是 | - | - | 结束日期 |
| status | integer | - | 是 | 1 | CHECK (status IN (0,1,2,3)) | 订阅状态：0已取消，1有效，2已过期，3暂停 |
| features | jsonb | - | 否 | '{}' | - | 功能权限 |
| created_at | timestamp | - | 是 | CURRENT_TIMESTAMP | - | 创建时间 |
| updated_at | timestamp | - | 是 | CURRENT_TIMESTAMP | - | 更新时间 |

## 数据库约束和触发器

### 1. 外键约束

```sql
-- 用户会话表
ALTER TABLE user_sessions 
ADD CONSTRAINT fk_sessions_user_id 
FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

-- AI对话表
ALTER TABLE ai_conversations 
ADD CONSTRAINT fk_conversations_user_id 
FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

-- 对话消息表
ALTER TABLE conversation_messages 
ADD CONSTRAINT fk_messages_conversation_id 
FOREIGN KEY (conversation_id) REFERENCES ai_conversations(id) ON DELETE CASCADE;

-- PPT项目表
ALTER TABLE ppt_projects 
ADD CONSTRAINT fk_ppt_projects_user_id 
FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

-- PPT幻灯片表
ALTER TABLE ppt_slides 
ADD CONSTRAINT fk_slides_project_id 
FOREIGN KEY (project_id) REFERENCES ppt_projects(id) ON DELETE CASCADE;

-- 幻灯片元素表
ALTER TABLE slide_elements 
ADD CONSTRAINT fk_elements_slide_id 
FOREIGN KEY (slide_id) REFERENCES ppt_slides(id) ON DELETE CASCADE;

-- 论文项目表
ALTER TABLE paper_projects 
ADD CONSTRAINT fk_paper_projects_user_id 
FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

-- 论文章节表
ALTER TABLE paper_sections 
ADD CONSTRAINT fk_sections_project_id 
FOREIGN KEY (project_id) REFERENCES paper_projects(id) ON DELETE CASCADE;

-- 参考文献表
ALTER TABLE reference_sources 
ADD CONSTRAINT fk_references_project_id 
FOREIGN KEY (project_id) REFERENCES paper_projects(id) ON DELETE CASCADE;

-- 作业项目表
ALTER TABLE homework_projects 
ADD CONSTRAINT fk_homework_projects_user_id 
FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

-- 作业题目表
ALTER TABLE homework_questions 
ADD CONSTRAINT fk_questions_project_id 
FOREIGN KEY (project_id) REFERENCES homework_projects(id) ON DELETE CASCADE;

-- 题目解答表
ALTER TABLE question_solutions 
ADD CONSTRAINT fk_solutions_question_id 
FOREIGN KEY (question_id) REFERENCES homework_questions(id) ON DELETE CASCADE;

-- 痕迹消除记录表
ALTER TABLE trace_removal_records 
ADD CONSTRAINT fk_trace_records_user_id 
FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

-- 痕迹操作表
ALTER TABLE trace_operations 
ADD CONSTRAINT fk_operations_record_id 
FOREIGN KEY (record_id) REFERENCES trace_removal_records(id) ON DELETE CASCADE;

-- 订阅记录表
ALTER TABLE subscription_records 
ADD CONSTRAINT fk_subscriptions_user_id 
FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
```

### 2. 触发器

```sql
-- 自动更新 updated_at 字段的触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为相关表创建触发器
CREATE TRIGGER update_users_updated_at 
BEFORE UPDATE ON users 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_conversations_updated_at 
BEFORE UPDATE ON ai_conversations 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ppt_projects_updated_at 
BEFORE UPDATE ON ppt_projects 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 自动更新消息数量的触发器
CREATE OR REPLACE FUNCTION update_message_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE ai_conversations 
        SET message_count = message_count + 1 
        WHERE id = NEW.conversation_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE ai_conversations 
        SET message_count = message_count - 1 
        WHERE id = OLD.conversation_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_conversation_message_count
AFTER INSERT OR DELETE ON conversation_messages
FOR EACH ROW EXECUTE FUNCTION update_message_count();
```

## 缓存策略 (Redis)

### 1. 用户会话缓存

```
键格式: session:{session_token}
值类型: Hash
TTL: 2592000 (30天)
内容: {
  "user_id": "123",
  "username": "student1",
  "email": "<EMAIL>",
  "last_activity": "2025-01-15T10:30:00Z"
}
```

### 2. 用户基本信息缓存

```
键格式: user:{user_id}
值类型: Hash
TTL: 3600 (1小时)
内容: {
  "username": "student1",
  "email": "<EMAIL>",
  "avatar_url": "https://...",
  "school_name": "清华大学",
  "major": "计算机科学"
}
```

### 3. 对话历史缓存

```
键格式: conversation:{conversation_id}:messages
值类型: List
TTL: 7200 (2小时)
内容: [
  {"role": "user", "content": "问题1", "timestamp": "..."},
  {"role": "assistant", "content": "回答1", "timestamp": "..."}
]
```

### 4. 项目进度缓存

```
键格式: project:{project_type}:{project_id}:progress
值类型: Hash
TTL: 1800 (30分钟)
内容: {
  "status": "1",
  "progress": "75",
  "last_updated": "2025-01-15T10:30:00Z"
}
```

## 向量数据库 (Qdrant)

### 1. 对话向量存储

```json
{
  "collection_name": "conversation_embeddings",
  "vector_size": 1536,
  "distance": "Cosine",
  "payload_schema": {
    "conversation_id": "string",
    "user_id": "integer",
    "message_type": "string",
    "content": "string",
    "timestamp": "string",
    "tokens": "integer"
  }
}
```

### 2. 知识库向量存储

```json
{
  "collection_name": "knowledge_base",
  "vector_size": 1536,
  "distance": "Cosine",
  "payload_schema": {
    "document_id": "string",
    "subject": "string",
    "content_type": "string",
    "content": "string",
    "source": "string",
    "confidence_score": "float"
  }
}
```

## 数据备份策略

### 1. 全量备份
- **频率**: 每天凌晨2点
- **保留期**: 30天
- **存储位置**: 云存储（阿里云OSS/AWS S3）

### 2. 增量备份
- **频率**: 每4小时
- **保留期**: 7天
- **存储位置**: 本地存储 + 云存储

### 3. 事务日志备份
- **频率**: 每15分钟
- **保留期**: 24小时
- **存储位置**: 本地存储

## 性能优化建议

### 1. 索引优化
- 为经常查询的字段创建索引
- 使用复合索引优化复杂查询
- 定期分析和重建索引

### 2. 分区策略
- 按时间分区：对话消息、操作日志
- 按用户分区：大表可考虑用户ID分区

### 3. 查询优化
- 使用EXPLAIN分析查询计划
- 避免全表扫描
- 合理使用JOIN和子查询

### 4. 缓存策略
- 热点数据缓存到Redis
- 设置合理的TTL时间
- 实现缓存预热和更新机制

## 数据安全和隐私

### 1. 敏感数据加密
- 密码使用bcrypt哈希
- 个人敏感信息使用AES加密
- 通信使用HTTPS/TLS

### 2. 访问控制
- 基于角色的权限控制
- API访问令牌管理
- 审计日志记录

### 3. 数据脱敏
- 测试环境数据脱敏
- 日志中敏感信息过滤
- 数据导出权限控制

### 4. 合规要求
- 遵循数据保护法规
- 用户数据删除机制
- 数据使用授权管理

## 监控和维护

### 1. 性能监控
- 数据库连接池监控
- 慢查询日志分析
- 磁盘空间使用监控

### 2. 数据质量监控
- 数据完整性检查
- 异常数据告警
- 数据一致性验证

### 3. 定期维护
- 统计信息更新
- 碎片整理
- 过期数据清理

这个数据字典提供了完整的数据表结构定义，包括字段约束、索引设置、业务规则等，为系统开发和维护提供了详细的参考文档。 