# 测试规范文档

## 文档信息
- **版本**: v1.0
- **创建日期**: 2025-01-15
- **更新日期**: 2025-01-15
- **维护人**: 技术团队

## 1. 测试策略概览

### 1.1 测试金字塔

我们采用经典的测试金字塔策略，确保高效的质量保证：

```
       ╔═══════════════════╗
      ╔╝     E2E Tests      ╚╗  <- 5-10%，覆盖核心用户流程
     ╔╝                     ╚╗
    ╔╝   Integration Tests   ╚╗ <- 15-25%，测试模块间交互
   ╔╝                        ╚╗
  ╔╝       Unit Tests         ╚╗ <- 70-80%，快速反馈
 ╔╝                           ╚╗
╔╝                             ╚╗
╚═══════════════════════════════╝
```

### 1.2 测试原则

1. **自动化**: 所有测试无需人工干预，完全自动化执行
2. **独立性**: 每个测试独立运行，不依赖其他测试
3. **可重复**: 同一测试多次运行结果一致
4. **快速反馈**: 单元测试执行时间 < 10s，集成测试 < 5min
5. **覆盖率**: 代码覆盖率 ≥ 80%，关键路径覆盖率 100%

## 2. 单元测试 (Unit Tests)

### 2.1 测试工具配置

**主要工具**:
- **测试框架**: Vitest
- **测试库**: Testing Library (React)
- **断言库**: Vitest 内置
- **Mock库**: Vitest Mock + MSW

**配置文件** (`frontend/vitest.config.ts`):
```typescript
import { defineConfig } from 'vitest/config'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'

export default defineConfig({
  plugins: [react()],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
    css: true,
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'src/test/',
        '**/*.d.ts',
        '**/*.config.*',
        '**/dist/**'
      ],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        }
      }
    }
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
      '@/components': resolve(__dirname, './src/components'),
      '@/hooks': resolve(__dirname, './src/hooks'),
      '@/utils': resolve(__dirname, './src/utils'),
      '@/types': resolve(__dirname, './src/types')
    }
  }
})
```

### 2.2 测试文件结构

```
src/
├── components/
│   ├── ChatMessage/
│   │   ├── index.tsx
│   │   ├── ChatMessage.tsx
│   │   └── __tests__/
│   │       ├── ChatMessage.test.tsx
│   │       └── ChatMessage.integration.test.tsx
│   └── Button/
│       ├── index.tsx
│       ├── Button.tsx
│       └── __tests__/
│           └── Button.test.tsx
├── hooks/
│   ├── useChat.ts
│   └── __tests__/
│       └── useChat.test.ts
├── utils/
│   ├── formatters.ts
│   └── __tests__/
│       └── formatters.test.ts
└── test/
    ├── setup.ts
    ├── utils.tsx
    └── mocks/
        ├── handlers.ts
        └── server.ts
```

### 2.3 组件测试示例

```typescript
// src/components/ChatMessage/__tests__/ChatMessage.test.tsx
import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { describe, it, expect, vi } from 'vitest'
import ChatMessage from '../ChatMessage'

describe('ChatMessage', () => {
  const mockMessage = {
    id: '1',
    role: 'user' as const,
    content: 'Hello, AI!',
    timestamp: new Date('2024-01-01T10:00:00Z'),
    tokens: 10
  }

  it('renders user message correctly', () => {
    render(<ChatMessage message={mockMessage} />)
    
    expect(screen.getByText('Hello, AI!')).toBeInTheDocument()
    expect(screen.getByTestId('message-user')).toBeInTheDocument()
  })

  it('handles copy message functionality', async () => {
    const user = userEvent.setup()
    const mockCopy = vi.fn()
    
    // Mock clipboard API
    Object.assign(navigator, {
      clipboard: {
        writeText: mockCopy
      }
    })

    render(<ChatMessage message={mockMessage} />)
    
    const copyButton = screen.getByRole('button', { name: /复制/i })
    await user.click(copyButton)
    
    expect(mockCopy).toHaveBeenCalledWith('Hello, AI!')
  })

  it('displays correct timestamp', () => {
    render(<ChatMessage message={mockMessage} />)
    
    expect(screen.getByText('10:00')).toBeInTheDocument()
  })
})
```

### 2.4 Hook测试示例

```typescript
// src/hooks/__tests__/useChat.test.ts
import { renderHook, act } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { useChat } from '../useChat'

describe('useChat', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('initializes with empty messages', () => {
    const { result } = renderHook(() => useChat())
    
    expect(result.current.messages).toEqual([])
    expect(result.current.isLoading).toBe(false)
  })

  it('sends message successfully', async () => {
    const { result } = renderHook(() => useChat())
    
    await act(async () => {
      await result.current.sendMessage('Hello')
    })
    
    expect(result.current.messages).toHaveLength(2) // user + ai
    expect(result.current.messages[0].content).toBe('Hello')
    expect(result.current.messages[0].role).toBe('user')
  })

  it('handles API errors gracefully', async () => {
    // Mock API failure
    vi.mocked(fetch).mockRejectedValueOnce(new Error('API Error'))
    
    const { result } = renderHook(() => useChat())
    
    await act(async () => {
      await result.current.sendMessage('Hello')
    })
    
    expect(result.current.error).toBe('发送消息失败，请重试')
    expect(result.current.isLoading).toBe(false)
  })
})
```

### 2.5 工具函数测试示例

```typescript
// src/utils/__tests__/formatters.test.ts
import { describe, it, expect } from 'vitest'
import { formatTime, formatFileSize, formatTokenCount } from '../formatters'

describe('formatters', () => {
  describe('formatTime', () => {
    it('formats recent time correctly', () => {
      const now = new Date('2024-01-01T10:30:00Z')
      const time = new Date('2024-01-01T10:25:00Z')
      
      expect(formatTime(time, now)).toBe('5分钟前')
    })

    it('formats today time correctly', () => {
      const now = new Date('2024-01-01T15:30:00Z')
      const time = new Date('2024-01-01T10:25:00Z')
      
      expect(formatTime(time, now)).toBe('10:25')
    })

    it('formats yesterday time correctly', () => {
      const now = new Date('2024-01-02T10:30:00Z')
      const time = new Date('2024-01-01T10:25:00Z')
      
      expect(formatTime(time, now)).toBe('昨天 10:25')
    })
  })

  describe('formatFileSize', () => {
    it('formats bytes correctly', () => {
      expect(formatFileSize(500)).toBe('500 B')
    })

    it('formats kilobytes correctly', () => {
      expect(formatFileSize(1536)).toBe('1.5 KB')
    })

    it('formats megabytes correctly', () => {
      expect(formatFileSize(1572864)).toBe('1.5 MB')
    })
  })
})
```

## 3. 集成测试 (Integration Tests)

### 3.1 测试环境配置

**测试数据库配置**:
```typescript
// tests/setup/database.ts
import { createConnection } from 'typeorm'

export const setupTestDatabase = async () => {
  const connection = await createConnection({
    type: 'postgres',
    host: 'localhost',
    port: 5433, // 测试专用端口
    username: 'test_user',
    password: 'test_password',
    database: 'college_test',
    synchronize: true,
    dropSchema: true,
    entities: ['src/entities/*.ts'],
    logging: false
  })
  
  return connection
}

export const teardownTestDatabase = async (connection: any) => {
  await connection.dropDatabase()
  await connection.close()
}
```

### 3.2 API集成测试示例

```typescript
// tests/integration/auth.test.ts
import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest'
import request from 'supertest'
import { app } from '../../backend/src/app'
import { setupTestDatabase, teardownTestDatabase } from '../setup/database'

describe('Authentication API', () => {
  let connection: any

  beforeAll(async () => {
    connection = await setupTestDatabase()
  })

  afterAll(async () => {
    await teardownTestDatabase(connection)
  })

  beforeEach(async () => {
    // 清理测试数据
    await connection.query('TRUNCATE TABLE users CASCADE')
  })

  describe('POST /api/auth/register', () => {
    it('should register new user successfully', async () => {
      const userData = {
        email: '<EMAIL>',
        username: 'testuser',
        password: 'password123',
        school: '清华大学',
        major: '计算机科学'
      }

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201)

      expect(response.body.user.email).toBe(userData.email)
      expect(response.body.user.username).toBe(userData.username)
      expect(response.body.token).toBeDefined()
      expect(response.body.user.password).toBeUndefined()
    })

    it('should reject duplicate email', async () => {
      const userData = {
        email: '<EMAIL>',
        username: 'testuser',
        password: 'password123'
      }

      // 首次注册
      await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201)

      // 重复注册
      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(400)

      expect(response.body.error).toBe('邮箱已被注册')
    })
  })

  describe('POST /api/auth/login', () => {
    beforeEach(async () => {
      // 创建测试用户
      await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          username: 'testuser',
          password: 'password123'
        })
    })

    it('should login with correct credentials', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'password123'
        })
        .expect(200)

      expect(response.body.token).toBeDefined()
      expect(response.body.user.email).toBe('<EMAIL>')
    })

    it('should reject invalid credentials', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'wrongpassword'
        })
        .expect(401)

      expect(response.body.error).toBe('邮箱或密码错误')
    })
  })
})
```

### 3.3 聊天功能集成测试

```typescript
// tests/integration/chat.test.ts
import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest'
import request from 'supertest'
import { app } from '../../backend/src/app'

describe('Chat API', () => {
  let authToken: string
  let userId: string

  beforeAll(async () => {
    // 创建测试用户并获取token
    const response = await request(app)
      .post('/api/auth/register')
      .send({
        email: '<EMAIL>',
        username: 'chatuser',
        password: 'password123'
      })

    authToken = response.body.token
    userId = response.body.user.id
  })

  describe('POST /api/chat/conversations', () => {
    it('should create new conversation', async () => {
      const response = await request(app)
        .post('/api/chat/conversations')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          title: '数学问题讨论',
          category: 'math'
        })
        .expect(201)

      expect(response.body.id).toBeDefined()
      expect(response.body.title).toBe('数学问题讨论')
      expect(response.body.category).toBe('math')
      expect(response.body.userId).toBe(userId)
    })
  })

  describe('POST /api/chat/conversations/:id/messages', () => {
    let conversationId: string

    beforeEach(async () => {
      const conversation = await request(app)
        .post('/api/chat/conversations')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ title: '测试对话' })

      conversationId = conversation.body.id
    })

    it('should send message and get AI response', async () => {
      const response = await request(app)
        .post(`/api/chat/conversations/${conversationId}/messages`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          content: '1+1等于多少？',
          type: 'text'
        })
        .expect(200)

      expect(response.body.userMessage.content).toBe('1+1等于多少？')
      expect(response.body.aiMessage.content).toBeDefined()
      expect(response.body.aiMessage.role).toBe('assistant')
    })

    it('should handle file upload', async () => {
      const response = await request(app)
        .post(`/api/chat/conversations/${conversationId}/messages`)
        .set('Authorization', `Bearer ${authToken}`)
        .attach('file', 'tests/fixtures/math-problem.png')
        .field('content', '请解这道数学题')
        .expect(200)

      expect(response.body.userMessage.attachments).toHaveLength(1)
      expect(response.body.aiMessage.content).toContain('根据图片内容')
    })
  })
})
```

## 4. 端到端测试 (E2E Tests)

### 4.1 Playwright配置

**配置文件** (`playwright.config.ts`):
```typescript
import { defineConfig, devices } from '@playwright/test'

export default defineConfig({
  testDir: './tests/e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: [
    ['html'],
    ['json', { outputFile: 'test-results/results.json' }],
    ['junit', { outputFile: 'test-results/results.xml' }]
  ],
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    actionTimeout: 10000,
    navigationTimeout: 30000
  },

  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] }
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] }
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] }
    },
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] }
    }
  ],

  webServer: {
    command: 'npm run dev',
    port: 3000,
    reuseExistingServer: !process.env.CI
  }
})
```

### 4.2 用户认证流程测试

```typescript
// tests/e2e/auth.spec.ts
import { test, expect } from '@playwright/test'

test.describe('用户认证流程', () => {
  test('用户注册流程', async ({ page }) => {
    await page.goto('/register')

    // 填写注册表单
    await page.fill('[data-testid="email-input"]', '<EMAIL>')
    await page.fill('[data-testid="username-input"]', 'testuser')
    await page.fill('[data-testid="password-input"]', 'password123')
    await page.fill('[data-testid="confirm-password-input"]', 'password123')
    await page.selectOption('[data-testid="school-select"]', '清华大学')
    await page.fill('[data-testid="major-input"]', '计算机科学')

    // 提交表单
    await page.click('[data-testid="register-button"]')

    // 验证注册成功
    await expect(page).toHaveURL('/dashboard')
    await expect(page.locator('[data-testid="welcome-message"]')).toContainText('欢迎，testuser')
  })

  test('用户登录流程', async ({ page }) => {
    // 前置条件：用户已注册
    await page.goto('/login')

    await page.fill('[data-testid="email-input"]', '<EMAIL>')
    await page.fill('[data-testid="password-input"]', 'password123')
    await page.click('[data-testid="login-button"]')

    await expect(page).toHaveURL('/dashboard')
    await expect(page.locator('[data-testid="user-avatar"]')).toBeVisible()
  })

  test('密码重置流程', async ({ page }) => {
    await page.goto('/forgot-password')

    await page.fill('[data-testid="email-input"]', '<EMAIL>')
    await page.click('[data-testid="send-reset-button"]')

    await expect(page.locator('[data-testid="success-message"]')).toContainText('重置链接已发送')
  })
})
```

### 4.3 AI对话功能测试

```typescript
// tests/e2e/chat.spec.ts
import { test, expect } from '@playwright/test'

test.describe('AI对话功能', () => {
  test.beforeEach(async ({ page }) => {
    // 登录
    await page.goto('/login')
    await page.fill('[data-testid="email-input"]', '<EMAIL>')
    await page.fill('[data-testid="password-input"]', 'password123')
    await page.click('[data-testid="login-button"]')
    await expect(page).toHaveURL('/dashboard')
  })

  test('创建新对话并发送消息', async ({ page }) => {
    // 进入对话页面
    await page.click('[data-testid="nav-chat"]')
    await expect(page).toHaveURL('/chat')

    // 创建新对话
    await page.click('[data-testid="new-chat-button"]')
    
    // 发送消息
    const messageInput = page.locator('[data-testid="message-input"]')
    await messageInput.fill('你好，请介绍一下你自己')
    await page.click('[data-testid="send-button"]')

    // 验证消息发送
    await expect(page.locator('[data-testid="user-message"]').last()).toContainText('你好，请介绍一下你自己')
    
    // 等待AI回复
    await expect(page.locator('[data-testid="ai-message"]').last()).toBeVisible({ timeout: 10000 })
    await expect(page.locator('[data-testid="ai-message"]').last()).not.toBeEmpty()
  })

  test('上传图片并发送', async ({ page }) => {
    await page.goto('/chat')
    
    // 上传文件
    const fileInput = page.locator('[data-testid="file-input"]')
    await fileInput.setInputFiles('tests/fixtures/sample-image.png')
    
    // 确认文件上传
    await expect(page.locator('[data-testid="uploaded-file"]')).toBeVisible()
    
    // 添加描述并发送
    await page.fill('[data-testid="message-input"]', '请分析这张图片')
    await page.click('[data-testid="send-button"]')
    
    // 验证消息包含附件
    await expect(page.locator('[data-testid="message-attachment"]')).toBeVisible()
  })

  test('数学公式渲染', async ({ page }) => {
    await page.goto('/chat')
    
    await page.fill('[data-testid="message-input"]', '请展示二次方程的求根公式')
    await page.click('[data-testid="send-button"]')
    
    // 等待包含数学公式的回复
    await expect(page.locator('[data-testid="math-formula"]')).toBeVisible({ timeout: 10000 })
  })

  test('对话历史记录', async ({ page }) => {
    await page.goto('/chat')
    
    // 发送几条消息创建历史
    for (let i = 1; i <= 3; i++) {
      await page.fill('[data-testid="message-input"]', `测试消息 ${i}`)
      await page.click('[data-testid="send-button"]')
      await page.waitForTimeout(1000)
    }
    
    // 打开历史记录
    await page.click('[data-testid="chat-history-button"]')
    
    // 验证历史记录显示
    await expect(page.locator('[data-testid="chat-history-item"]')).toHaveCount(1)
    
    // 点击历史记录项
    await page.click('[data-testid="chat-history-item"]')
    
    // 验证消息加载
    await expect(page.locator('[data-testid="user-message"]')).toHaveCount(3)
  })
})
```

### 4.4 PPT生成功能测试

```typescript
// tests/e2e/ppt.spec.ts
import { test, expect } from '@playwright/test'

test.describe('PPT生成功能', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/login')
    await page.fill('[data-testid="email-input"]', '<EMAIL>')
    await page.fill('[data-testid="password-input"]', 'password123')
    await page.click('[data-testid="login-button"]')
  })

  test('基于文本创建PPT', async ({ page }) => {
    await page.goto('/ppt')
    
    // 选择创建方式
    await page.click('[data-testid="create-from-text"]')
    
    // 输入PPT主题
    await page.fill('[data-testid="ppt-title"]', '人工智能发展历程')
    await page.fill('[data-testid="ppt-description"]', '介绍AI从1950年代至今的重要发展节点')
    
    // 选择模板
    await page.click('[data-testid="template-academic"]')
    
    // 开始生成
    await page.click('[data-testid="generate-ppt-button"]')
    
    // 等待生成完成
    await expect(page.locator('[data-testid="generation-progress"]')).toBeVisible()
    await expect(page.locator('[data-testid="ppt-preview"]')).toBeVisible({ timeout: 30000 })
    
    // 验证PPT内容
    await expect(page.locator('[data-testid="slide-title"]').first()).toContainText('人工智能发展历程')
  })

  test('上传文档生成PPT', async ({ page }) => {
    await page.goto('/ppt')
    
    await page.click('[data-testid="create-from-document"]')
    
    // 上传文档
    const fileInput = page.locator('[data-testid="document-upload"]')
    await fileInput.setInputFiles('tests/fixtures/sample-document.docx')
    
    // 等待文档解析
    await expect(page.locator('[data-testid="document-parsed"]')).toBeVisible({ timeout: 10000 })
    
    // 选择模板并生成
    await page.click('[data-testid="template-business"]')
    await page.click('[data-testid="generate-ppt-button"]')
    
    // 验证生成结果
    await expect(page.locator('[data-testid="ppt-preview"]')).toBeVisible({ timeout: 30000 })
  })

  test('PPT编辑和导出', async ({ page }) => {
    // 假设已有PPT项目
    await page.goto('/ppt/123')
    
    // 编辑幻灯片
    await page.click('[data-testid="slide-1"]')
    await page.click('[data-testid="edit-slide"]')
    
    // 修改标题
    await page.fill('[data-testid="slide-title-input"]', '修改后的标题')
    await page.click('[data-testid="save-slide"]')
    
    // 验证修改生效
    await expect(page.locator('[data-testid="slide-title"]')).toContainText('修改后的标题')
    
    // 导出PPT
    await page.click('[data-testid="export-button"]')
    await page.click('[data-testid="export-pptx"]')
    
    // 等待下载
    const downloadPromise = page.waitForEvent('download')
    const download = await downloadPromise
    expect(download.suggestedFilename()).toMatch(/\.pptx$/)
  })
})
```

## 5. 性能测试

### 5.1 负载测试配置

**K6配置** (`tests/performance/load-test.js`):
```javascript
import http from 'k6/http'
import { check, sleep } from 'k6'

export const options = {
  stages: [
    { duration: '2m', target: 100 }, // 渐进增加到100用户
    { duration: '5m', target: 100 }, // 保持100用户5分钟
    { duration: '2m', target: 200 }, // 增加到200用户
    { duration: '5m', target: 200 }, // 保持200用户5分钟
    { duration: '2m', target: 0 },   // 回到0用户
  ],
  thresholds: {
    http_req_duration: ['p(99)<1500'], // 99%请求在1.5秒内完成
    http_req_failed: ['rate<0.1'],     // 错误率小于10%
  },
}

export default function() {
  // 登录
  const loginResponse = http.post('http://localhost:3001/api/auth/login', {
    email: '<EMAIL>',
    password: 'password123'
  })
  
  check(loginResponse, {
    'login successful': (resp) => resp.status === 200,
    'token received': (resp) => resp.json('token') !== null,
  })
  
  const token = loginResponse.json('token')
  const params = {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  }
  
  // 发送聊天消息
  const chatResponse = http.post('http://localhost:3001/api/chat/send', 
    JSON.stringify({
      message: 'Hello, how are you?',
      conversationId: null
    }), 
    params
  )
  
  check(chatResponse, {
    'chat response received': (resp) => resp.status === 200,
    'response time OK': (resp) => resp.timings.duration < 2000,
  })
  
  sleep(1)
}
```

### 5.2 前端性能测试

**Lighthouse CI配置** (`.lighthouserc.json`):
```json
{
  "ci": {
    "collect": {
      "url": ["http://localhost:3000", "http://localhost:3000/chat", "http://localhost:3000/ppt"],
      "numberOfRuns": 3
    },
    "assert": {
      "assertions": {
        "categories:performance": ["error", {"minScore": 0.8}],
        "categories:accessibility": ["error", {"minScore": 0.9}],
        "categories:best-practices": ["error", {"minScore": 0.8}],
        "categories:seo": ["error", {"minScore": 0.8}],
        "first-contentful-paint": ["error", {"maxNumericValue": 2000}],
        "largest-contentful-paint": ["error", {"maxNumericValue": 2500}],
        "cumulative-layout-shift": ["error", {"maxNumericValue": 0.1}],
        "first-input-delay": ["error", {"maxNumericValue": 100}]
      }
    },
    "upload": {
      "target": "temporary-public-storage"
    }
  }
}
```

## 6. 测试数据管理

### 6.1 测试数据工厂

```typescript
// tests/factories/user.factory.ts
import { Factory } from 'fishery'
import { faker } from '@faker-js/faker/locale/zh_CN'

export const userFactory = Factory.define<User>(({ sequence }) => ({
  id: sequence.toString(),
  email: faker.internet.email(),
  username: faker.internet.username(),
  avatar: faker.image.avatar(),
  school: faker.helpers.arrayElement(['清华大学', '北京大学', '中国科学技术大学']),
  major: faker.helpers.arrayElement(['计算机科学', '数据科学', '人工智能']),
  grade: faker.helpers.arrayElement(['大一', '大二', '大三', '大四']),
  createdAt: faker.date.recent(),
  updatedAt: faker.date.recent()
}))

// 使用示例
const testUser = userFactory.build()
const testUsers = userFactory.buildList(5)
```

### 6.2 Mock数据服务

```typescript
// tests/mocks/chat.mock.ts
import { rest } from 'msw'

export const chatHandlers = [
  rest.post('/api/chat/send', (req, res, ctx) => {
    const { message } = req.body as { message: string }
    
    // 模拟不同类型的响应
    if (message.includes('数学')) {
      return res(
        ctx.delay(1000),
        ctx.json({
          id: '123',
          content: '这是一个数学问题的回答，包含公式：$$E = mc^2$$',
          role: 'assistant',
          timestamp: new Date().toISOString(),
          tokens: 50
        })
      )
    }
    
    return res(
      ctx.delay(800),
      ctx.json({
        id: '123',
        content: '这是AI的标准回复',
        role: 'assistant',
        timestamp: new Date().toISOString(),
        tokens: 30
      })
    )
  }),

  rest.get('/api/chat/conversations', (req, res, ctx) => {
    return res(
      ctx.json([
        {
          id: '1',
          title: '数学讨论',
          category: 'math',
          lastMessage: '求解二次方程',
          updatedAt: new Date().toISOString()
        }
      ])
    )
  })
]
```

## 7. CI/CD集成

### 7.1 GitHub Actions配置

```yaml
# .github/workflows/test.yml
name: 测试流水线

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run unit tests
        run: npm run test:unit:coverage
      
      - name: Upload coverage
        uses: codecov/codecov-action@v3

  integration-tests:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_DB: college_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5433:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6380:6379
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run integration tests
        run: npm run test:integration
        env:
          DATABASE_URL: postgresql://postgres:test_password@localhost:5433/college_test
          REDIS_URL: redis://localhost:6380

  e2e-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Install Playwright
        run: npx playwright install --with-deps
      
      - name: Start application
        run: |
          npm run build
          npm run start &
          sleep 10
      
      - name: Run E2E tests
        run: npm run test:e2e
      
      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: playwright-report
          path: playwright-report/
```

## 8. 测试报告和监控

### 8.1 测试覆盖率要求

- **整体覆盖率**: ≥ 80%
- **关键模块覆盖率**: ≥ 90%
- **新增代码覆盖率**: ≥ 95%

### 8.2 质量指标

- **单元测试执行时间**: < 30秒
- **集成测试执行时间**: < 5分钟  
- **E2E测试执行时间**: < 15分钟
- **测试失败率**: < 1%

### 8.3 持续监控

- 每日定时运行完整测试套件
- PR合并前强制测试通过
- 定期更新测试用例和数据
- 监控测试执行时间趋势

## 9. 总结

通过建立完善的测试体系，我们确保：

1. **质量保证**: 全面的测试覆盖，降低生产环境问题
2. **开发效率**: 快速反馈，早期发现问题
3. **持续集成**: 自动化测试流程，减少人工干预
4. **用户体验**: 端到端测试确保功能完整性
5. **性能监控**: 持续监控性能指标，优化用户体验

所有测试都设计为完全自动化，无需用户交互，确保可以在CI/CD环境中稳定运行。 