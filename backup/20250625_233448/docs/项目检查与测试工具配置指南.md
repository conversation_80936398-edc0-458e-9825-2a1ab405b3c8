# 项目检查与测试工具配置指南

## 文档信息
- **文档版本**: v1.0
- **创建日期**: 2025-01-15
- **更新日期**: 2025-01-15
- **维护人**: 项目组
- **审核人**: 技术负责人

## 概述

本指南基于2024年最新的最佳实践，为高校AI产品项目推荐并配置适合的项目检查、代码审查和测试工具。通过建立完善的质量保证体系，确保代码质量、提升开发效率、减少生产环境问题。

## 工具选型策略

### 选型原则

1. **技术栈匹配**: 工具必须与项目技术栈兼容
2. **团队能力**: 考虑团队学习成本和使用难度
3. **质量效益**: 平衡工具成本与质量提升效果
4. **生态完整**: 优先选择生态系统完善的工具
5. **维护性**: 选择有长期支持的成熟工具

### 推荐工具组合

```mermaid
graph TD
    A[代码质量保证] --> B[静态代码分析]
    A --> C[代码格式化]
    A --> D[测试框架]
    A --> E[CI/CD集成]
    
    B --> B1[ESLint - JavaScript/TypeScript]
    B --> B2[Stylelint - CSS/SCSS]
    B --> B3[SonarQube - 代码质量分析]
    
    C --> C1[Prettier - 代码格式化]
    C --> C2[EditorConfig - 编辑器配置]
    
    D --> D1[Vitest - 单元测试]
    D --> D2[Playwright - E2E测试]
    D --> D3[Testing Library - 组件测试]
    
    E --> E1[GitHub Actions - CI/CD]
    E --> E2[Husky - Git Hooks]
    E --> E3[lint-staged - 预提交检查]
```

## 前端项目配置

### 1. ESLint配置 (JavaScript/TypeScript检查)

**安装依赖**
```bash
npm install --save-dev eslint @typescript-eslint/parser @typescript-eslint/eslint-plugin
npm install --save-dev eslint-config-prettier eslint-plugin-prettier
npm install --save-dev eslint-plugin-react eslint-plugin-react-hooks
npm install --save-dev eslint-plugin-jsx-a11y eslint-plugin-import
```

**配置文件 `.eslintrc.js`**
```javascript
module.exports = {
  root: true,
  env: {
    browser: true,
    es2021: true,
    node: true,
  },
  extends: [
    'eslint:recommended',
    '@typescript-eslint/recommended',
    'plugin:react/recommended',
    'plugin:react-hooks/recommended',
    'plugin:jsx-a11y/recommended',
    'plugin:import/recommended',
    'plugin:import/typescript',
    'plugin:prettier/recommended',
  ],
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module',
    ecmaFeatures: {
      jsx: true,
    },
    project: './tsconfig.json',
  },
  plugins: [
    'react',
    'react-hooks',
    '@typescript-eslint',
    'jsx-a11y',
    'import',
  ],
  settings: {
    react: {
      version: 'detect',
    },
    'import/resolver': {
      typescript: {
        alwaysTryTypes: true,
        project: './tsconfig.json',
      },
    },
  },
  rules: {
    // TypeScript特定规则
    '@typescript-eslint/no-unused-vars': 'error',
    '@typescript-eslint/no-explicit-any': 'warn',
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    '@typescript-eslint/no-non-null-assertion': 'warn',
    
    // React特定规则
    'react/react-in-jsx-scope': 'off', // React 17+不需要
    'react/prop-types': 'off', // 使用TypeScript
    'react-hooks/rules-of-hooks': 'error',
    'react-hooks/exhaustive-deps': 'warn',
    
    // 无障碍性规则
    'jsx-a11y/anchor-is-valid': 'warn',
    'jsx-a11y/click-events-have-key-events': 'warn',
    'jsx-a11y/no-static-element-interactions': 'warn',
    
    // 导入规则
    'import/order': [
      'error',
      {
        groups: [
          'builtin',
          'external',
          'internal',
          'parent',
          'sibling',
          'index',
        ],
        'newlines-between': 'always',
        alphabetize: {
          order: 'asc',
          caseInsensitive: true,
        },
      },
    ],
    'import/no-unresolved': 'error',
    'import/no-duplicates': 'error',
    
    // 通用规则
    'no-console': process.env.NODE_ENV === 'production' ? 'error' : 'warn',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'warn',
    'prefer-const': 'error',
    'no-var': 'error',
  },
  overrides: [
    {
      files: ['**/*.test.{js,jsx,ts,tsx}', '**/__tests__/**/*'],
      env: {
        jest: true,
      },
      plugins: ['testing-library'],
      extends: ['plugin:testing-library/react'],
    },
  ],
};
```

**忽略文件 `.eslintignore`**
```
node_modules
dist
build
public
.next
coverage
*.config.js
*.config.ts
```

### 2. Prettier配置 (代码格式化)

**安装依赖**
```bash
npm install --save-dev prettier
```

**配置文件 `.prettierrc`**
```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false,
  "bracketSpacing": true,
  "bracketSameLine": false,
  "arrowParens": "avoid",
  "endOfLine": "lf",
  "quoteProps": "as-needed",
  "jsxSingleQuote": true,
  "proseWrap": "preserve"
}
```

**忽略文件 `.prettierignore`**
```
node_modules
dist
build
public
.next
coverage
package-lock.json
yarn.lock
*.min.js
*.min.css
```

### 3. Stylelint配置 (CSS/SCSS检查)

**安装依赖**
```bash
npm install --save-dev stylelint stylelint-config-standard-scss
npm install --save-dev stylelint-config-prettier stylelint-order
```

**配置文件 `.stylelintrc.js`**
```javascript
module.exports = {
  extends: [
    'stylelint-config-standard-scss',
    'stylelint-config-prettier',
  ],
  plugins: ['stylelint-order'],
  rules: {
    // 顺序规则
    'order/properties-alphabetical-order': true,
    
    // SCSS特定规则
    'scss/at-rule-no-unknown': true,
    'scss/selector-no-redundant-nesting-selector': true,
    
    // 通用规则
    'selector-class-pattern': '^[a-z][a-zA-Z0-9]*$',
    'color-hex-case': 'lower',
    'color-hex-length': 'short',
    'declaration-block-no-duplicate-properties': true,
    'declaration-empty-line-before': null,
    'rule-empty-line-before': [
      'always-multi-line',
      {
        except: ['first-nested'],
        ignore: ['after-comment'],
      },
    ],
  },
  ignoreFiles: [
    'node_modules/**/*',
    'dist/**/*',
    'build/**/*',
    'public/**/*',
  ],
};
```

## 测试工具配置

### 1. Vitest配置 (单元测试)

**安装依赖**
```bash
npm install --save-dev vitest @vitest/ui @vitest/coverage-v8
npm install --save-dev jsdom @testing-library/react @testing-library/jest-dom
npm install --save-dev @testing-library/user-event
```

**配置文件 `vitest.config.ts`**
```typescript
import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';
import path from 'path';

export default defineConfig({
  plugins: [react()],
  test: {
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
    globals: true,
    css: true,
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'src/test/',
        '**/*.d.ts',
        '**/*.config.*',
        'dist/',
        'build/',
      ],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80,
        },
      },
    },
    include: ['src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
    exclude: [
      'node_modules',
      'dist',
      '.idea',
      '.git',
      '.cache',
      'e2e',
    ],
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@components': path.resolve(__dirname, './src/components'),
      '@utils': path.resolve(__dirname, './src/utils'),
      '@hooks': path.resolve(__dirname, './src/hooks'),
      '@types': path.resolve(__dirname, './src/types'),
    },
  },
});
```

**测试设置文件 `src/test/setup.ts`**
```typescript
import '@testing-library/jest-dom';
import { expect, afterEach, vi } from 'vitest';
import { cleanup } from '@testing-library/react';

// 每个测试后清理
afterEach(() => {
  cleanup();
});

// 模拟全局对象
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// 模拟 ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// 模拟 IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));
```

**示例测试文件 `src/components/Button.test.tsx`**
```typescript
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, it, expect, vi } from 'vitest';
import Button from './Button';

describe('Button Component', () => {
  it('renders with correct text', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByRole('button', { name: /click me/i })).toBeInTheDocument();
  });

  it('calls onClick when clicked', async () => {
    const user = userEvent.setup();
    const handleClick = vi.fn();
    
    render(<Button onClick={handleClick}>Click me</Button>);
    
    await user.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('is disabled when disabled prop is true', () => {
    render(<Button disabled>Click me</Button>);
    expect(screen.getByRole('button')).toBeDisabled();
  });

  it('applies correct variant class', () => {
    render(<Button variant="primary">Click me</Button>);
    expect(screen.getByRole('button')).toHaveClass('btn-primary');
  });
});
```

### 2. Playwright配置 (E2E测试)

**安装依赖**
```bash
npm install --save-dev @playwright/test
npx playwright install
```

**配置文件 `playwright.config.ts`**
```typescript
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: [
    ['html'],
    ['junit', { outputFile: 'test-results/junit-report.xml' }],
  ],
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },
    {
      name: 'Mobile Safari',
      use: { ...devices['iPhone 12'] },
    },
  ],
  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI,
    timeout: 120 * 1000,
  },
});
```

**示例E2E测试 `e2e/auth.spec.ts`**
```typescript
import { test, expect } from '@playwright/test';

test.describe('用户认证', () => {
  test('用户可以成功登录', async ({ page }) => {
    // 访问登录页面
    await page.goto('/login');

    // 验证页面标题
    await expect(page).toHaveTitle(/登录/);

    // 填写登录表单
    await page.getByLabel('邮箱').fill('<EMAIL>');
    await page.getByLabel('密码').fill('password123');

    // 点击登录按钮
    await page.getByRole('button', { name: '登录' }).click();

    // 验证登录成功
    await expect(page).toHaveURL('/dashboard');
    await expect(page.getByText('欢迎回来')).toBeVisible();
  });

  test('显示登录错误信息', async ({ page }) => {
    await page.goto('/login');

    // 填写错误的登录信息
    await page.getByLabel('邮箱').fill('<EMAIL>');
    await page.getByLabel('密码').fill('wrongpassword');

    // 点击登录按钮
    await page.getByRole('button', { name: '登录' }).click();

    // 验证错误信息显示
    await expect(page.getByText('邮箱或密码错误')).toBeVisible();
  });

  test('用户可以退出登录', async ({ page }) => {
    // 先登录
    await page.goto('/login');
    await page.getByLabel('邮箱').fill('<EMAIL>');
    await page.getByLabel('密码').fill('password123');
    await page.getByRole('button', { name: '登录' }).click();

    // 等待跳转到仪表板
    await expect(page).toHaveURL('/dashboard');

    // 点击用户菜单
    await page.getByTestId('user-menu').click();

    // 点击退出登录
    await page.getByRole('button', { name: '退出登录' }).click();

    // 验证退出成功
    await expect(page).toHaveURL('/login');
  });
});
```

## 后端项目配置

### 1. Node.js项目ESLint配置

**配置文件 `.eslintrc.js`**
```javascript
module.exports = {
  root: true,
  env: {
    node: true,
    es2021: true,
  },
  extends: [
    'eslint:recommended',
    '@typescript-eslint/recommended',
    'plugin:prettier/recommended',
  ],
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module',
    project: './tsconfig.json',
  },
  plugins: ['@typescript-eslint'],
  rules: {
    '@typescript-eslint/no-unused-vars': 'error',
    '@typescript-eslint/explicit-function-return-type': 'warn',
    '@typescript-eslint/no-explicit-any': 'warn',
    '@typescript-eslint/prefer-nullish-coalescing': 'error',
    '@typescript-eslint/prefer-optional-chain': 'error',
    'no-console': 'off', // 后端允许console
    'prefer-const': 'error',
    'no-var': 'error',
  },
  overrides: [
    {
      files: ['**/*.test.{js,ts}', '**/__tests__/**/*'],
      env: {
        jest: true,
      },
    },
  ],
};
```

### 2. 后端测试配置 (Vitest)

**配置文件 `vitest.config.ts`**
```typescript
import { defineConfig } from 'vitest/config';
import path from 'path';

export default defineConfig({
  test: {
    globals: true,
    environment: 'node',
    setupFiles: ['./src/test/setup.ts'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'src/test/',
        '**/*.d.ts',
        '**/*.config.*',
        'dist/',
      ],
      thresholds: {
        global: {
          branches: 75,
          functions: 75,
          lines: 75,
          statements: 75,
        },
      },
    },
    include: ['src/**/*.{test,spec}.{js,ts}'],
    exclude: ['node_modules', 'dist', '.idea', '.git', '.cache'],
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
});
```

## SonarQube集成

### 1. SonarQube配置

**配置文件 `sonar-project.properties`**
```properties
sonar.projectKey=college-ai-platform
sonar.projectName=高校AI产品平台
sonar.projectVersion=1.0

# 源代码路径
sonar.sources=src
sonar.tests=src
sonar.test.inclusions=**/*.test.ts,**/*.test.tsx,**/*.spec.ts,**/*.spec.tsx

# 排除路径
sonar.exclusions=node_modules/**,dist/**,build/**,coverage/**,**/*.config.js,**/*.config.ts

# TypeScript配置
sonar.typescript.lcov.reportPaths=coverage/lcov.info

# 质量门设置
sonar.qualitygate.wait=true

# 代码覆盖率
sonar.coverage.exclusions=**/*.test.ts,**/*.test.tsx,**/*.spec.ts,**/*.spec.tsx,**/*.config.*,**/*.d.ts

# 重复代码检测
sonar.cpd.exclusions=**/*.test.ts,**/*.test.tsx,**/*.spec.ts,**/*.spec.tsx
```

### 2. GitHub Actions集成

**配置文件 `.github/workflows/sonarcloud.yml`**
```yaml
name: SonarCloud Analysis

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  sonarcloud:
    name: SonarCloud Analysis
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run tests with coverage
        run: npm run test:coverage

      - name: SonarCloud Scan
        uses: SonarSource/sonarcloud-github-action@master
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
```

## Git Hooks配置

### 1. Husky安装和配置

**安装依赖**
```bash
npm install --save-dev husky lint-staged
npx husky install
npm set-script prepare "husky install"
```

**Pre-commit Hook `.husky/pre-commit`**
```bash
#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

npx lint-staged
```

**Commit Message Hook `.husky/commit-msg`**
```bash
#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

npx commitlint --edit $1
```

### 2. lint-staged配置

**配置文件 `.lintstagedrc.js`**
```javascript
module.exports = {
  '*.{js,jsx,ts,tsx}': [
    'eslint --fix',
    'prettier --write',
    'vitest related --run',
  ],
  '*.{css,scss,less}': ['stylelint --fix', 'prettier --write'],
  '*.{json,md,yml,yaml}': ['prettier --write'],
  'package.json': ['npm run lint:package'],
};
```

### 3. CommitLint配置

**安装依赖**
```bash
npm install --save-dev @commitlint/config-conventional @commitlint/cli
```

**配置文件 `.commitlintrc.js`**
```javascript
module.exports = {
  extends: ['@commitlint/config-conventional'],
  rules: {
    'type-enum': [
      2,
      'always',
      [
        'feat',     // 新功能
        'fix',      // 修复
        'docs',     // 文档
        'style',    // 格式
        'refactor', // 重构
        'test',     // 测试
        'chore',    // 构建过程或辅助工具的变动
        'perf',     // 性能优化
        'ci',       // CI配置
        'revert',   // 回滚
      ],
    ],
    'subject-case': [2, 'never', ['upper-case']],
    'header-max-length': [2, 'always', 100],
  },
};
```

## CI/CD集成

### 1. GitHub Actions完整配置

**配置文件 `.github/workflows/ci.yml`**
```yaml
name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

env:
  NODE_VERSION: '18'

jobs:
  lint-and-format:
    name: Lint and Format Check
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run ESLint
        run: npm run lint

      - name: Run Prettier check
        run: npm run format:check

      - name: Run Stylelint
        run: npm run lint:css

  unit-tests:
    name: Unit Tests
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run unit tests
        run: npm run test:coverage

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info

  e2e-tests:
    name: E2E Tests
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Install Playwright browsers
        run: npx playwright install --with-deps

      - name: Run E2E tests
        run: npm run test:e2e

      - name: Upload Playwright report
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: playwright-report
          path: playwright-report/
          retention-days: 30

  security-audit:
    name: Security Audit
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Run npm audit
        run: npm audit --audit-level moderate

      - name: Run security scan with Snyk
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
```

### 2. Package.json脚本配置

```json
{
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "preview": "vite preview",
    
    "lint": "eslint src --ext .js,.jsx,.ts,.tsx --max-warnings 0",
    "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix",
    "lint:css": "stylelint \"src/**/*.{css,scss}\"",
    "lint:css:fix": "stylelint \"src/**/*.{css,scss}\" --fix",
    "lint:package": "npmPkgJsonLint .",
    
    "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json,css,scss,md}\"",
    "format:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx,json,css,scss,md}\"",
    
    "test": "vitest",
    "test:ui": "vitest --ui",
    "test:coverage": "vitest run --coverage",
    "test:watch": "vitest --watch",
    
    "test:e2e": "playwright test",
    "test:e2e:ui": "playwright test --ui",
    "test:e2e:debug": "playwright test --debug",
    
    "type-check": "tsc --noEmit",
    "validate": "npm run type-check && npm run lint && npm run format:check && npm run test:coverage",
    
    "prepare": "husky install"
  }
}
```

## 质量门配置

### 1. 代码覆盖率要求

```javascript
// vitest.config.ts 中的覆盖率配置
coverage: {
  thresholds: {
    global: {
      branches: 80,    // 分支覆盖率
      functions: 80,   // 函数覆盖率
      lines: 80,       // 行覆盖率
      statements: 80,  // 语句覆盖率
    },
  },
}
```

### 2. ESLint错误零容忍

```bash
# 在CI中使用 max-warnings 0 确保没有警告
eslint src --ext .js,.jsx,.ts,.tsx --max-warnings 0
```

### 3. 代码复杂度控制

```javascript
// .eslintrc.js 中添加复杂度规则
rules: {
  'complexity': ['error', { max: 10 }],
  'max-depth': ['error', { max: 4 }],
  'max-lines-per-function': ['error', { max: 50 }],
  'max-params': ['error', { max: 4 }],
}
```

## 团队协作配置

### 1. VS Code工作区配置

**配置文件 `.vscode/settings.json`**
```json
{
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true,
    "source.fixAll.stylelint": true
  },
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact"
  ],
  "typescript.preferences.importModuleSpecifier": "relative",
  "typescript.suggest.autoImports": true,
  "files.exclude": {
    "node_modules": true,
    "dist": true,
    "build": true,
    "coverage": true
  },
  "search.exclude": {
    "node_modules": true,
    "dist": true,
    "build": true,
    "coverage": true
  }
}
```

**推荐扩展 `.vscode/extensions.json`**
```json
{
  "recommendations": [
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "stylelint.vscode-stylelint",
    "ms-playwright.playwright",
    "vitest.explorer",
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-typescript-next"
  ]
}
```

### 2. EditorConfig配置

**配置文件 `.editorconfig`**
```ini
root = true

[*]
charset = utf-8
end_of_line = lf
indent_style = space
indent_size = 2
insert_final_newline = true
trim_trailing_whitespace = true

[*.{js,jsx,ts,tsx,json}]
indent_size = 2

[*.{css,scss,less}]
indent_size = 2

[*.md]
trim_trailing_whitespace = false

[*.{yml,yaml}]
indent_size = 2
```

## 监控与报告

### 1. 测试报告集成

```yaml
# GitHub Actions中生成测试报告
- name: Publish Test Results
  uses: dorny/test-reporter@v1
  if: success() || failure()
  with:
    name: Jest Tests
    path: test-results/junit-report.xml
    reporter: jest-junit
```

### 2. 代码质量趋势跟踪

通过集成以下工具跟踪代码质量趋势：
- **SonarCloud**: 代码质量和安全性分析
- **Codecov**: 代码覆盖率趋势
- **DeepSource**: 自动代码审查
- **CodeClimate**: 技术债务跟踪

## 最佳实践建议

### 1. 渐进式采用

1. **第一阶段**: 配置基础的ESLint和Prettier
2. **第二阶段**: 添加单元测试和覆盖率要求
3. **第三阶段**: 集成E2E测试和CI/CD
4. **第四阶段**: 添加代码质量分析和安全扫描

### 2. 团队培训

- 定期进行工具使用培训
- 建立代码质量标准文档
- 分享最佳实践案例
- 定期回顾和改进流程

### 3. 性能优化

- 使用缓存加速CI/CD流程
- 并行运行测试任务
- 增量检查和测试
- 合理设置重试策略

## 故障排除

### 常见问题和解决方案

1. **ESLint配置冲突**
   ```bash
   # 清除缓存
   npx eslint --cache --cache-location .eslintcache src
   ```

2. **Prettier格式化冲突**
   ```bash
   # 检查配置优先级
   npx prettier --find-config-path src/index.ts
   ```

3. **测试环境问题**
   ```bash
   # 清除测试缓存
   npx vitest --run --reporter=verbose --no-cache
   ```

4. **Playwright浏览器问题**
   ```bash
   # 重新安装浏览器
   npx playwright install --force
   ```

## 总结

本配置指南提供了完整的项目检查与测试工具配置方案。通过合理配置这些工具，可以显著提升代码质量、减少bug、提高开发效率。

关键成功因素：
1. 选择适合项目的工具组合
2. 建立完善的质量门机制
3. 自动化执行检查和测试
4. 持续监控和改进流程
5. 团队成员积极参与和配合

建议定期（每季度）回顾和更新配置，确保工具链与项目需求和技术发展保持同步。 