# 高校AI助手 - API设计规范文档

## 1. API设计原则

### 1.1 RESTful设计原则
- **资源导向**: 使用名词表示资源，动词通过HTTP方法表达
- **无状态**: 每个请求包含处理所需的所有信息
- **统一接口**: 一致的URL结构和HTTP状态码使用
- **分层系统**: 支持中间件和代理
- **可缓存**: 明确标识可缓存的响应

### 1.2 API版本控制
```typescript
// URL版本控制
const API_BASE_URL = 'https://api.college-ai.com/v1';

// 版本策略
interface APIVersion {
  current: 'v1';
  supported: ['v1'];
  deprecated: [];
  sunset: [];
}
```

### 1.3 统一响应格式
```typescript
// 标准响应格式
interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  meta?: {
    pagination?: PaginationMeta;
    timestamp: string;
    requestId: string;
    version: string;
  };
}

// 分页元数据
interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}
```

## 2. 认证和授权API

### 2.1 用户认证接口

#### 2.1.1 用户注册
```typescript
// POST /api/v1/auth/register
interface RegisterRequest {
  email: string;
  username: string;
  password: string;
  confirmPassword: string;
  university?: string;
  inviteCode?: string;
}

interface RegisterResponse {
  user: {
    id: string;
    email: string;
    username: string;
    role: string;
    status: string;
  };
  tokens: {
    accessToken: string;
    refreshToken: string;
    expiresIn: number;
  };
}
```

#### 2.1.2 用户登录
```typescript
// POST /api/v1/auth/login
interface LoginRequest {
  identifier: string; // email 或 username
  password: string;
  rememberMe?: boolean;
}

interface LoginResponse {
  user: UserProfile;
  tokens: AuthTokens;
  permissions: string[];
}
```

#### 2.1.3 Token刷新
```typescript
// POST /api/v1/auth/refresh
interface RefreshRequest {
  refreshToken: string;
}

interface RefreshResponse {
  accessToken: string;
  expiresIn: number;
}
```

### 2.2 用户管理接口

#### 2.2.1 获取用户信息
```typescript
// GET /api/v1/users/profile
interface UserProfileResponse {
  id: string;
  email: string;
  username: string;
  profile: {
    firstName: string;
    lastName: string;
    avatar: string;
    university: string;
    major: string;
    year: number;
  };
  preferences: UserPreferences;
  subscription: SubscriptionInfo;
}
```

#### 2.2.2 更新用户信息
```typescript
// PUT /api/v1/users/profile
interface UpdateProfileRequest {
  firstName?: string;
  lastName?: string;
  avatar?: string;
  university?: string;
  major?: string;
  year?: number;
  bio?: string;
}
```

## 3. AI对话系统API

### 3.1 对话管理接口

#### 3.1.1 创建对话
```typescript
// POST /api/v1/conversations
interface CreateConversationRequest {
  title: string;
  type: 'general_chat' | 'study_help' | 'homework_assistance' | 'paper_writing' | 'ppt_creation';
  initialMessage?: string;
  context?: {
    subject?: string;
    difficulty?: string;
    tags?: string[];
  };
}

interface ConversationResponse {
  id: string;
  title: string;
  type: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  messageCount: number;
}
```

#### 3.1.2 获取对话列表
```typescript
// GET /api/v1/conversations
interface GetConversationsQuery {
  page?: number;
  limit?: number;
  type?: string;
  status?: string;
  search?: string;
  sortBy?: 'created_at' | 'updated_at' | 'title';
  sortOrder?: 'asc' | 'desc';
}

interface ConversationsListResponse {
  conversations: ConversationSummary[];
  pagination: PaginationMeta;
}
```

#### 3.1.3 获取对话详情
```typescript
// GET /api/v1/conversations/:id
interface ConversationDetailResponse {
  conversation: ConversationInfo;
  messages: Message[];
  statistics: {
    totalTokens: number;
    totalCost: number;
    messageCount: number;
    averageResponseTime: number;
  };
}
```

### 3.2 消息处理接口

#### 3.2.1 发送消息
```typescript
// POST /api/v1/conversations/:id/messages
interface SendMessageRequest {
  content: string;
  attachments?: {
    type: 'image' | 'file' | 'document';
    url: string;
    filename: string;
    size: number;
  }[];
  options?: {
    model?: string;
    temperature?: number;
    maxTokens?: number;
    systemPrompt?: string;
  };
}

interface MessageResponse {
  message: {
    id: string;
    role: 'user' | 'assistant';
    content: string;
    timestamp: string;
    metadata: {
      tokens: number;
      cost: number;
      processingTime: number;
      model: string;
    };
  };
}
```

#### 3.2.2 流式消息响应
```typescript
// POST /api/v1/conversations/:id/messages/stream
// Content-Type: text/event-stream

interface StreamMessageChunk {
  id: string;
  event: 'start' | 'chunk' | 'end' | 'error';
  data: {
    messageId?: string;
    content?: string;
    delta?: string;
    metadata?: {
      tokens?: number;
      cost?: number;
    };
    error?: string;
  };
}
```

### 3.3 文件处理接口

#### 3.3.1 文件上传
```typescript
// POST /api/v1/files/upload
// Content-Type: multipart/form-data

interface FileUploadRequest {
  file: File;
  type: 'image' | 'document' | 'audio' | 'video';
  purpose: 'chat' | 'homework' | 'paper' | 'ppt';
}

interface FileUploadResponse {
  file: {
    id: string;
    filename: string;
    originalName: string;
    size: number;
    mimeType: string;
    url: string;
    thumbnailUrl?: string;
  };
  processing: {
    status: 'pending' | 'processing' | 'completed' | 'failed';
    result?: any;
  };
}
```

#### 3.3.2 图片分析
```typescript
// POST /api/v1/ai/analyze-image
interface ImageAnalysisRequest {
  imageUrl: string;
  analysisType: 'general' | 'ocr' | 'math' | 'diagram' | 'chart';
  options?: {
    language?: string;
    includeText?: boolean;
    includeMath?: boolean;
  };
}

interface ImageAnalysisResponse {
  analysis: {
    description: string;
    text?: string;
    math?: string[];
    objects?: Array<{
      name: string;
      confidence: number;
      boundingBox?: number[];
    }>;
    tags: string[];
  };
  processing: {
    model: string;
    confidence: number;
    processingTime: number;
  };
}
```

## 4. PPT生成系统API

### 4.1 PPT项目管理

#### 4.1.1 创建PPT项目
```typescript
// POST /api/v1/ppt/projects
interface CreatePPTProjectRequest {
  title: string;
  description?: string;
  templateId?: string;
  content: {
    topic: string;
    outline?: string[];
    requirements?: {
      slideCount?: number;
      style?: string;
      language?: string;
    };
  };
}

interface PPTProjectResponse {
  project: {
    id: string;
    title: string;
    status: string;
    slideCount: number;
    createdAt: string;
  };
  generation: {
    status: 'pending' | 'generating' | 'completed' | 'failed';
    progress: number;
    estimatedTime?: number;
  };
}
```

#### 4.1.2 获取PPT项目列表
```typescript
// GET /api/v1/ppt/projects
interface PPTProjectsQuery {
  page?: number;
  limit?: number;
  status?: string;
  search?: string;
  sortBy?: 'created_at' | 'updated_at' | 'title';
}

interface PPTProjectsResponse {
  projects: PPTProjectSummary[];
  pagination: PaginationMeta;
}
```

### 4.2 模板管理

#### 4.2.1 获取模板列表
```typescript
// GET /api/v1/ppt/templates
interface TemplatesQuery {
  category?: string;
  search?: string;
  isPremium?: boolean;
  page?: number;
  limit?: number;
}

interface TemplatesResponse {
  templates: Array<{
    id: string;
    name: string;
    category: string;
    thumbnail: string;
    previewImages: string[];
    isPremium: boolean;
    rating: number;
    usageCount: number;
  }>;
  categories: string[];
  pagination: PaginationMeta;
}
```

#### 4.2.2 获取模板详情
```typescript
// GET /api/v1/ppt/templates/:id
interface TemplateDetailResponse {
  template: {
    id: string;
    name: string;
    description: string;
    category: string;
    thumbnails: string[];
    structure: {
      slideLayouts: string[];
      defaultStyles: any;
      colorScheme: string[];
    };
    metadata: {
      author: string;
      created: string;
      updated: string;
      rating: number;
      reviews: number;
    };
  };
}
```

### 4.3 幻灯片编辑

#### 4.3.1 获取幻灯片列表
```typescript
// GET /api/v1/ppt/projects/:projectId/slides
interface SlidesResponse {
  slides: Array<{
    id: string;
    number: number;
    title: string;
    layout: string;
    thumbnail: string;
    lastModified: string;
  }>;
  totalSlides: number;
}
```

#### 4.3.2 更新幻灯片内容
```typescript
// PUT /api/v1/ppt/projects/:projectId/slides/:slideId
interface UpdateSlideRequest {
  title?: string;
  content?: {
    elements: Array<{
      type: 'text' | 'image' | 'chart' | 'table';
      position: { x: number; y: number };
      size: { width: number; height: number };
      data: any;
      style?: any;
    }>;
  };
  layout?: string;
  notes?: string;
}
```

## 5. 论文助手API

### 5.1 论文项目管理

#### 5.1.1 创建论文项目
```typescript
// POST /api/v1/papers
interface CreatePaperRequest {
  title: string;
  subject: string;
  paperType: 'research_paper' | 'thesis' | 'essay' | 'report';
  requirements: {
    length: number;
    format: 'apa' | 'mla' | 'chicago' | 'ieee';
    deadline?: string;
  };
  outline?: string[];
}

interface PaperProjectResponse {
  paper: {
    id: string;
    title: string;
    status: string;
    progress: number;
    wordCount: number;
    createdAt: string;
  };
}
```

### 5.2 参考文献管理

#### 5.2.1 搜索文献
```typescript
// GET /api/v1/papers/references/search
interface ReferenceSearchQuery {
  query: string;
  databases?: string[]; // ['pubmed', 'scholar', 'arxiv']
  year?: {
    from?: number;
    to?: number;
  };
  type?: string[];
  limit?: number;
}

interface ReferenceSearchResponse {
  references: Array<{
    id: string;
    title: string;
    authors: string[];
    year: number;
    journal: string;
    doi?: string;
    abstract: string;
    relevanceScore: number;
  }>;
  totalFound: number;
  searchTime: number;
}
```

#### 5.2.2 添加参考文献
```typescript
// POST /api/v1/papers/:paperId/references
interface AddReferenceRequest {
  referenceId?: string; // 从搜索结果添加
  manual?: {
    title: string;
    authors: string[];
    year: number;
    journal?: string;
    doi?: string;
    url?: string;
    type: string;
  };
}
```

### 5.3 写作辅助

#### 5.3.1 生成大纲
```typescript
// POST /api/v1/papers/:paperId/outline/generate
interface GenerateOutlineRequest {
  topic: string;
  requirements: {
    sections: number;
    depth: number;
    style: string;
  };
  references?: string[];
}

interface OutlineResponse {
  outline: Array<{
    level: number;
    title: string;
    description: string;
    suggestedLength: number;
    keyPoints: string[];
  }>;
  estimatedWordCount: number;
}
```

#### 5.3.2 写作建议
```typescript
// POST /api/v1/papers/writing/suggest
interface WritingSuggestionRequest {
  text: string;
  context: {
    section: string;
    paperType: string;
    targetAudience: string;
  };
  suggestionTypes: ('grammar' | 'style' | 'clarity' | 'structure' | 'citations')[];
}

interface WritingSuggestionResponse {
  suggestions: Array<{
    type: string;
    severity: 'low' | 'medium' | 'high';
    range: { start: number; end: number };
    message: string;
    suggestion: string;
    explanation?: string;
  }>;
  overallScore: {
    readability: number;
    academicTone: number;
    clarity: number;
  };
}
```

## 6. 作业助手API

### 6.1 问题识别和解析

#### 6.1.1 上传作业问题
```typescript
// POST /api/v1/homework/problems/upload
interface UploadProblemRequest {
  file: File;
  subject: 'math' | 'physics' | 'chemistry' | 'biology' | 'programming';
  difficulty?: 'easy' | 'medium' | 'hard';
  context?: {
    course: string;
    chapter: string;
    topic: string;
  };
}

interface ProblemUploadResponse {
  problem: {
    id: string;
    text: string;
    type: string;
    subject: string;
    confidence: number;
  };
  processing: {
    ocrText: string;
    mathExpressions: string[];
    diagrams: Array<{
      type: string;
      description: string;
      boundingBox: number[];
    }>;
  };
}
```

#### 6.1.2 获取问题解答
```typescript
// POST /api/v1/homework/problems/:problemId/solve
interface SolveProblemRequest {
  method?: 'step_by_step' | 'direct' | 'multiple_approaches';
  showWork?: boolean;
  explanationLevel?: 'basic' | 'detailed' | 'advanced';
}

interface SolutionResponse {
  solution: {
    answer: string;
    steps: Array<{
      number: number;
      description: string;
      equation?: string;
      explanation: string;
    }>;
    method: string;
    confidence: number;
  };
  alternatives?: Array<{
    method: string;
    answer: string;
    briefSteps: string[];
  }>;
  verification: {
    isCorrect: boolean;
    checkMethod: string;
  };
}
```

### 6.2 作业管理

#### 6.2.1 创建作业集
```typescript
// POST /api/v1/homework/sets
interface CreateHomeworkSetRequest {
  title: string;
  subject: string;
  dueDate?: string;
  problems: string[]; // problem IDs
}

interface HomeworkSetResponse {
  set: {
    id: string;
    title: string;
    problemCount: number;
    completedCount: number;
    progress: number;
    estimatedTime: number;
  };
}
```

## 7. AI痕迹消除API

### 7.1 文本检测和优化

#### 7.1.1 AI文本检测
```typescript
// POST /api/v1/ai-detection/analyze
interface AIDetectionRequest {
  text: string;
  options?: {
    detectionModel?: string;
    threshold?: number;
    includeDetails?: boolean;
  };
}

interface AIDetectionResponse {
  result: {
    score: number; // 0-100, AI生成可能性
    confidence: number;
    risk_level: 'low' | 'medium' | 'high' | 'very_high';
  };
  analysis: {
    sentenceScores: Array<{
      sentence: string;
      score: number;
      indicators: string[];
    }>;
    patterns: {
      repetitiveStructures: boolean;
      unusualVocabulary: boolean;
      inconsistentStyle: boolean;
      artificialTransitions: boolean;
    };
  };
  recommendations: string[];
}
```

#### 7.1.2 文本优化
```typescript
// POST /api/v1/ai-detection/optimize
interface TextOptimizationRequest {
  text: string;
  optimizationLevel: 'light' | 'standard' | 'aggressive';
  preserveStructure?: boolean;
  targetStyle?: 'academic' | 'casual' | 'professional';
  customInstructions?: string;
}

interface TextOptimizationResponse {
  optimized: {
    text: string;
    changes: Array<{
      type: 'vocabulary' | 'structure' | 'style' | 'tone';
      original: string;
      modified: string;
      reason: string;
    }>;
  };
  metrics: {
    originalScore: number;
    optimizedScore: number;
    improvement: number;
    readabilityChange: number;
  };
  comparison: {
    wordCount: { original: number; optimized: number };
    sentenceCount: { original: number; optimized: number };
    averageSentenceLength: { original: number; optimized: number };
  };
}
```

## 8. 错误处理和状态码

### 8.1 HTTP状态码规范
```typescript
// 标准HTTP状态码使用
const HTTP_STATUS = {
  // 成功响应
  OK: 200,                    // 请求成功
  CREATED: 201,               // 资源创建成功
  ACCEPTED: 202,              // 请求已接受，异步处理中
  NO_CONTENT: 204,            // 成功，无返回内容

  // 客户端错误
  BAD_REQUEST: 400,           // 请求参数错误
  UNAUTHORIZED: 401,          // 未认证
  FORBIDDEN: 403,             // 无权限
  NOT_FOUND: 404,             // 资源不存在
  METHOD_NOT_ALLOWED: 405,    // 方法不允许
  CONFLICT: 409,              // 资源冲突
  UNPROCESSABLE_ENTITY: 422,  // 请求格式正确但语义错误
  TOO_MANY_REQUESTS: 429,     // 请求频率限制

  // 服务器错误
  INTERNAL_SERVER_ERROR: 500, // 服务器内部错误
  BAD_GATEWAY: 502,           // 网关错误
  SERVICE_UNAVAILABLE: 503,   // 服务不可用
  GATEWAY_TIMEOUT: 504        // 网关超时
} as const;
```

### 8.2 错误代码规范
```typescript
// 业务错误代码
const ERROR_CODES = {
  // 认证相关 (1xxx)
  INVALID_CREDENTIALS: '1001',
  TOKEN_EXPIRED: '1002',
  TOKEN_INVALID: '1003',
  ACCOUNT_DISABLED: '1004',
  EMAIL_NOT_VERIFIED: '1005',

  // 权限相关 (2xxx)
  INSUFFICIENT_PERMISSIONS: '2001',
  RESOURCE_ACCESS_DENIED: '2002',
  SUBSCRIPTION_REQUIRED: '2003',
  QUOTA_EXCEEDED: '2004',

  // 资源相关 (3xxx)
  RESOURCE_NOT_FOUND: '3001',
  RESOURCE_ALREADY_EXISTS: '3002',
  RESOURCE_CONFLICT: '3003',
  RESOURCE_LOCKED: '3004',

  // AI服务相关 (4xxx)
  AI_SERVICE_UNAVAILABLE: '4001',
  AI_QUOTA_EXCEEDED: '4002',
  AI_REQUEST_TOO_LARGE: '4003',
  AI_PROCESSING_FAILED: '4004',

  // 文件相关 (5xxx)
  FILE_TOO_LARGE: '5001',
  FILE_TYPE_NOT_SUPPORTED: '5002',
  FILE_UPLOAD_FAILED: '5003',
  FILE_PROCESSING_FAILED: '5004',

  // 系统相关 (9xxx)
  INTERNAL_ERROR: '9001',
  DATABASE_ERROR: '9002',
  EXTERNAL_SERVICE_ERROR: '9003',
  MAINTENANCE_MODE: '9004'
} as const;
```

### 8.3 错误响应示例
```typescript
// 标准错误响应格式
interface ErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: {
      field?: string;
      value?: any;
      constraints?: string[];
    };
  };
  meta: {
    timestamp: string;
    requestId: string;
    version: string;
  };
}

// 验证错误示例
const validationErrorExample: ErrorResponse = {
  success: false,
  error: {
    code: 'VALIDATION_ERROR',
    message: '请求数据验证失败',
    details: {
      field: 'email',
      value: 'invalid-email',
      constraints: ['必须是有效的邮箱地址']
    }
  },
  meta: {
    timestamp: '2025-01-08T10:30:00Z',
    requestId: 'req_123456789',
    version: 'v1'
  }
};
```

---

**总结：**
本API设计规范基于RESTful原则和最佳实践，提供了完整的接口定义、错误处理和文档标准，确保API的一致性、可维护性和易用性。 