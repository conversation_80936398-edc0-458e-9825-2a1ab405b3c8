# 高校AI助手 - 代码规范

## 更新日志

### 2025-01-15 v2.0 更新
- **新增**: React + Vite 前端Mock开发规范
- **新增**: MSW Mock数据管理规范  
- **新增**: Vitest + Playwright 测试规范
- **新增**: 分阶段开发流程规范
- **更新**: 技术栈选型调整为React+Vite
- **优化**: 项目结构适配Mock开发模式

## 分阶段开发规范

### 阶段1: 前端Mock开发规范（当前阶段）

#### 技术栈规范
- **构建工具**: Vite (替代Webpack，提升开发体验)
- **状态管理**: Zustand (替代Redux，简化状态管理)
- **Mock数据**: MSW (Service Worker级别Mock)
- **测试框架**: Vitest (替代Jest，与Vite集成更好)
- **E2E测试**: Playwright (替代Cypress，更稳定)

#### Mock开发原则
1. **真实性**: Mock数据应尽可能接近真实API响应
2. **完整性**: 覆盖所有业务场景，包括成功、失败、边界情况
3. **延迟模拟**: 模拟真实网络延迟，测试加载状态
4. **数据驱动**: 基于Mock数据定义API接口规范
5. **易切换**: Mock与真实API之间能够无缝切换

## 1. 代码组织规范

### 1.1 文件结构规范
- 单个文件不超过800行，超过需要拆分重构
- 函数单一职责，长度不超过50行
- 组件文件按功能模块分组织
- 使用统一的命名规范

### 1.2 目录结构规范
```
src/
├── components/          # 通用组件
│   ├── ui/             # 基础UI组件
│   ├── layout/         # 布局组件
│   └── business/       # 业务组件
├── pages/              # 页面组件
├── hooks/              # 自定义Hook
├── utils/              # 工具函数
├── services/           # API服务
├── types/              # TypeScript类型定义
├── constants/          # 常量定义
└── assets/             # 静态资源
```

### 1.3 命名规范
- **文件名**: kebab-case (chat-dialog.tsx)
- **组件名**: PascalCase (ChatDialog)
- **变量/函数名**: camelCase (chatHistory)
- **常量**: UPPER_SNAKE_CASE (API_BASE_URL)
- **接口**: PascalCase + I前缀 (IUserInfo)

## 2. TypeScript规范

### 2.1 类型定义
```typescript
// ✅ 好的做法
interface IUser {
  id: string;
  name: string;
  email: string;
  role: 'student' | 'teacher' | 'admin';
}

// ❌ 避免使用any
const userData: any = {};

// ✅ 明确类型
const userData: IUser = {
  id: '1',
  name: 'John',
  email: '<EMAIL>',
  role: 'student'
};
```

### 2.2 组件Props类型
```typescript
// ✅ 明确定义Props接口
interface IChatDialogProps {
  messages: IMessage[];
  onSendMessage: (content: string) => void;
  loading?: boolean;
}

const ChatDialog: React.FC<IChatDialogProps> = ({ 
  messages, 
  onSendMessage, 
  loading = false 
}) => {
  // 组件实现
};
```

## 3. React组件规范

### 3.1 组件结构
```typescript
// ✅ 标准组件结构
import React, { useState, useEffect } from 'react';
import { Button, Input } from 'antd';
import './ChatDialog.less';

interface IChatDialogProps {
  // Props定义
}

const ChatDialog: React.FC<IChatDialogProps> = (props) => {
  // 1. hooks
  const [state, setState] = useState();
  
  // 2. 副作用
  useEffect(() => {
    // 初始化逻辑
  }, []);
  
  // 3. 事件处理函数
  const handleSubmit = () => {
    // 处理逻辑
  };
  
  // 4. 渲染逻辑
  return (
    <div className="chat-dialog">
      {/* JSX内容 */}
    </div>
  );
};

export default ChatDialog;
```

### 3.2 状态管理
```typescript
// ✅ 使用useReducer管理复杂状态
interface IState {
  messages: IMessage[];
  loading: boolean;
  error: string | null;
}

const initialState: IState = {
  messages: [],
  loading: false,
  error: null
};

const chatReducer = (state: IState, action: any): IState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    default:
      return state;
  }
};
```

## 4. API服务规范

### 4.1 服务层设计
```typescript
// ✅ 统一的API服务结构
class ChatService {
  private baseURL = process.env.REACT_APP_API_URL;
  
  async sendMessage(content: string): Promise<IMessage> {
    try {
      const response = await fetch(`${this.baseURL}/chat/send`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ content })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('发送消息失败:', error);
      throw error;
    }
  }
}

export const chatService = new ChatService();
```

### 4.2 错误处理
```typescript
// ✅ 统一错误处理
const handleApiError = (error: Error) => {
  console.error('API错误:', error);
  message.error('操作失败，请重试');
};

// ✅ 使用try-catch包装异步操作
const sendMessage = async (content: string) => {
  try {
    setLoading(true);
    const result = await chatService.sendMessage(content);
    setMessages(prev => [...prev, result]);
  } catch (error) {
    handleApiError(error as Error);
  } finally {
    setLoading(false);
  }
};
```

## 5. 样式规范

### 5.1 CSS类命名
```less
// ✅ BEM命名规范
.chat-dialog {
  padding: 16px;
  
  &__header {
    font-size: 18px;
    font-weight: bold;
  }
  
  &__content {
    margin: 16px 0;
  }
  
  &__footer {
    text-align: right;
  }
  
  // 修饰符
  &--loading {
    opacity: 0.6;
  }
}
```

### 5.2 响应式设计
```less
// ✅ 移动端适配
.chat-dialog {
  padding: 16px;
  
  @media (max-width: 768px) {
    padding: 8px;
    
    &__header {
      font-size: 16px;
    }
  }
}
```

## 6. 性能优化规范

### 6.1 组件优化
```typescript
// ✅ 使用React.memo避免不必要渲染
const MessageItem = React.memo<IMessageItemProps>(({ message }) => {
  return (
    <div className="message-item">
      {message.content}
    </div>
  );
});

// ✅ 使用useMemo缓存计算结果
const filteredMessages = useMemo(() => {
  return messages.filter(msg => msg.type === 'user');
}, [messages]);

// ✅ 使用useCallback缓存函数
const handleSubmit = useCallback((content: string) => {
  onSendMessage(content);
}, [onSendMessage]);
```

### 6.2 懒加载
```typescript
// ✅ 代码分割和懒加载
const PPTGenerator = lazy(() => import('./components/PPTGenerator'));
const PaperAssistant = lazy(() => import('./components/PaperAssistant'));

// 使用Suspense包装
<Suspense fallback={<Spin size="large" />}>
  <PPTGenerator />
</Suspense>
```

## 7. 测试规范

### 7.1 单元测试
```typescript
// ✅ 测试文件命名: ComponentName.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import ChatDialog from './ChatDialog';

describe('ChatDialog', () => {
  test('应该正确渲染消息列表', () => {
    const mockMessages = [
      { id: '1', content: 'Hello', role: 'user' }
    ];
    
    render(<ChatDialog messages={mockMessages} onSendMessage={jest.fn()} />);
    
    expect(screen.getByText('Hello')).toBeInTheDocument();
  });
  
  test('应该在点击发送按钮时调用回调函数', () => {
    const mockOnSend = jest.fn();
    render(<ChatDialog messages={[]} onSendMessage={mockOnSend} />);
    
    const input = screen.getByPlaceholderText('输入消息...');
    const sendButton = screen.getByText('发送');
    
    fireEvent.change(input, { target: { value: 'Test message' } });
    fireEvent.click(sendButton);
    
    expect(mockOnSend).toHaveBeenCalledWith('Test message');
  });
});
```

## 8. Git提交规范

### 8.1 提交消息格式
```
<type>(<scope>): <description>

[可选的正文]

[可选的脚注]
```

### 8.2 提交类型
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档修改
- `style`: 代码格式修改
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

### 8.3 提交示例
```
feat(chat): 添加图片识别功能

- 支持上传PNG/JPG格式图片
- 集成OCR识别数学公式
- 添加图片预览组件

closes #123
```

## 9. 代码审查规范

### 9.1 审查清单
- [ ] 代码符合命名规范
- [ ] 类型定义完整准确
- [ ] 错误处理完善
- [ ] 性能优化合理
- [ ] 测试覆盖充分
- [ ] 文档注释清晰

### 9.2 常见问题记录

#### 9.2.1 类型安全问题
- **问题**: 使用any类型导致类型检查失效
- **解决**: 明确定义接口类型，避免使用any

#### 9.2.2 性能问题
- **问题**: 组件频繁重渲染
- **解决**: 使用React.memo、useMemo、useCallback优化

#### 9.2.3 状态管理问题
- **问题**: 状态更新逻辑复杂，容易产生bug
- **解决**: 使用useReducer管理复杂状态，遵循immutable原则

#### 9.2.4 API调用问题
- **问题**: 未处理网络错误，用户体验差
- **解决**: 统一错误处理，添加加载状态和重试机制

#### 9.2.5 样式问题
- **问题**: CSS类名冲突，样式覆盖问题
- **解决**: 采用BEM命名规范，使用CSS Modules或styled-components

## 10. 开发工具配置

### 10.1 ESLint配置
```json
{
  "extends": [
    "react-app",
    "@typescript-eslint/recommended"
  ],
  "rules": {
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/explicit-function-return-type": "warn",
    "react-hooks/exhaustive-deps": "warn"
  }
}
```

### 10.2 Prettier配置
```json
{
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false,
  "semi": true,
  "singleQuote": true,
  "quoteProps": "as-needed",
  "trailingComma": "es5"
}
```

## 11. 持续改进

### 11.1 代码质量监控
- 定期运行代码质量检查工具
- 监控测试覆盖率，保持在80%以上
- 使用SonarQube进行代码质量分析

### 11.2 技术债务管理
- 定期review和重构老旧代码
- 及时更新依赖包版本
- 记录和跟踪技术债务

## 12. 近期发现的问题和经验总结

### 12.1 常见问题记录（需要特别注意）
1. **TypeScript类型定义不完整** - 在API响应和组件props中缺少类型定义
2. **状态管理混乱** - 组件内状态和全局状态使用不规范  
3. **错误处理不统一** - API错误和UI错误处理方式不一致
4. **文件命名不规范** - 组件文件和工具文件命名混乱

### 12.2 最新经验积累（2025年更新）

#### 架构设计层面
1. **模块化单体架构** - 在初期开发阶段，模块化单体比微服务更适合快速迭代
2. **技术选型决策** - 需要建立完整的ADR（架构决策记录）流程
3. **数据库设计** - PostgreSQL作为主数据库，Redis作为缓存，Qdrant处理向量数据
4. **API设计规范** - 统一的响应格式和错误处理机制至关重要

#### 开发流程层面
1. **文档先行** - 完善的技术文档能显著提升开发效率
2. **渐进式实现** - 分阶段开发避免过度复杂化
3. **代码评审** - 严格的代码评审流程保证代码质量
4. **自动化测试** - 单元测试和集成测试的重要性

#### 最佳实践更新
1. 使用Zustand替代Redux Toolkit实现更轻量的状态管理
2. 采用React Query管理服务端状态，提升缓存效率
3. 实施严格的TypeScript配置，禁用any类型
4. 建立完善的错误边界和用户友好的错误提示

### 12.3 重复出现的bug强调 ⚠️
**特别注意以下问题，在开发中经常重复出现**: 
1. **异步操作错误处理缺失** - 所有Promise都必须有catch处理
2. **类型断言滥用** - 避免使用as进行强制类型转换
3. **组件生命周期管理** - 正确使用useEffect的依赖数组
4. **内存泄漏** - 及时清理事件监听器和定时器
5. **API响应数据验证** - 必须验证后端返回的数据结构
6. **文件大小限制** - 上传文件前检查文件大小和格式

### 12.4 编码过程中积累的新规范
1. **组件props接口定义** - 所有组件必须明确定义Props接口
2. **API调用封装** - 统一使用自定义hooks封装API调用逻辑
3. **错误消息国际化** - 所有用户可见的错误消息支持多语言
4. **加载状态管理** - 统一的Loading组件和状态管理

---

**文档最后更新**: 2025年1月8日  
**负责人**: 开发团队  
**下次检查计划**: 每完成一轮开发后更新，持续改进代码质量 