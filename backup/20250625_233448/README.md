# 高校AI助手 - College AI Assistant

## 项目简介

这是一个专为高校师生设计的综合性AI助手平台，提供AI对话、PPT生成、论文写作、理科作业辅导和AI痕迹消除等核心功能。

## 核心功能

### 1. AI对话助手
- 通用知识问答，满足学生日常学习需求
- 针对高数、物理等难点学科优化
- 支持文字和图片交互识别
- 智能理解函数图、几何图、电路图等
- 支持图像和表格生成

### 2. AI PPT生成器
- 预设学术PPT模板库
- 基于文档的PPT生成
- 支持VBA代码生成
- 单页修改和重新生成功能

### 3. AI论文助手
- 学术风格论文生成
- 联网搜索真实参考文献
- 数据表和数据图生成
- 支持Word和LaTeX格式
- LaTeX代码修改功能

### 4. AI理科作业助手
- 识别多格式作业文件(图片/DOC/PDF)
- 基于知识库生成解题思路
- 输出标准作业文档

### 5. AI痕迹消除
- 降低AI生成内容的识别特征
- 支持多种格式输出(PDF/DOC/DOCX)

## 技术栈与开发策略

### 分阶段开发策略

**阶段1: 前端Mock开发（当前阶段）**
- 前端独立开发，使用Mock数据
- 完善UI/UX设计和用户交互
- 定义完整的API接口规范

**阶段2: 后端开发**
- 基于前端需求开发真实API
- 渐进式替换Mock数据

**阶段3: 集成测试与优化**
- 前后端联调
- 性能优化和部署

### 前端技术栈
- **框架**: React 18 + Vite + TypeScript
- **UI库**: Ant Design 5.x
- **状态管理**: Zustand / Redux Toolkit
- **HTTP客户端**: Axios
- **Mock数据**: MSW (Mock Service Worker)
- **路由**: React Router v6
- **样式**: CSS Modules / Styled Components
- **图表**: ECharts / Recharts
- **数学公式**: KaTeX

### 后端技术栈（推荐方案）

**方案一：Python生态（推荐用于AI功能）**
- **框架**: FastAPI + Python 3.11+
- **数据库**: PostgreSQL + Redis + Qdrant(向量数据库)
- **ORM**: SQLAlchemy + Alembic
- **AI集成**: OpenAI API + LangChain + Transformers
- **认证**: JWT + OAuth2
- **文档**: 自动生成OpenAPI文档

**方案二：Node.js生态（前后端统一语言）**
- **框架**: Nest.js + TypeScript
- **数据库**: PostgreSQL + Redis + Prisma ORM
- **AI集成**: OpenAI SDK + LangChain.js
- **认证**: Passport.js + JWT

**方案三：现代全栈（最简单部署）**
- **框架**: Next.js 14 (App Router)
- **后端**: Supabase (数据库+认证+存储)
- **部署**: Vercel (一键部署)
- **AI**: OpenAI API

## 项目结构

```
college/
├── docs/                 # 项目文档
│   ├── API设计规范.md
│   ├── 技术架构设计.md
│   ├── 数据库设计.md
│   └── 测试规范.md
├── frontend/            # 前端React项目
│   ├── src/
│   │   ├── components/  # 可复用组件
│   │   ├── pages/       # 页面组件
│   │   ├── hooks/       # 自定义Hooks
│   │   ├── stores/      # 状态管理
│   │   ├── utils/       # 工具函数
│   │   ├── types/       # TypeScript类型定义
│   │   ├── mocks/       # Mock数据和配置
│   │   └── __tests__/   # 前端测试
│   ├── public/
│   ├── package.json
│   └── vite.config.ts
├── backend/             # 后端API项目
│   ├── src/
│   │   ├── routes/      # API路由
│   │   ├── services/    # 业务逻辑
│   │   ├── models/      # 数据模型
│   │   ├── middleware/  # 中间件
│   │   ├── utils/       # 工具函数
│   │   └── __tests__/   # 后端测试
│   ├── package.json
│   └── tsconfig.json
├── shared/              # 前后端共享代码
│   ├── types/           # 共享类型定义
│   └── constants/       # 共享常量
├── tests/               # 集成测试和E2E测试
│   ├── integration/     # 集成测试
│   ├── e2e/            # 端到端测试
│   └── utils/          # 测试工具
├── scripts/             # 构建和部署脚本
├── docker/              # Docker配置
├── .github/             # GitHub Actions CI/CD
├── package.json         # 根目录package.json
└── README.md
```

## 测试策略

### 测试金字塔

```
       ╔══════════════════╗
      ╔╝    E2E Tests      ╚╗  <- 少量，覆盖核心用户流程
     ╔╝                    ╚╗
    ╔╝   Integration Tests  ╚╗ <- 中量，测试模块间交互
   ╔╝                       ╚╗
  ╔╝      Unit Tests         ╚╗ <- 大量，快速反馈
 ╔╝                          ╚╗
╔╝                            ╚╗
╚══════════════════════════════╝
```

### 1. 单元测试 (Unit Tests)
**目标**: 测试个别函数、组件、类的功能
**覆盖率要求**: ≥80%
**工具**: Vitest + Testing Library

**测试范围**:
- React组件渲染和交互
- 自定义Hooks逻辑
- 工具函数和业务逻辑
- API服务层函数
- 数据模型验证

**命名规范**:
```
src/
├── components/
│   ├── ChatMessage.tsx
│   └── __tests__/
│       └── ChatMessage.test.tsx
└── utils/
    ├── formatters.ts
    └── __tests__/
        └── formatters.test.ts
```

**运行命令**:
```bash
# 运行所有单元测试
npm run test:unit

# 监听模式运行
npm run test:unit:watch

# 生成覆盖率报告
npm run test:unit:coverage
```

### 2. 集成测试 (Integration Tests)
**目标**: 测试模块间的交互和API端点
**工具**: Vitest + Supertest + Test Database

**测试范围**:
- API端点完整流程
- 数据库操作和事务
- 第三方服务集成（Mock）
- 前后端数据流
- 认证和权限验证

**测试环境**:
- 使用测试专用数据库
- Mock外部API（OpenAI等）
- 独立的Redis实例

**运行命令**:
```bash
# 运行集成测试
npm run test:integration

# 重置测试数据库
npm run test:db:reset
```

### 3. 端到端测试 (E2E Tests)
**目标**: 模拟真实用户操作，测试完整业务流程
**工具**: Playwright
**浏览器**: Chromium, Firefox, WebKit

**测试场景**:
- 用户注册和登录流程
- AI对话完整流程
- PPT生成和下载
- 论文写作助手使用
- 支付和订阅流程

**测试特点**:
- 无需用户交互，完全自动化
- 支持headless模式
- 自动截图和录屏
- 并行执行，提高效率

**运行命令**:
```bash
# 运行E2E测试
npm run test:e2e

# 运行特定浏览器
npm run test:e2e:chromium
npm run test:e2e:firefox

# 调试模式（有界面）
npm run test:e2e:debug
```

### 4. 性能测试
**工具**: Lighthouse CI + K6
**指标**:
- 首屏加载时间 < 2s
- 最大内容绘制 < 2.5s
- 累积布局偏移 < 0.1
- 首次输入延迟 < 100ms

### 5. 可访问性测试
**工具**: axe-core + Playwright
**标准**: WCAG 2.1 AA级别

## Mock数据开发

### MSW配置

**安装和设置**:
```bash
cd frontend
npm install --save-dev msw
npx msw init public/ --save
```

**Mock数据结构**:
```typescript
// src/mocks/handlers.ts
export const handlers = [
  // AI对话API
  rest.post('/api/chat/send', (req, res, ctx) => {
    return res(
      ctx.delay(1000), // 模拟网络延迟
      ctx.status(200),
      ctx.json({
        id: '123',
        content: 'Mock AI回复内容',
        timestamp: new Date().toISOString(),
        tokens: 50
      })
    )
  }),

  // PPT生成API
  rest.post('/api/ppt/generate', (req, res, ctx) => {
    return res(
      ctx.delay(3000),
      ctx.status(200),
      ctx.json({
        taskId: 'ppt-123',
        status: 'processing',
        downloadUrl: null
      })
    )
  }),

  // 用户认证API
  rest.post('/api/auth/login', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        token: 'mock-jwt-token',
        user: {
          id: '1',
          username: 'testuser',
          email: '<EMAIL>'
        }
      })
    )
  })
];
```

## 开发规范

### 代码规范
1. 使用ESLint + Prettier保证代码质量
2. 文件行数不超过800行，超过需要重构
3. 组件单一职责原则
4. 严格的TypeScript类型检查
5. 统一的命名规范（camelCase, PascalCase, kebab-case）

### Git规范
```bash
# 提交信息格式
type(scope): description

# 示例
feat(chat): 添加AI对话历史记录功能
fix(ppt): 修复模板加载失败问题
test(auth): 添加登录组件单元测试
docs(readme): 更新项目安装说明
```

### 测试规范
1. 所有测试必须无需用户交互
2. 测试完成后自动退出
3. 每个功能都要有对应的单元测试
4. 核心流程必须有E2E测试覆盖
5. 测试数据使用工厂模式生成

## 快速开始

### 环境要求
- Node.js 18+
- npm 9+ 或 yarn 1.22+
- Git

### 安装和启动

```bash
# 克隆项目
git clone <repository-url>
cd college

# 安装依赖
npm install

# 启动前端开发服务器（Mock模式）
cd frontend
npm run dev

# 运行所有测试
npm run test

# 运行特定类型测试
npm run test:unit       # 单元测试
npm run test:integration # 集成测试
npm run test:e2e        # 端到端测试

# 代码质量检查
npm run lint           # ESLint检查
npm run lint:fix       # 自动修复
npm run type-check     # TypeScript类型检查

# 构建项目
npm run build

# 预览构建结果
npm run preview
```

### 开发流程

1. **创建功能分支**
   ```bash
   git checkout -b feature/chat-history
   ```

2. **开发功能**
   - 先写测试（TDD）
   - 实现功能
   - 更新Mock数据

3. **运行测试**
   ```bash
   npm run test:unit
   npm run test:e2e
   ```

4. **代码检查**
   ```bash
   npm run lint
   npm run type-check
   ```

5. **提交代码**
   ```bash
   git add .
   git commit -m "feat(chat): 添加对话历史记录功能"
   ```

6. **推送和创建PR**
   ```bash
   git push origin feature/chat-history
   ```

## CI/CD流程

### GitHub Actions配置

**测试流水线** (`.github/workflows/test.yml`):
```yaml
name: Test Pipeline

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run linting
        run: npm run lint
      
      - name: Run type checking
        run: npm run type-check
      
      - name: Run unit tests
        run: npm run test:unit:coverage
      
      - name: Run integration tests
        run: npm run test:integration
      
      - name: Run E2E tests
        run: npm run test:e2e
      
      - name: Upload coverage
        uses: codecov/codecov-action@v3
```

**部署流水线** (`.github/workflows/deploy.yml`):
```yaml
name: Deploy Pipeline

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Run tests
        run: npm run test
      
      - name: Build project
        run: npm run build
      
      - name: Deploy to staging
        run: npm run deploy:staging
```

## 监控和维护

### 性能监控
- **Core Web Vitals**: 通过Lighthouse CI监控
- **错误追踪**: Sentry集成
- **用户分析**: Google Analytics 4

### 日志管理
- **前端**: 错误边界 + Sentry
- **后端**: Winston + ELK Stack
- **测试**: 详细的测试报告和覆盖率

### 依赖管理
```bash
# 检查依赖更新
npm outdated

# 安全漏洞检查
npm audit

# 自动修复漏洞
npm audit fix
```

## 贡献指南

1. 遵循项目的代码规范和提交规范
2. 提交前确保所有测试通过
3. 新功能必须包含相应的测试
4. 更新相关文档
5. Code Review通过后方可合并

## 许可证

MIT License

## 联系方式

- 项目维护者: [项目组]
- 邮箱: [联系邮箱]
- 文档地址: [文档链接] 