version: '3.8'

services:
  # MongoDB数据库
  mongodb:
    image: mongo:7.0
    container_name: college-ai-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: college_ai
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./scripts/init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js:ro
    networks:
      - college-ai-network

  # Redis缓存
  redis:
    image: redis:7.2-alpine
    container_name: college-ai-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - college-ai-network

  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: college-ai-backend
    restart: unless-stopped
    ports:
      - "3001:3001"
    environment:
      NODE_ENV: development
      PORT: 3001
      MONGODB_URI: ******************************************************************
      REDIS_URL: redis://redis:6379
      JWT_SECRET: your_jwt_secret_here
      OPENAI_API_KEY: ${OPENAI_API_KEY}
    depends_on:
      - mongodb
      - redis
    volumes:
      - ./backend:/app
      - /app/node_modules
      - ./uploads:/app/uploads
    networks:
      - college-ai-network

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: college-ai-frontend
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      REACT_APP_API_URL: http://localhost:3001/api
      REACT_APP_SOCKET_URL: http://localhost:3001
    depends_on:
      - backend
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - college-ai-network

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: college-ai-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - frontend
      - backend
    networks:
      - college-ai-network

volumes:
  mongodb_data:
  redis_data:

networks:
  college-ai-network:
    driver: bridge 